import React from 'react';
import PropTypes from 'prop-types';
import { Route, Redirect } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { UserSelector } from 'selectors';
import { logRoutePermission } from 'utils/permissionLogger';
import Can from 'utils/can';
import AccessDenied from './AccessDenied';

/**
 * A route component that checks if the user has the required permissions
 * before rendering the component.
 *
 * @param {Object} props - Component props
 * @param {string} props.path - Route path
 * @param {React.Component} props.component - Component to render if user has permission
 * @param {Object} props.permission - Required permission object with feat and act properties
 * @param {Object} props.rest - Additional props to pass to the Route component
 */
const PermissionRoute = ({ component: Component, permission, ...rest }) => {
  const profile = useSelector(UserSelector.profile());

  // Check if user has the required permission
  const hasPermission = () => {
    // If profile is not loaded yet, deny access temporarily
    if (!profile) {
      logRoutePermission(rest.path, permission, false, 'No profile loaded');
      return false;
    }

    // If no permission is required, allow access
    if (!permission) {
      logRoutePermission(rest.path, null, true, 'No permission required');
      return true;
    }

    // Use the Can utility for permission checking
    // This ensures we have a single source of truth for permission checks
    if (Can(permission.act, permission.feat)) {
      logRoutePermission(rest.path, permission, true, 'Permission granted');
      return true;
    }

    // If we get here, user doesn't have the required permission
    logRoutePermission(rest.path, permission, false, 'Permission denied');
    return false;
  };

  return (
    <Route
      {...rest}
      render={props => {
        // If user has permission, render the component
        if (hasPermission()) {
          return <Component {...props} />;
        }

        // If user is not logged in, redirect to login
        if (!profile) {
          return (
            <Redirect
              to={{
                pathname: "/login",
                state: { from: props.location }
              }}
            />
          );
        }

        // If user is logged in but doesn't have permission, show access denied
        // Check if we're trying to access a route that's similar to the current one
        // to prevent potential redirect loops
        const currentPath = props.location.pathname;
        console.log('Access denied for path:', currentPath);
        console.log('Required permission:', permission);

        // Show the AccessDenied component
        return (
          <AccessDenied
            feature={permission?.feat}
            action={permission?.act}
          />
        );
      }}
    />
  );
};

// PropTypes validation
PermissionRoute.propTypes = {
  component: PropTypes.elementType.isRequired,
  permission: PropTypes.shape({
    feat: PropTypes.string.isRequired,
    act: PropTypes.string.isRequired
  }),
  path: PropTypes.string.isRequired,
  location: PropTypes.object
};

export default PermissionRoute;
