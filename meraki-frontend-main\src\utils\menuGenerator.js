// No need to import React since we're not using JSX here
import {
  Dashboard,
  People,
  AccountBalance,
  AccountTree,
  CalendarToday,
  EventNote,
  Task,
  AccountBalanceWallet,
  AssignmentIndOutlined,
  Assignment,
  Settings,
  MoreTime,
  Speed
} from "@mui/icons-material";
import Can from "../utils/can";

// Menu item definitions with their icons and permissions
const menuDefinitions = {
  // Admin menus
  adminDashboard: {
    name: "Admin Dashboard",
    icon: Dashboard,
    path: "/app/admin-dashboard",
    act: "read_all",
    feat: "Dashboard"
  },
  employees: {
    name: "Employee Management",
    icon: People,
    path: "/app/user",
    act: "read",
    feat: "User"
  },
  department: {
    name: "Department",
    icon: AccountBalance,
    path: "/app/department",
    act: "read",
    feat: "Department"
  },
  designation: {
    name: "Designation",
    icon: AccountTree,
    path: "/app/designation",
    act: "read",
    feat: "Designation"
  },
  adminAttendance: {
    name: "Attendance",
    icon: CalendarToday,
    path: "/app/attendance",
    act: "read_all",
    feat: "Attendance"
  },
  expenses: {
    name: "Expenses",
    icon: AccountBalanceWallet,
    path: "/app/expenses",
    act: "read",
    feat: "Expense",
    children: [
      {
        name: "Create Expense",
        path: "/app/expenses/create",
        act: "create",
        feat: "Expense"
      }
    ]
  },
  leaveManagement: {
    name: "Leave Management",
    icon: AssignmentIndOutlined,
    path: "/app/leave",
    act: "read_all", // Specifically requires read_all permission
    feat: "Leave",
    children: [
      {
        name: "Leave Report",
        path: "/app/leave/report",
        act: "read",
        feat: "Leave Report"
      },
      {
        name: "Approval",
        path: "/app/leave/approval",
        act: "read",
        feat: "Approve"
      },
      {
        name: "Calendar",
        path: "/app/leave/calendar",
        act: "read",
        feat: "Calendar"
      },
      {
        name: "Configuration",
        path: "/app/leave/configuration",
        act: "read",
        feat: "Configuration"
      }
    ]
  },
  projects: {
    name: "Projects",
    icon: Task,
    path: "/app/project/list",
    act: "read_all",
    feat: "Project",
    children: [
      {
        name: "Project Overview",
        path: "/app/project/overview",
        act: "read",
        feat: "Project Overview"
      },
      {
        name: "Project List",
        path: "/app/project/list",
        act: "read",
        feat: "Project List"
      },
      {
        name: "Project Timesheet",
        path: "/app/project/timesheet",
        act: "read",
        feat: "Project Timesheet"
      }
    ]
  },
  sprints: {
    name: "Sprints",
    icon: Speed,
    path: "/app/sprint",
    act: "read_all",
    feat: "Sprint"
  },
  timeline: {
    name: "Timeline",
    icon: MoreTime,
    path: "/app/timeline",
    act: "read_all",
    feat: "Timeline",
    children: [
      {
        name: "Overview",
        path: "/app/timeline/overview",
        act: "read",
        feat: "Overview"
      },
      {
        name: "Time Request",
        path: "/app/timeline/request",
        act: "read",
        feat: "Time Request"
      },
      {
        name: "Task Request",
        path: "/app/timeline/taskrequest",
        act: "read",
        feat: "Task Request"
      },
      {
        name: "Work Schedule",
        path: "/app/timeline/workschedule",
        act: "read",
        feat: "Work Schedule"
      }
    ]
  },
  client: {
    name: "Client",
    icon: AccountBalance,
    path: "/app/client",
    act: "read",
    feat: "Client"
  },
  report: {
    name: "Report",
    icon: Assignment,
    path: "/app/report",
    act: "read",
    feat: "Report"
  },
  setting: {
    name: "Setting",
    icon: Settings,
    path: "/app/setting",
    act: "read",
    feat: "Setting"
  },

  // User menus
  userDashboard: {
    name: "My Dashboard",
    icon: Dashboard,
    path: "/app/user-dashboard",
    act: "read", // Regular users only need read permission
    feat: "Dashboard"
  },
  userAttendance: {
    name: "My Attendance",
    icon: CalendarToday,
    path: "/app/attendance",
    act: "read",
    feat: "Attendance"
  },
  userLeave: {
    name: "My Leave",
    icon: AssignmentIndOutlined,
    path: "/app/user/leave",
    act: "read", // Regular read permission (not read_all)
    feat: "Leave"
  },
  userProjects: {
    name: "My Projects",
    icon: EventNote,
    path: '/app/user/projects',
    act: "read",
    feat: "Project"
  },
  userSprints: {
    name: "My Sprints",
    icon: Speed,
    path: "/app/user/sprint",
    act: "read_self",
    feat: "Sprint"
  },
  userTasks: {
    name: "My Tasks",
    icon: Task,
    path: "/app/tasks",
    act: "read",
    feat: "Tasks"
  },
  userTimeline: {
    name: "My Timeline",
    icon: MoreTime,
    path: "/app/user/timeline",
    act: "read",
    feat: "Timeline"
  }
};

/**
 * Generate menus based on user profile and permissions
 * @param {Object} profile - User profile with role and permissions
 * @returns {Array} Array of menu items the user has access to
 */
export function generateMenus(profile) {
  if (!profile) {
    return [];
  }

  // Check if user is admin
  const isAdmin = profile.role && profile.role.includes('admin');

  const menus = [];

  // Define menu groups
  const adminMenuKeys = [
    'adminDashboard', 'employees', 'department', 'designation',
    'adminAttendance', 'expenses', 'leaveManagement', 'projects',
    'sprints', 'timeline', 'client', 'report', 'setting'
  ];

  const userMenuKeys = [
    'userDashboard', 'userAttendance', 'userLeave',
    'userProjects', 'userSprints', 'userTasks', 'userTimeline'
  ];

  // Helper function to check if user has specific permission type
  const hasSpecificPermission = (feat, action) => {
    if (!profile.permissions) {
      return false;
    }

    // Special handling for Project/Projects
    if (feat === 'Project' || feat === 'Projects') {
      return profile.permissions.some(p =>
        (p.feat === 'Project' || p.feat === 'Projects') &&
        p.acts &&
        p.acts.includes(action)
      );
    }

    // Check if user has the specific permission
    return profile.permissions.some(p => {
      return p.feat === feat && p.acts && p.acts.includes(action);
    });
  };

  // Determine which menus to show based on permissions
  const menuKeysToProcess = [];

  // Process admin menus
  adminMenuKeys.forEach(key => {
    const menuDef = menuDefinitions[key];
    if (!menuDef) {
      return;
    }

    // Special case for dashboard menus based on role
    if (key === 'adminDashboard') {
      // Only show admin dashboard to admin users
      if (isAdmin) {
        menuKeysToProcess.push(key);
      }
      return;
    }

    // Admin menus typically require read_all permission, but some only need read
    // Special cases for menus that only need read permission
    if (hasSpecificPermission(menuDef.feat, 'read_all') ||
        (isAdmin && menuDef.feat === 'User') ||
        // Special case for Department and Designation - they only need read permission
        (menuDef.act === 'read' && hasSpecificPermission(menuDef.feat, 'read'))) {
      menuKeysToProcess.push(key);
    }
  });

  // Process user menus
  userMenuKeys.forEach(key => {
    const menuDef = menuDefinitions[key];
    if (!menuDef) {
      return;
    }

    // Special case for dashboard menus based on role
    if (key === 'userDashboard') {
      // Only show user dashboard to non-admin users
      if (!isAdmin) {
        menuKeysToProcess.push(key);
      }
      return;
    }

    // User menus require read permission
    // We'll show user menus if they have any permission for the feature
    const hasAnyPermission = hasSpecificPermission(menuDef.feat, 'read') ||
                            hasSpecificPermission(menuDef.feat, 'read_self') ||
                            hasSpecificPermission(menuDef.feat, 'create') ||
                            hasSpecificPermission(menuDef.feat, 'update') ||
                            hasSpecificPermission(menuDef.feat, 'delete');

    // Show user menu if user has some permission for this feature
    // This allows users to see both admin and user menus if they have both permissions
    if (hasAnyPermission) {
      menuKeysToProcess.push(key);
    }
  });

  // Process menu definitions
  menuKeysToProcess.forEach(key => {
    const menuDef = menuDefinitions[key];
    if (!menuDef) {
      return;
    }

    const { name, icon, path, act, feat, children } = menuDef;

    // Use the Can utility for permission checking
    const hasPermission = Can(act, feat);

    if (hasPermission) {

      // Create menu item
      const menuItem = {
        name,
        icon,
        path,
        act,
        feat
      };

      // Process children if any
      if (children && Array.isArray(children)) {
        // For admin menus with read_all permission
        if (act === 'read_all') {
          // Grant access to all child menus
          menuItem.children = children.map(child => ({
            ...child,
            // Keep original permission for logging purposes
            originalAct: child.act,
            originalFeat: child.feat,
            // But always allow access
            hasPermission: true
          }));

          // All child menus of admin menus with read_all permission are automatically granted permission
        } else {
          // For regular menus, check permissions with inheritance
          const permittedChildren = children.filter(child => {
            // Use the Can utility for permission checking
            // This will use our enhanced permission inheritance logic
            return Can(child.act, child.feat);
          });

          if (permittedChildren.length > 0) {
            menuItem.children = permittedChildren;
          }
        }
      }

      menus.push(menuItem);
    }
  });

  // Sort menus to ensure dashboard is always first
  // Then admin menus, then user menus
  const sortedMenus = [...menus].sort((a, b) => {
    // Dashboards first
    if (a.path.includes('dashboard') && !b.path.includes('dashboard')) {
      return -1;
    }
    if (!a.path.includes('dashboard') && b.path.includes('dashboard')) {
      return 1;
    }

    // Admin dashboard before user dashboard
    if (a.path.includes('admin-dashboard') && b.path.includes('user-dashboard')) {
      return -1;
    }
    if (a.path.includes('user-dashboard') && b.path.includes('admin-dashboard')) {
      return 1;
    }

    // Admin menus before user menus (user menus have "My" in the name)
    if (!a.name.startsWith('My') && b.name.startsWith('My')) {
      return -1;
    }
    if (a.name.startsWith('My') && !b.name.startsWith('My')) {
      return 1;
    }

    // Alphabetical order for the rest
    return a.name.localeCompare(b.name);
  });

  return sortedMenus;
}

export default generateMenus;