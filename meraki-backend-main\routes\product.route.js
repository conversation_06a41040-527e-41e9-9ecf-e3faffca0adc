    'use strict';

    const express = require('express');
    const router = express.Router();
    const multer  = require('multer')().none();
    const product = require('../controllers/product.controller');

    module.exports = (app) => {
        router.get("/", product.getProducts);
        router.post("/create", product.createProduct);
        router.patch("/update/:id",multer,product.updateProduct)
        router.delete("/delete/:id",product.deleteProduct)
        
        // Add specific routes before the generic /:id route
        router.get("/user/:id",product.getProductsByUser)
        
        // Add a route for /sprints to handle that specific path
        router.get("/sprints", (req, res) => {
            res.redirect('/api/sprint');
        });
        
        router.get("/:id",product.getProductById),
        router.patch("/create/task/:id",multer,product.createTask),
        router.patch("/update/task/:productId/:taskId", multer, product.updateTask);
        router.patch("/start-task/:productId/:taskId", product.startTask);
        router.patch("/stop-task/:productId/:taskId", product.submitTask);
        router.patch("/pause-task/:productId/:taskId",product.pauseTask)
       

        app.use("/api/product", router);
    };