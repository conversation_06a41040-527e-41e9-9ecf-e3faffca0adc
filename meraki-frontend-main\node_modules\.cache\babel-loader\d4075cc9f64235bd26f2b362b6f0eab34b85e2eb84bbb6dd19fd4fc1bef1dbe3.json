{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\MonthWorkScheduleForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Typography, MenuItem, Box } from '@mui/material';\nimport { useFormik } from 'formik';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport Input from 'components/Input';\nimport SelectField from 'components/SelectField';\nimport { UserActions, GeneralActions } from 'slices/actions';\nimport { GeneralSelector, UserSelector } from 'selectors';\nimport { SCHEDULE_TEMPLATES, SCHEDULE_TYPES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\nimport AdvancedWorkScheduleForm from './AdvancedWorkScheduleForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthWorkScheduleForm = ({\n  open,\n  onClose,\n  selectedUser,\n  selectedDate\n}) => {\n  _s();\n  var _currentSelectedUser$, _currentSelectedUser$2, _currentSelectedUser$3, _currentSelectedUser$4, _currentSelectedUser$5, _currentSelectedUser$6;\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [currentSelectedUser, setCurrentSelectedUser] = useState(selectedUser);\n  const [showAdvancedForm, setShowAdvancedForm] = useState(false);\n  useEffect(() => {\n    if (success) {\n      toast.success(`Work schedule updated successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      onClose();\n    }\n  }, [success, onClose]);\n  useEffect(() => {\n    setCurrentSelectedUser(selectedUser);\n  }, [selectedUser]);\n\n  // Helper function to format date for input field\n  const formatDateForInput = dateValue => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0];\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n  const formik = useFormik({\n    initialValues: {\n      selectedUserId: (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : currentSelectedUser._id) || (users.length > 0 ? users[0]._id : ''),\n      scheduleTemplate: (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$ = currentSelectedUser.workSchedule) === null || _currentSelectedUser$ === void 0 ? void 0 : _currentSelectedUser$.scheduleTemplate) || DEFAULT_WORK_SCHEDULE.scheduleTemplate,\n      shiftStart: formatDateForInput(selectedDate || (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$2 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$2 === void 0 ? void 0 : _currentSelectedUser$2.shiftStart)),\n      shiftEnd: formatDateForInput(selectedDate || (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$3 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$3 === void 0 ? void 0 : _currentSelectedUser$3.shiftEnd)),\n      startTime: (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$4 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$4 === void 0 ? void 0 : _currentSelectedUser$4.startTime) || DEFAULT_WORK_SCHEDULE.startTime,\n      endTime: (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$5 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$5 === void 0 ? void 0 : _currentSelectedUser$5.endTime) || DEFAULT_WORK_SCHEDULE.endTime,\n      minimumHours: (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$6 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$6 === void 0 ? void 0 : _currentSelectedUser$6.minimumHours) || DEFAULT_WORK_SCHEDULE.minimumHours,\n      workPlan: ''\n    },\n    enableReinitialize: true,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  const handleSubmit = values => {\n    const targetUser = users.find(u => u._id === values.selectedUserId);\n    if (!targetUser) {\n      toast.error('Please select a valid user');\n      return;\n    }\n    const params = {\n      id: targetUser._id,\n      workSchedule: {\n        scheduleTemplate: values.scheduleTemplate,\n        shiftStart: values.shiftStart,\n        shiftEnd: values.shiftEnd,\n        startTime: values.startTime,\n        endTime: values.endTime,\n        minimumHours: parseFloat(values.minimumHours)\n      }\n    };\n    dispatch(UserActions.updateUser(params));\n  };\n\n  // Helper function to calculate hours between two times\n  const calculateHours = (startTime, endTime) => {\n    const [startHour, startMin] = startTime.split(':').map(Number);\n    const [endHour, endMin] = endTime.split(':').map(Number);\n    let startMinutes = startHour * 60 + startMin;\n    let endMinutes = endHour * 60 + endMin;\n\n    // Handle overnight shifts (night shift)\n    if (endMinutes <= startMinutes) {\n      endMinutes += 24 * 60; // Add 24 hours\n    }\n    const diffMinutes = endMinutes - startMinutes;\n    return (diffMinutes / 60).toFixed(2);\n  };\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(field, value);\n\n    // If user selection changes, update the form with that user's current schedule\n    if (field === 'selectedUserId') {\n      const selectedUser = users.find(u => u._id === value);\n      if (selectedUser) {\n        var _selectedUser$workSch, _selectedUser$workSch2, _selectedUser$workSch3, _selectedUser$workSch4;\n        setCurrentSelectedUser(selectedUser);\n        formik.setFieldValue('scheduleTemplate', ((_selectedUser$workSch = selectedUser.workSchedule) === null || _selectedUser$workSch === void 0 ? void 0 : _selectedUser$workSch.scheduleTemplate) || DEFAULT_WORK_SCHEDULE.scheduleTemplate);\n        formik.setFieldValue('startTime', ((_selectedUser$workSch2 = selectedUser.workSchedule) === null || _selectedUser$workSch2 === void 0 ? void 0 : _selectedUser$workSch2.startTime) || DEFAULT_WORK_SCHEDULE.startTime);\n        formik.setFieldValue('endTime', ((_selectedUser$workSch3 = selectedUser.workSchedule) === null || _selectedUser$workSch3 === void 0 ? void 0 : _selectedUser$workSch3.endTime) || DEFAULT_WORK_SCHEDULE.endTime);\n        formik.setFieldValue('minimumHours', ((_selectedUser$workSch4 = selectedUser.workSchedule) === null || _selectedUser$workSch4 === void 0 ? void 0 : _selectedUser$workSch4.minimumHours) || DEFAULT_WORK_SCHEDULE.minimumHours);\n      }\n    }\n\n    // Auto-calculate hours when start or end time changes\n    if (field === 'startTime' || field === 'endTime') {\n      const startTime = field === 'startTime' ? value : formik.values.startTime;\n      const endTime = field === 'endTime' ? value : formik.values.endTime;\n      if (startTime && endTime) {\n        const calculatedHours = calculateHours(startTime, endTime);\n        formik.setFieldValue('minimumHours', calculatedHours);\n      }\n    }\n\n    // Auto-suggest schedule template based on time\n    if (field === 'startTime') {\n      const hour = parseInt(value.split(':')[0], 10);\n      if (hour >= 22 || hour < 6) {\n        formik.setFieldValue('scheduleTemplate', 'night_shift');\n      } else {\n        formik.setFieldValue('scheduleTemplate', 'day_shift');\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Month Work Schedule\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: [\"Date: \", selectedDate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : currentSelectedUser.workSchedule) && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"primary\",\n        sx: {\n          display: 'block',\n          mt: 1\n        },\n        children: [\"Current (\", currentSelectedUser.name, \"): \", currentSelectedUser.workSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift', \"(\", currentSelectedUser.workSchedule.startTime, \"-\", currentSelectedUser.workSchedule.endTime, \",\", currentSelectedUser.workSchedule.minimumHours, \"h min)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: formik.handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Select User\",\n                name: \"selectedUserId\",\n                value: formik.values.selectedUserId,\n                onChange: e => handleWorkScheduleChange('selectedUserId', e.target.value),\n                required: true,\n                children: users.map(user => {\n                  var _user$designation;\n                  return /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: user._id,\n                    children: [user.name, \" - \", ((_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation.name) || user.role || 'No Role', user.workSchedule && (user.workSchedule.startTime !== '09:00' || user.workSchedule.endTime !== '17:30' || user.workSchedule.scheduleTemplate !== 'day_shift') && /*#__PURE__*/_jsxDEV(Typography, {\n                      component: \"span\",\n                      variant: \"caption\",\n                      color: \"primary\",\n                      sx: {\n                        ml: 1\n                      },\n                      children: [\"(\", user.workSchedule.startTime, \"-\", user.workSchedule.endTime, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 25\n                    }, this)]\n                  }, user._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Schedule Template\",\n                name: \"scheduleTemplate\",\n                value: formik.values.scheduleTemplate,\n                onChange: e => handleWorkScheduleChange('scheduleTemplate', e.target.value),\n                required: true,\n                children: SCHEDULE_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: template.value,\n                  children: template.label\n                }, template.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Minimum Hours\",\n                name: \"minimumHours\",\n                type: \"number\",\n                step: \"0.1\",\n                value: formik.values.minimumHours,\n                onChange: e => handleWorkScheduleChange('minimumHours', e.target.value),\n                required: true,\n                helperText: formik.values.startTime && formik.values.endTime ? `Calculated: ${calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Enter start and end time to auto-calculate'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Shift Start Date\",\n                name: \"shiftStart\",\n                type: \"date\",\n                value: formik.values.shiftStart,\n                onChange: e => handleWorkScheduleChange('shiftStart', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Shift End Date\",\n                name: \"shiftEnd\",\n                type: \"date\",\n                value: formik.values.shiftEnd,\n                onChange: e => handleWorkScheduleChange('shiftEnd', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Start Time\",\n                name: \"startTime\",\n                value: formik.values.startTime,\n                onChange: e => handleWorkScheduleChange('startTime', e.target.value),\n                required: true,\n                children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"End Time\",\n                name: \"endTime\",\n                value: formik.values.endTime,\n                onChange: e => handleWorkScheduleChange('endTime', e.target.value),\n                required: true,\n                children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Work Plan or Notes\",\n                name: \"workPlan\",\n                multiline: true,\n                rows: 4,\n                value: formik.values.workPlan,\n                onChange: e => handleWorkScheduleChange('workPlan', e.target.value),\n                placeholder: \"Enter work plan, goals, or any specific notes for this month...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 2,\n                  bgcolor: 'info.light',\n                  borderRadius: 1,\n                  border: '1px solid',\n                  borderColor: 'info.main',\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"info.dark\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Note:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 3\n                  }, this), \" This updates the user\\u2019s default schedule. For specific dates/times, use Advanced Scheduling.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  size: \"small\",\n                  onClick: () => setShowAdvancedForm(true),\n                  children: \"Advanced\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        color: \"secondary\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: formik.handleSubmit,\n        variant: \"contained\",\n        color: \"primary\",\n        children: \"Save Schedule\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AdvancedWorkScheduleForm, {\n      open: showAdvancedForm,\n      onClose: () => setShowAdvancedForm(false),\n      selectedUser: currentSelectedUser,\n      selectedDate: selectedDate,\n      scheduleType: \"daily\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 5\n  }, this);\n};\n_s(MonthWorkScheduleForm, \"barKOlzO0vzcc3xvuU06hzU+nvM=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useFormik];\n});\n_c = MonthWorkScheduleForm;\nMonthWorkScheduleForm.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string\n};\nexport default MonthWorkScheduleForm;\nvar _c;\n$RefreshReg$(_c, \"MonthWorkScheduleForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Grid", "Typography", "MenuItem", "Box", "useFormik", "useDispatch", "useSelector", "toast", "PropTypes", "Input", "SelectField", "UserActions", "GeneralActions", "GeneralSelector", "UserSelector", "SCHEDULE_TEMPLATES", "SCHEDULE_TYPES", "DEFAULT_WORK_SCHEDULE", "TIME_OPTIONS", "AdvancedWorkScheduleForm", "jsxDEV", "_jsxDEV", "MonthWorkScheduleForm", "open", "onClose", "selected<PERSON>ser", "selectedDate", "_s", "_currentSelectedUser$", "_currentSelectedUser$2", "_currentSelectedUser$3", "_currentSelectedUser$4", "_currentSelectedUser$5", "_currentSelectedUser$6", "dispatch", "users", "getUsers", "success", "updateUser", "type", "currentSelectedUser", "setCurrentSelectedUser", "showAdvancedForm", "setShowAdvancedForm", "position", "autoClose", "closeOnClick", "formatDateForInput", "dateValue", "Date", "toISOString", "split", "formik", "initialValues", "selectedUserId", "_id", "length", "scheduleTemplate", "workSchedule", "shiftStart", "shiftEnd", "startTime", "endTime", "minimumHours", "workPlan", "enableReinitialize", "onSubmit", "values", "handleSubmit", "targetUser", "find", "u", "error", "params", "id", "parseFloat", "calculateHours", "startHour", "startMin", "map", "Number", "endHour", "endMin", "startMinutes", "endMinutes", "diffMinutes", "toFixed", "handleWorkScheduleChange", "field", "value", "setFieldValue", "_selectedUser$workSch", "_selectedUser$workSch2", "_selectedUser$workSch3", "_selectedUser$workSch4", "calculatedHours", "hour", "parseInt", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "sx", "display", "mt", "name", "container", "spacing", "item", "xs", "label", "onChange", "e", "target", "required", "user", "_user$designation", "designation", "role", "component", "ml", "sm", "template", "step", "helperText", "option", "multiline", "rows", "placeholder", "p", "bgcolor", "borderRadius", "border", "borderColor", "justifyContent", "alignItems", "size", "onClick", "scheduleType", "_c", "propTypes", "bool", "isRequired", "func", "object", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/MonthWorkScheduleForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>alog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Grid,\n  Typography,\n  MenuItem,\n  Box\n} from '@mui/material';\nimport { useFormik } from 'formik';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport Input from 'components/Input';\nimport SelectField from 'components/SelectField';\nimport { UserActions, GeneralActions } from 'slices/actions';\nimport { GeneralSelector, UserSelector } from 'selectors';\nimport { SCHEDULE_TEMPLATES, SCHEDULE_TYPES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\nimport AdvancedWorkScheduleForm from './AdvancedWorkScheduleForm';\n\nconst MonthWorkScheduleForm = ({ open, onClose, selectedUser, selectedDate }) => {\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [currentSelectedUser, setCurrentSelectedUser] = useState(selectedUser);\n  const [showAdvancedForm, setShowAdvancedForm] = useState(false);\n\n  useEffect(() => {\n    if (success) {\n      toast.success(`Work schedule updated successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true,\n      });\n      onClose();\n    }\n  }, [success, onClose]);\n\n  useEffect(() => {\n    setCurrentSelectedUser(selectedUser);\n  }, [selectedUser]);\n\n  // Helper function to format date for input field\n  const formatDateForInput = (dateValue) => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0];\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n\n  const formik = useFormik({\n    initialValues: {\n      selectedUserId: currentSelectedUser?._id || (users.length > 0 ? users[0]._id : ''),\n      scheduleTemplate: currentSelectedUser?.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate,\n      shiftStart: formatDateForInput(selectedDate || currentSelectedUser?.workSchedule?.shiftStart),\n      shiftEnd: formatDateForInput(selectedDate || currentSelectedUser?.workSchedule?.shiftEnd),\n      startTime: currentSelectedUser?.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime,\n      endTime: currentSelectedUser?.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime,\n      minimumHours: currentSelectedUser?.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours,\n      workPlan: ''\n    },\n    enableReinitialize: true,\n    onSubmit: (values) => {\n      handleSubmit(values);\n    }\n  });\n\n  const handleSubmit = (values) => {\n    const targetUser = users.find(u => u._id === values.selectedUserId);\n    if (!targetUser) {\n      toast.error('Please select a valid user');\n      return;\n    }\n\n    const params = {\n      id: targetUser._id,\n      workSchedule: {\n        scheduleTemplate: values.scheduleTemplate,\n        shiftStart: values.shiftStart,\n        shiftEnd: values.shiftEnd,\n        startTime: values.startTime,\n        endTime: values.endTime,\n        minimumHours: parseFloat(values.minimumHours)\n      }\n    };\n\n    dispatch(UserActions.updateUser(params));\n  };\n\n  // Helper function to calculate hours between two times\n  const calculateHours = (startTime, endTime) => {\n    const [startHour, startMin] = startTime.split(':').map(Number);\n    const [endHour, endMin] = endTime.split(':').map(Number);\n\n    let startMinutes = (startHour * 60) + startMin;\n    let endMinutes = (endHour * 60) + endMin;\n\n    // Handle overnight shifts (night shift)\n    if (endMinutes <= startMinutes) {\n      endMinutes += (24 * 60); // Add 24 hours\n    }\n\n    const diffMinutes = endMinutes - startMinutes;\n    return (diffMinutes / 60).toFixed(2);\n  };\n\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(field, value);\n\n    // If user selection changes, update the form with that user's current schedule\n    if (field === 'selectedUserId') {\n      const selectedUser = users.find(u => u._id === value);\n      if (selectedUser) {\n        setCurrentSelectedUser(selectedUser);\n        formik.setFieldValue('scheduleTemplate', selectedUser.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate);\n        formik.setFieldValue('startTime', selectedUser.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime);\n        formik.setFieldValue('endTime', selectedUser.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime);\n        formik.setFieldValue('minimumHours', selectedUser.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours);\n      }\n    }\n\n    // Auto-calculate hours when start or end time changes\n    if (field === 'startTime' || field === 'endTime') {\n      const startTime = field === 'startTime' ? value : formik.values.startTime;\n      const endTime = field === 'endTime' ? value : formik.values.endTime;\n\n      if (startTime && endTime) {\n        const calculatedHours = calculateHours(startTime, endTime);\n        formik.setFieldValue('minimumHours', calculatedHours);\n      }\n    }\n\n    // Auto-suggest schedule template based on time\n    if (field === 'startTime') {\n      const hour = parseInt(value.split(':')[0], 10);\n      if (hour >= 22 || hour < 6) {\n        formik.setFieldValue('scheduleTemplate', 'night_shift');\n      } else {\n        formik.setFieldValue('scheduleTemplate', 'day_shift');\n      }\n    }\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        <Typography variant=\"h6\">\n          Month Work Schedule\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Date: {selectedDate}\n        </Typography>\n        {currentSelectedUser?.workSchedule && (\n          <Typography variant=\"caption\" color=\"primary\" sx={{ display: 'block', mt: 1 }}>\n            Current ({currentSelectedUser.name}): {currentSelectedUser.workSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}\n            ({currentSelectedUser.workSchedule.startTime}-{currentSelectedUser.workSchedule.endTime},\n            {currentSelectedUser.workSchedule.minimumHours}h min)\n          </Typography>\n        )}\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ mt: 2 }}>\n          <form onSubmit={formik.handleSubmit}>\n            <Grid container spacing={2}>\n              {/* User Selection Dropdown */}\n              <Grid item xs={12}>\n                <SelectField\n                  label=\"Select User\"\n                  name=\"selectedUserId\"\n                  value={formik.values.selectedUserId}\n                  onChange={(e) => handleWorkScheduleChange('selectedUserId', e.target.value)}\n                  required\n                >\n                  {users.map((user) => (\n                    <MenuItem key={user._id} value={user._id}>\n                      {user.name} - {user.designation?.name || user.role || 'No Role'}\n                      {user.workSchedule && (\n                        user.workSchedule.startTime !== '09:00' ||\n                        user.workSchedule.endTime !== '17:30' ||\n                        user.workSchedule.scheduleTemplate !== 'day_shift'\n                      ) && (\n                        <Typography component=\"span\" variant=\"caption\" color=\"primary\" sx={{ ml: 1 }}>\n                          ({user.workSchedule.startTime}-{user.workSchedule.endTime})\n                        </Typography>\n                      )}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <SelectField\n                  label=\"Schedule Template\"\n                  name=\"scheduleTemplate\"\n                  value={formik.values.scheduleTemplate}\n                  onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}\n                  required\n                >\n                  {SCHEDULE_TEMPLATES.map((template) => (\n                    <MenuItem key={template.value} value={template.value}>\n                      {template.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <Input\n                  label=\"Minimum Hours\"\n                  name=\"minimumHours\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  value={formik.values.minimumHours}\n                  onChange={(e) => handleWorkScheduleChange('minimumHours', e.target.value)}\n                  required\n                  helperText={\n                    formik.values.startTime && formik.values.endTime ? `Calculated: ${calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Enter start and end time to auto-calculate'\n                  }\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <Input\n                  label=\"Shift Start Date\"\n                  name=\"shiftStart\"\n                  type=\"date\"\n                  value={formik.values.shiftStart}\n                  onChange={(e) => handleWorkScheduleChange('shiftStart', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <Input\n                  label=\"Shift End Date\"\n                  name=\"shiftEnd\"\n                  type=\"date\"\n                  value={formik.values.shiftEnd}\n                  onChange={(e) => handleWorkScheduleChange('shiftEnd', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <SelectField\n                  label=\"Start Time\"\n                  name=\"startTime\"\n                  value={formik.values.startTime}\n                  onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}\n                  required\n                >\n                  {TIME_OPTIONS.map((option) => (\n                    <MenuItem key={option.value} value={option.value}>\n                      {option.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <SelectField\n                  label=\"End Time\"\n                  name=\"endTime\"\n                  value={formik.values.endTime}\n                  onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}\n                  required\n                >\n                  {TIME_OPTIONS.map((option) => (\n                    <MenuItem key={option.value} value={option.value}>\n                      {option.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12}>\n                <Input\n                  label=\"Work Plan or Notes\"\n                  name=\"workPlan\"\n                  multiline\n                  rows={4}\n                  value={formik.values.workPlan}\n                  onChange={(e) => handleWorkScheduleChange('workPlan', e.target.value)}\n                  placeholder=\"Enter work plan, goals, or any specific notes for this month...\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Box sx={{\n                  p: 2,\n                  bgcolor: 'info.light',\n                  borderRadius: 1,\n                  border: '1px solid',\n                  borderColor: 'info.main',\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                }}>\n          <Typography variant=\"body2\" color=\"info.dark\">\n  <strong>Note:</strong> This updates the user&rsquo;s default schedule.\n  For specific dates/times, use Advanced Scheduling.\n</Typography>\n\n                  <Button\n                    variant=\"outlined\"\n                    size=\"small\"\n                    onClick={() => setShowAdvancedForm(true)}\n                  >\n                    Advanced\n                  </Button>\n                </Box>\n              </Grid>\n            </Grid>\n          </form>\n        </Box>\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={onClose} color=\"secondary\">\n          Cancel\n        </Button>\n        <Button \n          onClick={formik.handleSubmit} \n          variant=\"contained\" \n          color=\"primary\"\n        >\n          Save Schedule\n        </Button>\n      </DialogActions>\n\n      {/* Advanced Work Schedule Form */}\n      <AdvancedWorkScheduleForm\n        open={showAdvancedForm}\n        onClose={() => setShowAdvancedForm(false)}\n        selectedUser={currentSelectedUser}\n        selectedDate={selectedDate}\n        scheduleType=\"daily\"\n      />\n    </Dialog>\n  );\n};\n\nMonthWorkScheduleForm.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string\n};\n\nexport default MonthWorkScheduleForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,GAAG,QACE,eAAe;AACtB,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAC5D,SAASC,eAAe,EAAEC,YAAY,QAAQ,WAAW;AACzD,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,wBAAwB;AAChH,OAAOC,wBAAwB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC/E,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,KAAK,GAAG7B,WAAW,CAACQ,YAAY,CAACsB,QAAQ,CAAC,CAAC,CAAC;EAClD,MAAMC,OAAO,GAAG/B,WAAW,CAACO,eAAe,CAACwB,OAAO,CAAC1B,WAAW,CAAC2B,UAAU,CAACC,IAAI,CAAC,CAAC;EACjF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhD,QAAQ,CAACgC,YAAY,CAAC;EAC5E,MAAM,CAACiB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAE/DC,SAAS,CAAC,MAAM;IACd,IAAI2C,OAAO,EAAE;MACX9B,KAAK,CAAC8B,OAAO,CAAC,qCAAqC,EAAE;QACnDO,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFtB,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACa,OAAO,EAAEb,OAAO,CAAC,CAAC;EAEtB9B,SAAS,CAAC,MAAM;IACd+C,sBAAsB,CAAChB,YAAY,CAAC;EACtC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMsB,kBAAkB,GAAIC,SAAS,IAAK;IACxC,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C;IACA,IAAI,OAAOH,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAOA,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAO,IAAIF,IAAI,CAACD,SAAS,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,MAAM,GAAGhD,SAAS,CAAC;IACvBiD,aAAa,EAAE;MACbC,cAAc,EAAE,CAAAd,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEe,GAAG,MAAKpB,KAAK,CAACqB,MAAM,GAAG,CAAC,GAAGrB,KAAK,CAAC,CAAC,CAAC,CAACoB,GAAG,GAAG,EAAE,CAAC;MAClFE,gBAAgB,EAAE,CAAAjB,mBAAmB,aAAnBA,mBAAmB,wBAAAZ,qBAAA,GAAnBY,mBAAmB,CAAEkB,YAAY,cAAA9B,qBAAA,uBAAjCA,qBAAA,CAAmC6B,gBAAgB,KAAIxC,qBAAqB,CAACwC,gBAAgB;MAC/GE,UAAU,EAAEZ,kBAAkB,CAACrB,YAAY,KAAIc,mBAAmB,aAAnBA,mBAAmB,wBAAAX,sBAAA,GAAnBW,mBAAmB,CAAEkB,YAAY,cAAA7B,sBAAA,uBAAjCA,sBAAA,CAAmC8B,UAAU,EAAC;MAC7FC,QAAQ,EAAEb,kBAAkB,CAACrB,YAAY,KAAIc,mBAAmB,aAAnBA,mBAAmB,wBAAAV,sBAAA,GAAnBU,mBAAmB,CAAEkB,YAAY,cAAA5B,sBAAA,uBAAjCA,sBAAA,CAAmC8B,QAAQ,EAAC;MACzFC,SAAS,EAAE,CAAArB,mBAAmB,aAAnBA,mBAAmB,wBAAAT,sBAAA,GAAnBS,mBAAmB,CAAEkB,YAAY,cAAA3B,sBAAA,uBAAjCA,sBAAA,CAAmC8B,SAAS,KAAI5C,qBAAqB,CAAC4C,SAAS;MAC1FC,OAAO,EAAE,CAAAtB,mBAAmB,aAAnBA,mBAAmB,wBAAAR,sBAAA,GAAnBQ,mBAAmB,CAAEkB,YAAY,cAAA1B,sBAAA,uBAAjCA,sBAAA,CAAmC8B,OAAO,KAAI7C,qBAAqB,CAAC6C,OAAO;MACpFC,YAAY,EAAE,CAAAvB,mBAAmB,aAAnBA,mBAAmB,wBAAAP,sBAAA,GAAnBO,mBAAmB,CAAEkB,YAAY,cAAAzB,sBAAA,uBAAjCA,sBAAA,CAAmC8B,YAAY,KAAI9C,qBAAqB,CAAC8C,YAAY;MACnGC,QAAQ,EAAE;IACZ,CAAC;IACDC,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAGC,MAAM,IAAK;MACpBC,YAAY,CAACD,MAAM,CAAC;IACtB;EACF,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAID,MAAM,IAAK;IAC/B,MAAME,UAAU,GAAGlC,KAAK,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChB,GAAG,KAAKY,MAAM,CAACb,cAAc,CAAC;IACnE,IAAI,CAACe,UAAU,EAAE;MACf9D,KAAK,CAACiE,KAAK,CAAC,4BAA4B,CAAC;MACzC;IACF;IAEA,MAAMC,MAAM,GAAG;MACbC,EAAE,EAAEL,UAAU,CAACd,GAAG;MAClBG,YAAY,EAAE;QACZD,gBAAgB,EAAEU,MAAM,CAACV,gBAAgB;QACzCE,UAAU,EAAEQ,MAAM,CAACR,UAAU;QAC7BC,QAAQ,EAAEO,MAAM,CAACP,QAAQ;QACzBC,SAAS,EAAEM,MAAM,CAACN,SAAS;QAC3BC,OAAO,EAAEK,MAAM,CAACL,OAAO;QACvBC,YAAY,EAAEY,UAAU,CAACR,MAAM,CAACJ,YAAY;MAC9C;IACF,CAAC;IAED7B,QAAQ,CAACvB,WAAW,CAAC2B,UAAU,CAACmC,MAAM,CAAC,CAAC;EAC1C,CAAC;;EAED;EACA,MAAMG,cAAc,GAAGA,CAACf,SAAS,EAAEC,OAAO,KAAK;IAC7C,MAAM,CAACe,SAAS,EAAEC,QAAQ,CAAC,GAAGjB,SAAS,CAACV,KAAK,CAAC,GAAG,CAAC,CAAC4B,GAAG,CAACC,MAAM,CAAC;IAC9D,MAAM,CAACC,OAAO,EAAEC,MAAM,CAAC,GAAGpB,OAAO,CAACX,KAAK,CAAC,GAAG,CAAC,CAAC4B,GAAG,CAACC,MAAM,CAAC;IAExD,IAAIG,YAAY,GAAIN,SAAS,GAAG,EAAE,GAAIC,QAAQ;IAC9C,IAAIM,UAAU,GAAIH,OAAO,GAAG,EAAE,GAAIC,MAAM;;IAExC;IACA,IAAIE,UAAU,IAAID,YAAY,EAAE;MAC9BC,UAAU,IAAK,EAAE,GAAG,EAAG,CAAC,CAAC;IAC3B;IAEA,MAAMC,WAAW,GAAGD,UAAU,GAAGD,YAAY;IAC7C,OAAO,CAACE,WAAW,GAAG,EAAE,EAAEC,OAAO,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACjDrC,MAAM,CAACsC,aAAa,CAACF,KAAK,EAAEC,KAAK,CAAC;;IAElC;IACA,IAAID,KAAK,KAAK,gBAAgB,EAAE;MAC9B,MAAM/D,YAAY,GAAGU,KAAK,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChB,GAAG,KAAKkC,KAAK,CAAC;MACrD,IAAIhE,YAAY,EAAE;QAAA,IAAAkE,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAChBrD,sBAAsB,CAAChB,YAAY,CAAC;QACpC2B,MAAM,CAACsC,aAAa,CAAC,kBAAkB,EAAE,EAAAC,qBAAA,GAAAlE,YAAY,CAACiC,YAAY,cAAAiC,qBAAA,uBAAzBA,qBAAA,CAA2BlC,gBAAgB,KAAIxC,qBAAqB,CAACwC,gBAAgB,CAAC;QAC/HL,MAAM,CAACsC,aAAa,CAAC,WAAW,EAAE,EAAAE,sBAAA,GAAAnE,YAAY,CAACiC,YAAY,cAAAkC,sBAAA,uBAAzBA,sBAAA,CAA2B/B,SAAS,KAAI5C,qBAAqB,CAAC4C,SAAS,CAAC;QAC1GT,MAAM,CAACsC,aAAa,CAAC,SAAS,EAAE,EAAAG,sBAAA,GAAApE,YAAY,CAACiC,YAAY,cAAAmC,sBAAA,uBAAzBA,sBAAA,CAA2B/B,OAAO,KAAI7C,qBAAqB,CAAC6C,OAAO,CAAC;QACpGV,MAAM,CAACsC,aAAa,CAAC,cAAc,EAAE,EAAAI,sBAAA,GAAArE,YAAY,CAACiC,YAAY,cAAAoC,sBAAA,uBAAzBA,sBAAA,CAA2B/B,YAAY,KAAI9C,qBAAqB,CAAC8C,YAAY,CAAC;MACrH;IACF;;IAEA;IACA,IAAIyB,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,SAAS,EAAE;MAChD,MAAM3B,SAAS,GAAG2B,KAAK,KAAK,WAAW,GAAGC,KAAK,GAAGrC,MAAM,CAACe,MAAM,CAACN,SAAS;MACzE,MAAMC,OAAO,GAAG0B,KAAK,KAAK,SAAS,GAAGC,KAAK,GAAGrC,MAAM,CAACe,MAAM,CAACL,OAAO;MAEnE,IAAID,SAAS,IAAIC,OAAO,EAAE;QACxB,MAAMiC,eAAe,GAAGnB,cAAc,CAACf,SAAS,EAAEC,OAAO,CAAC;QAC1DV,MAAM,CAACsC,aAAa,CAAC,cAAc,EAAEK,eAAe,CAAC;MACvD;IACF;;IAEA;IACA,IAAIP,KAAK,KAAK,WAAW,EAAE;MACzB,MAAMQ,IAAI,GAAGC,QAAQ,CAACR,KAAK,CAACtC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC9C,IAAI6C,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,CAAC,EAAE;QAC1B5C,MAAM,CAACsC,aAAa,CAAC,kBAAkB,EAAE,aAAa,CAAC;MACzD,CAAC,MAAM;QACLtC,MAAM,CAACsC,aAAa,CAAC,kBAAkB,EAAE,WAAW,CAAC;MACvD;IACF;EACF,CAAC;EAED,oBACErE,OAAA,CAAC1B,MAAM;IAAC4B,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC0E,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3D/E,OAAA,CAACzB,WAAW;MAAAwG,QAAA,gBACV/E,OAAA,CAACpB,UAAU;QAACoG,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAEzB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbpF,OAAA,CAACpB,UAAU;QAACoG,OAAO,EAAC,OAAO;QAACK,KAAK,EAAC,gBAAgB;QAAAN,QAAA,GAAC,QAC3C,EAAC1E,YAAY;MAAA;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,EACZ,CAAAjE,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEkB,YAAY,kBAChCrC,OAAA,CAACpB,UAAU;QAACoG,OAAO,EAAC,SAAS;QAACK,KAAK,EAAC,SAAS;QAACC,EAAE,EAAE;UAAEC,OAAO,EAAE,OAAO;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,GAAC,WACpE,EAAC5D,mBAAmB,CAACsE,IAAI,EAAC,KAAG,EAACtE,mBAAmB,CAACkB,YAAY,CAACD,gBAAgB,KAAK,WAAW,GAAG,WAAW,GAAG,aAAa,EAAC,GACtI,EAACjB,mBAAmB,CAACkB,YAAY,CAACG,SAAS,EAAC,GAAC,EAACrB,mBAAmB,CAACkB,YAAY,CAACI,OAAO,EAAC,GACxF,EAACtB,mBAAmB,CAACkB,YAAY,CAACK,YAAY,EAAC,QACjD;MAAA;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACU,CAAC,eAEdpF,OAAA,CAACxB,aAAa;MAAAuG,QAAA,eACZ/E,OAAA,CAAClB,GAAG;QAACwG,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,eACjB/E,OAAA;UAAM6C,QAAQ,EAAEd,MAAM,CAACgB,YAAa;UAAAgC,QAAA,eAClC/E,OAAA,CAACrB,IAAI;YAAC+G,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAZ,QAAA,gBAEzB/E,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAd,QAAA,eAChB/E,OAAA,CAACX,WAAW;gBACVyG,KAAK,EAAC,aAAa;gBACnBL,IAAI,EAAC,gBAAgB;gBACrBrB,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACb,cAAe;gBACpC8D,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,gBAAgB,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBAC5E8B,QAAQ;gBAAAnB,QAAA,EAEPjE,KAAK,CAAC4C,GAAG,CAAEyC,IAAI;kBAAA,IAAAC,iBAAA;kBAAA,oBACdpG,OAAA,CAACnB,QAAQ;oBAAgBuF,KAAK,EAAE+B,IAAI,CAACjE,GAAI;oBAAA6C,QAAA,GACtCoB,IAAI,CAACV,IAAI,EAAC,KAAG,EAAC,EAAAW,iBAAA,GAAAD,IAAI,CAACE,WAAW,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAkBX,IAAI,KAAIU,IAAI,CAACG,IAAI,IAAI,SAAS,EAC9DH,IAAI,CAAC9D,YAAY,KAChB8D,IAAI,CAAC9D,YAAY,CAACG,SAAS,KAAK,OAAO,IACvC2D,IAAI,CAAC9D,YAAY,CAACI,OAAO,KAAK,OAAO,IACrC0D,IAAI,CAAC9D,YAAY,CAACD,gBAAgB,KAAK,WAAW,CACnD,iBACCpC,OAAA,CAACpB,UAAU;sBAAC2H,SAAS,EAAC,MAAM;sBAACvB,OAAO,EAAC,SAAS;sBAACK,KAAK,EAAC,SAAS;sBAACC,EAAE,EAAE;wBAAEkB,EAAE,EAAE;sBAAE,CAAE;sBAAAzB,QAAA,GAAC,GAC3E,EAACoB,IAAI,CAAC9D,YAAY,CAACG,SAAS,EAAC,GAAC,EAAC2D,IAAI,CAAC9D,YAAY,CAACI,OAAO,EAAC,GAC5D;oBAAA;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CACb;kBAAA,GAVYe,IAAI,CAACjE,GAAG;oBAAA+C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWb,CAAC;gBAAA,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACY,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACvB/E,OAAA,CAACX,WAAW;gBACVyG,KAAK,EAAC,mBAAmB;gBACzBL,IAAI,EAAC,kBAAkB;gBACvBrB,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACV,gBAAiB;gBACtC2D,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,kBAAkB,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBAC9E8B,QAAQ;gBAAAnB,QAAA,EAEPrF,kBAAkB,CAACgE,GAAG,CAAEgD,QAAQ,iBAC/B1G,OAAA,CAACnB,QAAQ;kBAAsBuF,KAAK,EAAEsC,QAAQ,CAACtC,KAAM;kBAAAW,QAAA,EAClD2B,QAAQ,CAACZ;gBAAK,GADFY,QAAQ,CAACtC,KAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACY,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACvB/E,OAAA,CAACZ,KAAK;gBACJ0G,KAAK,EAAC,eAAe;gBACrBL,IAAI,EAAC,cAAc;gBACnBvE,IAAI,EAAC,QAAQ;gBACbyF,IAAI,EAAC,KAAK;gBACVvC,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACJ,YAAa;gBAClCqD,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,cAAc,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBAC1E8B,QAAQ;gBACRU,UAAU,EACR7E,MAAM,CAACe,MAAM,CAACN,SAAS,IAAIT,MAAM,CAACe,MAAM,CAACL,OAAO,GAAG,eAAec,cAAc,CAACxB,MAAM,CAACe,MAAM,CAACN,SAAS,EAAET,MAAM,CAACe,MAAM,CAACL,OAAO,CAAC,QAAQ,GAAG;cAC5I;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACY,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACvB/E,OAAA,CAACZ,KAAK;gBACJ0G,KAAK,EAAC,kBAAkB;gBACxBL,IAAI,EAAC,YAAY;gBACjBvE,IAAI,EAAC,MAAM;gBACXkD,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACR,UAAW;gBAChCyD,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,YAAY,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBACxE8B,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACY,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACvB/E,OAAA,CAACZ,KAAK;gBACJ0G,KAAK,EAAC,gBAAgB;gBACtBL,IAAI,EAAC,UAAU;gBACfvE,IAAI,EAAC,MAAM;gBACXkD,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACP,QAAS;gBAC9BwD,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,UAAU,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBACtE8B,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACY,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACvB/E,OAAA,CAACX,WAAW;gBACVyG,KAAK,EAAC,YAAY;gBAClBL,IAAI,EAAC,WAAW;gBAChBrB,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACN,SAAU;gBAC/BuD,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,WAAW,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBACvE8B,QAAQ;gBAAAnB,QAAA,EAEPlF,YAAY,CAAC6D,GAAG,CAAEmD,MAAM,iBACvB7G,OAAA,CAACnB,QAAQ;kBAAoBuF,KAAK,EAAEyC,MAAM,CAACzC,KAAM;kBAAAW,QAAA,EAC9C8B,MAAM,CAACf;gBAAK,GADAe,MAAM,CAACzC,KAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAACY,EAAE,EAAE,CAAE;cAAA1B,QAAA,eACvB/E,OAAA,CAACX,WAAW;gBACVyG,KAAK,EAAC,UAAU;gBAChBL,IAAI,EAAC,SAAS;gBACdrB,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACL,OAAQ;gBAC7BsD,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,SAAS,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBACrE8B,QAAQ;gBAAAnB,QAAA,EAEPlF,YAAY,CAAC6D,GAAG,CAAEmD,MAAM,iBACvB7G,OAAA,CAACnB,QAAQ;kBAAoBuF,KAAK,EAAEyC,MAAM,CAACzC,KAAM;kBAAAW,QAAA,EAC9C8B,MAAM,CAACf;gBAAK,GADAe,MAAM,CAACzC,KAAK;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAd,QAAA,eAChB/E,OAAA,CAACZ,KAAK;gBACJ0G,KAAK,EAAC,oBAAoB;gBAC1BL,IAAI,EAAC,UAAU;gBACfqB,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACR3C,KAAK,EAAErC,MAAM,CAACe,MAAM,CAACH,QAAS;gBAC9BoD,QAAQ,EAAGC,CAAC,IAAK9B,wBAAwB,CAAC,UAAU,EAAE8B,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAE;gBACtE4C,WAAW,EAAC;cAAiE;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPpF,OAAA,CAACrB,IAAI;cAACiH,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAd,QAAA,eAChB/E,OAAA,CAAClB,GAAG;gBAACwG,EAAE,EAAE;kBACP2B,CAAC,EAAE,CAAC;kBACJC,OAAO,EAAE,YAAY;kBACrBC,YAAY,EAAE,CAAC;kBACfC,MAAM,EAAE,WAAW;kBACnBC,WAAW,EAAE,WAAW;kBACxB9B,OAAO,EAAE,MAAM;kBACf+B,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE;gBACd,CAAE;gBAAAxC,QAAA,gBACR/E,OAAA,CAACpB,UAAU;kBAACoG,OAAO,EAAC,OAAO;kBAACK,KAAK,EAAC,WAAW;kBAAAN,QAAA,gBACrD/E,OAAA;oBAAA+E,QAAA,EAAQ;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,sGAExB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAEKpF,OAAA,CAACtB,MAAM;kBACLsG,OAAO,EAAC,UAAU;kBAClBwC,IAAI,EAAC,OAAO;kBACZC,OAAO,EAAEA,CAAA,KAAMnG,mBAAmB,CAAC,IAAI,CAAE;kBAAAyD,QAAA,EAC1C;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBpF,OAAA,CAACvB,aAAa;MAAAsG,QAAA,gBACZ/E,OAAA,CAACtB,MAAM;QAAC+I,OAAO,EAAEtH,OAAQ;QAACkF,KAAK,EAAC,WAAW;QAAAN,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpF,OAAA,CAACtB,MAAM;QACL+I,OAAO,EAAE1F,MAAM,CAACgB,YAAa;QAC7BiC,OAAO,EAAC,WAAW;QACnBK,KAAK,EAAC,SAAS;QAAAN,QAAA,EAChB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGhBpF,OAAA,CAACF,wBAAwB;MACvBI,IAAI,EAAEmB,gBAAiB;MACvBlB,OAAO,EAAEA,CAAA,KAAMmB,mBAAmB,CAAC,KAAK,CAAE;MAC1ClB,YAAY,EAAEe,mBAAoB;MAClCd,YAAY,EAAEA,YAAa;MAC3BqH,YAAY,EAAC;IAAO;MAAAzC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;AAAC9E,EAAA,CApUIL,qBAAqB;EAAA,QACRjB,WAAW,EACdC,WAAW,EACTA,WAAW,EA8BZF,SAAS;AAAA;AAAA4I,EAAA,GAjCpB1H,qBAAqB;AAsU3BA,qBAAqB,CAAC2H,SAAS,GAAG;EAChC1H,IAAI,EAAEf,SAAS,CAAC0I,IAAI,CAACC,UAAU;EAC/B3H,OAAO,EAAEhB,SAAS,CAAC4I,IAAI,CAACD,UAAU;EAClC1H,YAAY,EAAEjB,SAAS,CAAC6I,MAAM;EAC9B3H,YAAY,EAAElB,SAAS,CAAC8I;AAC1B,CAAC;AAED,eAAehI,qBAAqB;AAAC,IAAA0H,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}