const path = require("path");
const { getLoader, loaderByN<PERSON> } = require("@craco/craco");

const packages = [];
console.log(__dirname);
packages.push(path.join(__dirname, "./path-to-the-shared-package"));
packages.push(path.join(__dirname, "./path-to-another-shared-package"));

module.exports = {
  webpack: {
    configure: (webpackConfig, arg) => {
      const { isFound, match } = getLoader(
        webpackConfig,
        loaderByName("babel-loader")
      );
      if (isFound) {
        const include = Array.isArray(match.loader.include)
          ? match.loader.include
          : [match.loader.include];

        match.loader.include = include.concat(packages);
      }
      return webpackConfig;
    },
  },
  devServer: {
    port: 3000,         // Ensure correct port binding
    host: "0.0.0.0",    // Allow external access
    allowedHosts: "all",
    historyApiFallback: true,  // Fix potential routing issues
  },
};
