/**
 * Redux Actions Export
 *
 * This module exports all action creators from the Redux slices.
 * These actions can be dispatched to trigger state changes in the Redux store.
 */

// Application Core
import GeneralSlice from "./slice/GeneralSlice";
import AuthSlice from "./slice/AuthSlice";

// User Management
import UserSlice from "./slice/UserSlice";

// Organization Structure
import DepartmentSlice from "./slice/DepartmentSlice";
import DesignationSlice from "./slice/DesignationSlice";

// HR Management
import AttendanceSlice from "./slice/AttendanceSlice";
import ExpensesSlice from "./slice/ExpensesSlice";
import LeaveSlice from "./slice/LeaveSlice";

// Activity Tracking
import ActivitySlice from "./slice/ActivitySlice";
import TimelineSlice from "./slice/TimelineSlice";

// Project Management
import ProductSlice from "./slice/ProductSlice";
import ClientSlice from "./slice/ClientSlice";
import SprintSlice from "./slice/SprintSlice";

// System Configuration
import SettingSlice from "./slice/SettingSlice";

// Application Core Actions
export const GeneralActions = GeneralSlice.actions;
export const AuthActions = AuthSlice.actions;

// User Management Actions
export const UserActions = UserSlice.actions;

// Organization Structure Actions
export const DepartmentActions = DepartmentSlice.actions;
export const DesignationActions = DesignationSlice.actions;

// HR Management Actions
export const AttendanceActions = AttendanceSlice.actions;
export const ExpensesActions = ExpensesSlice.actions;
export const LeaveActions = LeaveSlice.actions;

// Activity Tracking Actions
export const ActivityActions = ActivitySlice.actions;
export const TimelineActions = TimelineSlice.actions;

// Project Management Actions
export const ProductActions = ProductSlice.actions;
export const ClientActions = ClientSlice.actions;
export const SprintActions = SprintSlice.actions;

// System Configuration Actions
export const SettingActions = SettingSlice.actions;