import React, { useEffect, useState } from 'react';
import {
  Box,
  Card,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  Pagination,
  TableRow,
  Hidden,
  Typography,
  Button,
  IconButton,
  CircularProgress
} from "@mui/material";

import styled from "@emotion/styled";
import { DefaultSort } from 'constants/sort';
import { PlayCircle, StopCircle, Delete, Visibility } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { ProductSelector, UserSelector } from 'selectors';
import { useParams, useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import ProductHeader from './components/ProductHeader';
import { ProductActions, UserActions } from 'slices/actions';
import TaskInfoComponent from './components/TaskInfoComponent';

const FilterBox = styled(Box)(() => ({
  width: "100%",
  marginTop: 30,
  marginBottom: 20,
  display: "flex",
  justifyContent: "space-between",
}));

function TaskHistoryAdmin() {
  const { data } = useParams();
  const tasks = useSelector(ProductSelector.getTasks()) || [];
  const projects = useSelector(ProductSelector.getProducts()) || [];
  const pagination = useSelector(ProductSelector.getTaskPagination()) || {};
  const users = useSelector(UserSelector.getUsers()) || [];
  const profile = useSelector(UserSelector.profile());

  const dispatch = useDispatch();
  const history = useHistory();

  const [product, setProduct] = useState([]);
  const [currentTask, setCurrentTask] = useState(null);
  const [showTaskInfo, setShowTaskInfo] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState({
    sort: DefaultSort.newest.value,
    page: 1,
  });

  useEffect(() => {
    if (!projects.length) { dispatch(ProductActions.getProducts()) }
    if (!users.length) { dispatch(UserActions.getUsers()) }
  }, []);

  useEffect(() => {
    if (data && projects?.length > 0) {
      const foundProduct = projects.filter((element) => element._id === data);
      setProduct(foundProduct);
    }
  }, [data, projects]);

  useEffect(() => {
    if (data) {
      setLoading(true);
      dispatch(ProductActions.getTasks({ projectid: data, filter }));
      setTimeout(() => setLoading(false), 2000);
    }
  }, [data, filter]);

  useEffect(() => {
    if (tasks.length > 0 && product.length > 0) {
      const updatedProduct = { ...product[0], taskArr: tasks };
      setProduct([updatedProduct]);
    }
  }, [tasks]);

  const handleChangePagination = (e, val) => {
    setFilter((prev) => ({ ...prev, page: val }));
  };

  const manualFetch = () => {
    if (data) {
      setLoading(true);
      dispatch(ProductActions.getTasks({ projectid: data, filter }));
      setTimeout(() => setLoading(false), 2000);
    }
  };

  const handleStart = (taskId) => {
    dispatch(ProductActions.startTask({ taskId }));
    setTimeout(() => manualFetch(), 500);
  };

  const handleStop = (taskId) => {
    dispatch(ProductActions.stopTask({ taskId }));
    setTimeout(() => manualFetch(), 500);
  };

  const handleDelete = (taskId) => {
    dispatch(ProductActions.deleteTask(taskId));
    setTimeout(() => manualFetch(), 500);
  };

  const handleView = (task) => {
    setCurrentTask(task);
    setShowTaskInfo(true);
  };

  const closeTaskInfo = () => {
    setShowTaskInfo(false);
    setCurrentTask(null);
    manualFetch();
  };

  const tasksArr = product[0]?.taskArr || [];
  const filteredTasks = tasksArr.filter(
    (task) =>
      task.assignee.includes(profile?._id) || task.reporter === profile?._id
  );

  return (
    <>
      {showTaskInfo && currentTask && (
        <TaskInfoComponent
          data={currentTask}
          productId={product[0]?._id}
          taskInfoController={closeTaskInfo}
        />
      )}

      <Card style={{ overflow: "scroll" }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            {product[0]?.productName || "Loading Product..."}
          </Typography>

          <Button
            variant="contained"
            color="primary"
            onClick={manualFetch}
            disabled={loading}
          >
            Refresh Tasks
          </Button>
        </Box>

        <FilterBox>
          <Grid container spacing={10} justifyContent="space-between">
            <Grid item lg={11} sm={12} xs={12}>
              <ProductHeader product={product} />
            </Grid>
          </Grid>
        </FilterBox>

        <Box>
          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Table>
                <TableHead>
                  <TableRow>
                    <Hidden smDown>
                      <TableCell align="center">Task Name</TableCell>
                      <TableCell align="center">Assignee</TableCell>
                      <TableCell align="center">Status</TableCell>
                      <TableCell align="center">Spent/Assigned</TableCell>
                      <TableCell align="center">Action</TableCell>
                    </Hidden>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTasks.length > 0 ? (
                    filteredTasks.map((task, index) => (
                      <TableRow key={index}>
                        <TableCell align="center">{task.taskTitle}</TableCell>
                        <TableCell align="center">
                          {task.assignee.map((assigneeId) => {
                              const user = users.find((u) => u._id === assigneeId);
                              return user ? user.name : null;
                            }).filter((name) => name !== null).join(", ")}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton onClick={() => handleStart(task._id)}>
                            <PlayCircle />
                          </IconButton>
                          <IconButton onClick={() => handleStop(task._id)}>
                            <StopCircle />
                          </IconButton>
                        </TableCell>
                        <TableCell align="center">
                          {task.dailyLogs?.[new Date().toISOString().split("T")[0]] || 0} / {task.totalHours || "N/A"}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton onClick={() => handleView(task)}>
                            <Visibility />
                          </IconButton>
                          <IconButton onClick={() => handleDelete(task._id)}>
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        No Tasks Found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {pagination.pages > 0 && (
                <Pagination
                  sx={{ mt: 1 }}
                  page={filter.page}
                  count={pagination.pages}
                  onChange={handleChangePagination}
                />
              )}
            </>
          )}
        </Box>
      </Card>
    </>
  );
}

export default TaskHistoryAdmin;