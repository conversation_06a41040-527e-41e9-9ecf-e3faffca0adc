'use strict';
 
const express = require('express');
const router = express.Router();
const path = require("path");
const screenshot = require('../controllers/screenshot.controller');
const multer = require("multer");
 
// Configure Multer storage
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    return cb(null, "./static/screenshot");
  },
  filename: (req, file, cb) => {
    
   return cb(null, `${Date.now()}-${file.originalname}`);
  },
});
 
const upload = multer({ storage });
 
module.exports = (app) => {
    router.post("/upload-screenshot", upload.single("screenshot"),screenshot.uploadScreenShot);
 
    app.use("/api/track", router);
};