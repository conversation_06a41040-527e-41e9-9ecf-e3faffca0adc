import React, { useEffect, useState } from "react";
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableHead,
  Pagination,
  TableRow,
  Hidden,
  IconButton,
  Typography
} from "@mui/material";
import styled from "@emotion/styled";
import FloatingButton from "components/FloatingButton";
import { useDispatch, useSelector } from "react-redux";
import { DefaultSort } from "constants/sort";
import { ClientSelector, ProductSelector, UserSelector } from "selectors";
import { ClientActions, ProductActions, UserActions } from "slices/actions";
import ProductAdd from "./ProductAdd";
import { Button } from "react-bootstrap";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { AddCircle } from "@mui/icons-material";
import AddMembers from "./components/AddMembers";

const FilterBox = styled(Box)(() => ({
  width: "100%",
  marginTop: 30,
  marginBottom: 20,
  display: "flex",
  justifyContent: "space-between",
}));

function ProductList() {
  const [filter, setFilter] = useState({ sort: DefaultSort.newest.value, page: 1 });
  const products = useSelector(ProductSelector.getProducts()) || [];
  const profile = useSelector(UserSelector.profile());
  const users = useSelector(UserSelector.getUsers()) || [];
  const clients = useSelector(ClientSelector.getClients()) || [];
  const pagination = useSelector(ProductSelector.getPagination()) || { pages: 1 };

  const [productPop, setProductPop] = useState(false);
  const [addMemeber, setAddMember] = useState(false);
  const [currentProduct, setCurrectProduct] = useState(null);
  

  const dispatch = useDispatch();
  const history = useHistory();

  useEffect(() => {
    dispatch(UserActions.getUsers());
    dispatch(ClientActions.getClients({}));
    dispatch(ProductActions.getProducts(filter));
  }, [filter]);

  const handleChangePagination = (e, val) => {
    setFilter((prev) => ({ ...prev, page: val }));
  };

  const deleteProduct = (id) => {
    dispatch(ProductActions.deleteProduct({ id, query: filter }));
  };

  return (
    <>
      {addMemeber && <AddMembers openVal={addMemeber} openValFun={setAddMember} productId={currentProduct} />}
      {productPop && <ProductAdd openValFun={setProductPop} filter={filter} />}

      <Card style={{ overflow: "scroll" }}>
        <Typography variant="h5" sx={{ fontWeight: 600 }}>Projects</Typography>

        <Box>
          <Table>
            <TableHead>
              <TableRow>
                <Hidden smDown>
                  <TableCell align="center">Project Name</TableCell>
                  <TableCell align="center">Total Hr</TableCell>
                  <TableCell align="center">Members</TableCell>
                  <TableCell align="center">Client</TableCell>
                  <TableCell align="center">Access</TableCell>
                  <TableCell align="center">Action</TableCell>
                </Hidden>
              </TableRow>
            </TableHead>
            <TableBody>
              {products.length > 0 ? products.map((data, index) => (
                <TableRow key={index} sx={{ "&:last-child td, &:last-child td": { border: 0 } }}>
                  <TableCell align="center">{data.productName}</TableCell>
                  <TableCell align="center">{data?.totalHours}</TableCell>
                  <TableCell align="center">
                    <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                      {data?.members?.map((assigneeId) => users.find((u) => u._id === assigneeId)?.name).filter(Boolean).join(", ")}
                      <IconButton onClick={() => { setCurrectProduct(data._id); setAddMember(true); }}>
                        <AddCircle />
                      </IconButton>
                    </div>
                  </TableCell>
                  <TableCell align="center">{clients.find(c => c._id === data?.client)?.name || "N/A"}</TableCell>
                  <TableCell align="center">{data.visibility ? "Public" : "Private"}</TableCell>
                  <TableCell align="center">
                    <Button style={{ backgroundColor: "green", color: "white", marginRight: 1 }} onClick={() => history.push(`/app/project/update/${data._id}`)}>Update</Button>
                    <Button style={{ backgroundColor: "red", color: "white", marginRight: 1 }} onClick={() => deleteProduct(data._id)}>Delete</Button>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">No Data Found</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>

          <Pagination sx={{ mt: 1 }} page={filter.page} count={pagination.pages} onChange={handleChangePagination} />
        </Box>

        {profile?.role?.includes("admin") && <FloatingButton onClick={() => setProductPop(true)} />}
      </Card>
    </>
  );
}

export default ProductList;
