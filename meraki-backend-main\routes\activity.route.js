'use strict';

/**
 * Activity Routes
 *
 * This module defines all routes related to daily activity tracking,
 * including goals, check-ins/outs, breaks, and productivity metrics.
 */

const express = require('express');
const router = express.Router();
const activityRouter = express.Router();
const multer = require('multer')().none();
const activity = require('../controllers/activity.controller');

/**
 * Register all activity routes
 *
 * @param {Object} app - Express application instance
 */
module.exports = (app) => {
    // Goal management
    router.post("/goal", activity.registerGoal);

    // Activity history
    router.get("/history/:id", activity.getAllActivities);
    router.get('/history/:date/:id',activity.todayActivity)
    router.patch("/checkOut/delete",multer,activity.deleteCheckOutTime)

    // Check-in/out management
    router.patch("/checkout", multer, activity.checkOutStatusUpdate);
    router.patch("/late/checkin", multer, activity.lateCheckInUpdate);
    router.patch("/early/checkout", multer, activity.earlyCheckOutUpdate); // Fixed method name

    // Break management
    router.patch("/break/over", multer, activity.overLimitBreakUpdate);
    router.patch("/breakIn", multer, activity.breakInUpdate);
    router.patch("/breakOut", multer, activity.breakOutUpdate);

    // Idle time tracking
    router.patch("/idelstart", multer, activity.idelStartUpdate);
    router.patch("/idelend", multer, activity.idelEndUpdate);

    // Status updates
    router.patch("/status", multer, activity.todayStatusUpdate);
    router.patch("/product", multer, activity.productivityUpdate);

    // Auto-checkout and absent marking (admin only)
    router.post("/auto-checkout", multer, activity.runAutoCheckout);
    router.post("/mark-absent", multer, activity.markAbsentUsers);

    // Register all routes under /api/today
    app.use("/api/today", router);

    // New activity routes for multiple users data
    activityRouter.get("/users", activity.getAllUsersActivity);

    // Register activity routes under /api/activity
    app.use("/api/activity", activityRouter);

    router.get('/history/:date/:id',activity.todayActivity)
};