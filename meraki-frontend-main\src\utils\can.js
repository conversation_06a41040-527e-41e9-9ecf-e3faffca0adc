import store from "../store";

// Enable or disable permission logging (set to true for debugging)
const ENABLE_LOGGING = true;

// Add timestamp to logs for easier debugging
const log = (...args) => {
  if (ENABLE_LOGGING) {
    console.log(`[${new Date().toISOString()}]`, ...args);
  }
};

/**
 * Normalized feature names mapping
 * This maps various feature name formats to their canonical form
 */
const FEATURE_MAPPING = {
  // Dashboard
  'dashboard': 'Dashboard',
  'user-dashboard': 'Dashboard',
  'admin-dashboard': 'Dashboard',

  // User Management
  'user': 'User',
  'users': 'User',

  // Tasks
  'tasks': 'Tasks',
  'my tasks': 'Tasks',
  'mytasks': 'Tasks',
  'task': 'Tasks',

  // Projects
  'projects': 'Project',
  'project': 'Project',
  'project list': 'Project List',
  'projectlist': 'Project List',
  'project overview': 'Project Overview',
  'projectoverview': 'Project Overview',
  'project timesheet': 'Project Timesheet',
  'projecttimesheet': 'Project Timesheet',

  // Attendance
  'attendance': 'Attendance',
  'my attendance': 'Attendance',

  // Leave
  'leave': 'Leave',
  'my leave': 'Leave',
  'leave report': 'Leave Report',
  'leavereport': 'Leave Report',

  // Timeline
  'timeline': 'Timeline',
  'my timeline': 'Timeline',

  // Expense
  'expense': 'Expense',
  'expenses': 'Expense',
  'my expense': 'Expense',
  'my expenses': 'Expense',

  // Setting
  'setting': 'Setting',
  'settings': 'Setting',
};

/**
 * Simplified permission check function
 * @param {string} act - The action to check (e.g., 'read', 'create')
 * @param {string} feat - The feature to check (e.g., 'User', 'Attendance')
 * @returns {boolean} - Whether the user has permission
 */
export default function Can(act, feat) {
    const state = store.getState();
    const profile = state.user.profile;

    // If no profile or permissions, deny access
    if (!profile || !profile.permissions || !Array.isArray(profile.permissions)) {
        if (ENABLE_LOGGING) {
            console.log(`Permission Check: ${act} - ${feat} = DENIED (No profile or permissions)`);
        }
        return false;
    }

    // Normalize feature name (case-insensitive lookup)
    const normalizedFeatLower = feat.toLowerCase();
    const canonicalFeat = FEATURE_MAPPING[normalizedFeatLower] || feat;

    if (ENABLE_LOGGING) {
        console.log(`Checking permission: ${act} - ${feat} (normalized to ${canonicalFeat})`);
        console.log('User permissions:', profile.permissions);
    }

    // Check if user has admin role
    const isAdmin = profile.role && profile.role.includes('admin');

    // Special case for dashboard - always allow access
    if (canonicalFeat === 'Dashboard') {
        if (ENABLE_LOGGING) {
            console.log(`Permission Check: ${act} - ${feat} = GRANTED (Dashboard special case)`);
        }
        return true;
    }

    // Special case for admin users and Employee Management
    if (isAdmin && canonicalFeat === 'User') {
        if (ENABLE_LOGGING) {
            console.log(`Permission Check: ${act} - ${feat} = GRANTED (Admin special case for Employee Management)`);
        }
        return true;
    }

    // Special case for admin users and Settings
    if (isAdmin && canonicalFeat === 'Setting') {
        if (ENABLE_LOGGING) {
            console.log(`Permission Check: ${act} - ${feat} = GRANTED (Admin special case for Settings)`);
        }
        return true;
    }

    // Special case for read permission when user has read_all
    // This ensures that if a user has read_all, they also have read permission
    if (act === 'read') {
        const hasReadAll = profile.permissions.some(p =>
            p.feat === canonicalFeat &&
            p.acts &&
            p.acts.includes('read_all')
        );

        if (hasReadAll) {
            if (ENABLE_LOGGING) {
                console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has read_all permission)`);
            }
            return true;
        }
    }

    // Check for parent-child menu permission inheritance
    // If a feature has a parent (e.g., "Leave Report" is a child of "Leave"),
    // and the user has permission for the parent, grant permission for the child
    const parentFeatureMap = {
        // Leave Management children
        'Leave Report': 'Leave',
        'Approve': 'Leave',

        // Project Management children
        'Project Overview': 'Project',
        'Project List': 'Project',
        'Project Timesheet': 'Project',

        // Expense Management children
        'Expense Report': 'Expense',
        'Expense Approval': 'Expense',

        // Add more parent-child relationships as needed
    };

    // If this feature has a parent defined in the map
    if (parentFeatureMap[canonicalFeat]) {
        const parentFeat = parentFeatureMap[canonicalFeat];

        // Check if user has the same permission for the parent feature
        const hasParentPermission = profile.permissions.some(p => {
            // Handle both "Project" and "Projects" for parent feature
            const parentMatches =
                p.feat === parentFeat ||
                (parentFeat === 'Project' && p.feat === 'Projects') ||
                (parentFeat === 'Projects' && p.feat === 'Project');

            return parentMatches && p.acts && p.acts.includes(act);
        });

        if (hasParentPermission) {
            if (ENABLE_LOGGING) {
                console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has permission for parent feature ${parentFeat})`);
            }
            return true;
        }

        // If the action is 'read' and the user has 'read_all' for the parent
        if (act === 'read') {
            const hasParentReadAll = profile.permissions.some(p => {
                // Handle both "Project" and "Projects" for parent feature
                const parentMatches =
                    p.feat === parentFeat ||
                    (parentFeat === 'Project' && p.feat === 'Projects') ||
                    (parentFeat === 'Projects' && p.feat === 'Project');

                return parentMatches && p.acts && p.acts.includes('read_all');
            });

            if (hasParentReadAll) {
                if (ENABLE_LOGGING) {
                    console.log(`Permission Check: ${act} - ${feat} = GRANTED (User has read_all permission for parent feature ${parentFeat})`);
                }
                return true;
            }
        }
    }

    // Check for exact permission match
    const hasDirectPermission = profile.permissions.some(p => {
        // Skip invalid permissions
        if (!p || !p.feat || !Array.isArray(p.acts)) {
            return false;
        }

        // Normalize permission feature name
        const permFeatLower = p.feat.toLowerCase();
        const canonicalPermFeat = FEATURE_MAPPING[permFeatLower] || p.feat;

        // Check for feature match
        const featureMatches = canonicalPermFeat === canonicalFeat;

        // Basic permission check - if acts array is empty, treat as no permissions
        if (featureMatches && p.acts.length === 0) {
            if (ENABLE_LOGGING) {
                console.log(`Feature ${p.feat} has empty acts array, treating as no permissions`);
            }
            return false;
        }

        // Basic permission check
        if (featureMatches && (p.acts.includes(act) || p.acts.includes('*'))) {
            return true;
        }

        // For 'read' permission, also allow if user has higher permissions
        if (act === 'read' && featureMatches) {
            return p.acts.includes('create') ||
                   p.acts.includes('update') ||
                   p.acts.includes('delete') ||
                   p.acts.includes('read_all') ||
                   p.acts.includes('read_some') ||
                   p.acts.includes('read_self');
        }

        // For specialized read permissions
        if (act === 'read_all' && featureMatches) {
            return p.acts.includes('read_all');
        }

        return false;
    });

    // Check for wildcard permission
    const hasWildcardPermission = profile.permissions.some(p =>
        p.feat === '*' && (p.acts.includes(act) || p.acts.includes('*'))
    );

    // Log the result
    if (ENABLE_LOGGING) {
        if (hasDirectPermission) {
            console.log(`Permission Check: ${act} - ${feat} = GRANTED (Direct match)`);
        } else if (hasWildcardPermission) {
            console.log(`Permission Check: ${act} - ${feat} = GRANTED (Wildcard)`);
        } else {
            console.log(`Permission Check: ${act} - ${feat} = DENIED (No matching permission)`);
        }
    }

    return hasDirectPermission || hasWildcardPermission;
}