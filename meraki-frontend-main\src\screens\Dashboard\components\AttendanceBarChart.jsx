import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend } from 'chart.js';
import { Card, CardContent, Typography } from '@mui/material';

// Register Chart.js components
ChartJS.register(BarElement, CategoryScale, LinearScale, Title, Tooltip, Legend);

function AttendanceBarChart({ activities }) {
    const [datesData, setDatesData] = useState([]);
    const [hoursData, setHoursData] = useState([]);

    useEffect(() => {
        if (!activities || activities.length === 0) {
            return;
        }

        const tempDates = [];
        const tempHours = [];

        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const options = { day: '2-digit', month: 'short', year: 'numeric' };
            const formattedDate = date.toLocaleDateString('en-GB', options);

            tempDates.push(formattedDate);

            let found = false;
            for (let j = 0; j < activities.length; j++) {
                const workingDate = new Date(activities[j].checkInTime);
                const formattedWorkingDate = workingDate.toLocaleDateString('en-GB', options);

                if (formattedWorkingDate === formattedDate) {
                    tempHours.push(activities[j].totalWorkingTime / 60);
                    found = true;
                    break;
                }
            }

            if (!found) {
                tempHours.push(0);
            }
        }

        setDatesData(tempDates);
        setHoursData(tempHours);
    }, [activities]);

    // Chart Data
    const data = {
        labels: datesData,
        datasets: [{
            label: 'Attendance',
            data: hoursData,
            backgroundColor: 'rgba(0, 128, 0, 1)',
            borderColor: 'rgba(75, 192, 192, 1)',
            borderWidth: 1,
        }],
    };

    // Chart Options
    const options = {
        responsive: true,
        maintainAspectRatio: false, // Allows setting a custom height
        plugins: {
            legend: { position: 'top' },
            title: { display: true, text: 'Attendance Bar Chart' },
        },
    };

    return (
        <Card sx={{ width: '100%',  borderRadius: "20px", boxShadow: "rgba(149, 157, 165, 0.2) 0px 8px 24px", padding: "20px" }}>
            <CardContent>

                {/* Title */}
                <Typography variant="h5" align="center" gutterBottom sx={{ padding: "10px", margin: "10px" }}>
                    Attendance Overview
                </Typography>
                <div style={{ height: 250 }}>
                    <Bar data={data} options={options} />
                </div>
            </CardContent>
        </Card>
    );
}

AttendanceBarChart.propTypes = {
    activities: PropTypes.array,
};

export default AttendanceBarChart;