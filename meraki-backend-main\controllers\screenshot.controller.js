'use strict';
const Screenshot = require("../models/screenshot.model")
 
 
exports.uploadScreenShot = async (req, res) => {
 
    console.log("Requested File ",req.file)
     if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
    }
    const path = req.file.path
    const result = await Screenshot.create({
        imagePath : path,
        captureTime: new Date()
    })
    console.log("Result after uploading the file ",result)
 
    return res.status(200).send({ message: "Screenshot uploaded!"});
}