import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Grid, IconButton
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { UserActions } from 'slices/actions';
import { UserSelector } from 'selectors';
import DaySpecificScheduleForm from './DaySpecificScheduleForm';

const WeekWorkSchedule = ({ dateRange }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const [open, setOpen] = useState(false);
  const [selectedCell, setSelectedCell] = useState(null);

  // Fetch users when component mounts
  useEffect(() => {
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  // Generate days for the current week based on dateRange
  const generateWeekDays = () => {
    if (!dateRange?.startDate) {
      // Default to current week if no dateRange provided
      const startOfWeek = dayjs().startOf('week');
      return Array.from({ length: 7 }, (_, i) => {
        const date = startOfWeek.add(i, 'day');
        return {
          label: `${date.format('ddd DD')}`,
          date: date.format('YYYY-MM-DD'),
          fullDate: date
        };
      });
    }

    const startDate = dayjs(dateRange.startDate);
    const endDate = dayjs(dateRange.endDate);
    const days = [];

    // Calculate the difference in days
    const daysDiff = endDate.diff(startDate, 'day') + 1; // +1 to include both start and end dates

    for (let i = 0; i < daysDiff && i < 7; i++) { // Limit to 7 days max for safety
      const currentDate = startDate.add(i, 'day');
      days.push({
        label: `${currentDate.format('ddd DD')}`,
        date: currentDate.format('YYYY-MM-DD'),
        fullDate: currentDate
      });
    }

    return days;
  };

  const days = generateWeekDays();

  // Format the week range display
  const getWeekDisplayText = () => {
    if (days.length > 0) {
      const firstDay = days[0].fullDate;
      const lastDay = days[days.length - 1].fullDate;
      return `${firstDay.format("MMM D")} - ${lastDay.format("MMM D, YYYY")}`;
    }
    return '';
  };

  const handleCellClick = (user, day) => {
    setSelectedCell({
      user,
      day: day.label,
      date: day.date
    });
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setSelectedCell(null);
  };

  return (
    <Box p={2}>
      <Typography variant="h5" mb={2}>
        Work Schedule - {getWeekDisplayText()}
      </Typography>
      <Grid container spacing={1}>
        <Grid item xs={2.5}></Grid>
        {days.map((day) => (
          <Grid key={day.date} item xs>
            <Typography variant="subtitle2" align="center">{day.label}</Typography>
          </Grid>
        ))}
      </Grid>

      {users.map((user, rowIndex) => (
        <Grid container key={rowIndex} spacing={1} alignItems="center" mt={1}>
          <Grid item xs={2.5}>
            <Box>
              <Typography variant="body1" fontWeight="bold">{user.name || 'Unknown User'}</Typography>
              <Typography variant="caption">
                {user.designation?.name || user.role || 'No Role'}
              </Typography>
              {/* Show work schedule indicator if different from default */}
              {user.workSchedule && (
                user.workSchedule.startTime !== '09:00' ||
                user.workSchedule.endTime !== '17:30' ||
                user.workSchedule.scheduleTemplate !== 'day_shift'
              ) && (
                <Typography variant="caption" color="primary" sx={{ display: 'block' }}>
                  {user.workSchedule.startTime}-{user.workSchedule.endTime}
                </Typography>
              )}
            </Box>
          </Grid>
          {days.map((day, colIndex) => {
            // Check if user has custom work schedule
            const hasCustomSchedule = user.workSchedule && (
              user.workSchedule.startTime !== '09:00' ||
              user.workSchedule.endTime !== '17:30' ||
              user.workSchedule.scheduleTemplate !== 'day_shift'
            );

            return (
              <Grid item xs key={colIndex}>
                <Box
                  sx={{
                    height: 60,
                    bgcolor: hasCustomSchedule ? 'rgba(25, 118, 210, 0.08)' : 'rgba(0,0,0,0.03)',
                    borderRadius: 1,
                    position: 'relative',
                    backdropFilter: 'blur(3px)',
                    '&:hover .add-icon': { opacity: 1 },
                    '&:hover .schedule-info': { opacity: 1 },
                    border: hasCustomSchedule ? '1px solid rgba(25, 118, 210, 0.2)' : 'none'
                  }}
                >
                  {/* Show schedule info if custom schedule exists */}
                  {hasCustomSchedule && (
                    <Box
                      className="schedule-info"
                      sx={{
                        position: 'absolute',
                        top: 2,
                        left: 2,
                        right: 2,
                        fontSize: '9px',
                        color: 'primary.main',
                        fontWeight: 600,
                        textAlign: 'center',
                        opacity: 0.8,
                        transition: 'opacity 0.3s',
                        lineHeight: 1.1,
                        overflow: 'hidden'
                      }}
                    >
                      {user.workSchedule.scheduleTemplate === 'night_shift' ? 'Night' : 'Day'}
                      <br />
                      {user.workSchedule.startTime}-{user.workSchedule.endTime}
                      <br />
                      <Typography variant="caption" sx={{ fontSize: '7px', color: 'text.secondary' }}>
                        {user.workSchedule.minimumHours}h min
                      </Typography>
                    </Box>
                  )}

                  <IconButton
                    className="add-icon"
                    sx={{
                      position: 'absolute',
                      bottom: 2,
                      right: 2,
                      opacity: 0,
                      transition: 'opacity 0.3s',
                      '& .MuiSvgIcon-root': {
                        fontSize: '14px'
                      }
                    }}
                    onClick={() => handleCellClick(user, day)}
                  >
                    <AddCircleOutlineIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Grid>
            );
          })}
        </Grid>
      ))}

      {/* Day-Specific Schedule Form */}
      <DaySpecificScheduleForm
        open={open}
        onClose={handleClose}
        selectedUser={selectedCell?.user}
        selectedDate={selectedCell?.date}
        dayName={selectedCell?.day}
      />
    </Box>
  );
};

WeekWorkSchedule.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default WeekWorkSchedule;
