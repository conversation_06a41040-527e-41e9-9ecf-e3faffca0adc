const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const projectRoot = 'e:\\Suraj Patil\\Organization_Management_System';
const frontendRoot = path.join(projectRoot, 'meraki-frontend-main');
const backendRoot = path.join(projectRoot, 'meraki-backend-main');

// Read package.json files
const frontendPackageJson = JSON.parse(fs.readFileSync(path.join(frontendRoot, 'package.json'), 'utf8'));
const backendPackageJson = JSON.parse(fs.readFileSync(path.join(backendRoot, 'package.json'), 'utf8'));

// Get all dependencies
const frontendDeps = {
  ...frontendPackageJson.dependencies,
  ...frontendPackageJson.devDependencies
};
const backendDeps = {
  ...backendPackageJson.dependencies
};

console.log('=== FRONTEND ANALYSIS ===');

// Find all JS/JSX files in the frontend
function getAllFiles(dir, fileList = [], extensions = ['.js', '.jsx']) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('node_modules') && !file.startsWith('.git')) {
      fileList = getAllFiles(filePath, fileList, extensions);
    } else if (extensions.includes(path.extname(file).toLowerCase())) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Get all frontend files
const frontendFiles = getAllFiles(frontendRoot);
console.log(`Found ${frontendFiles.length} JavaScript/JSX files in frontend`);

// Check for unused dependencies
const usedDeps = new Set();

// Process each file to find imports
frontendFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  
  // Match import statements
  const importRegex = /import\s+(?:(?:{[^}]*}|\*\s+as\s+\w+|\w+)\s+from\s+)?['"]([^'"]+)['"]/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    const importPath = match[1];
    
    // Check if it's a package import (not relative)
    if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
      // Extract package name (handle subpaths like @mui/material/Button)
      const packageName = importPath.startsWith('@') 
        ? importPath.split('/').slice(0, 2).join('/')
        : importPath.split('/')[0];
      
      usedDeps.add(packageName);
    }
  }
});

// Find unused dependencies
const unusedDeps = Object.keys(frontendDeps).filter(dep => !usedDeps.has(dep));

console.log('\n=== UNUSED FRONTEND DEPENDENCIES ===');
unusedDeps.forEach(dep => {
  console.log(`- ${dep}: ${frontendDeps[dep]}`);
});

// Find commented code blocks
console.log('\n=== COMMENTED CODE BLOCKS ===');
let commentedCodeCount = 0;

frontendFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const relativePath = path.relative(projectRoot, file);
  
  // Find multi-line commented code blocks
  const commentedCodeBlocks = content.match(/\/\*[\s\S]*?\*\/|\/\/.*$/gm) || [];
  const commentedCodeLines = content.match(/^\s*\/\/.*$/gm) || [];
  
  // Only report significant commented code (more than just a few characters)
  const significantComments = [
    ...commentedCodeBlocks.filter(block => block.length > 50 && !block.includes('@') && !block.includes('* ')),
    ...commentedCodeLines.filter(line => line.trim().length > 30 && !line.includes('TODO') && !line.includes('FIXME'))
  ];
  
  if (significantComments.length > 0) {
    console.log(`\nFile: ${relativePath}`);
    console.log(`Found ${significantComments.length} commented code blocks`);
    commentedCodeCount += significantComments.length;
  }
});

console.log(`\nTotal commented code blocks found: ${commentedCodeCount}`);

// Find unused imports in files
console.log('\n=== UNUSED IMPORTS IN FILES ===');
let unusedImportsCount = 0;

frontendFiles.forEach(file => {
  const content = fs.readFileSync(file, 'utf8');
  const relativePath = path.relative(projectRoot, file);
  
  // Extract all imports
  const importRegex = /import\s+{([^}]+)}\s+from\s+['"][^'"]+['"]/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    const imports = match[1].split(',').map(i => i.trim().split(' as ')[0].trim());
    
    // Check each imported item
    imports.forEach(importName => {
      // Simple heuristic: if the import name doesn't appear elsewhere in the file, it might be unused
      const importRegExp = new RegExp(`[^a-zA-Z0-9_]${importName}[^a-zA-Z0-9_]`, 'g');
      const usageCount = (content.match(importRegExp) || []).length;
      
      if (usageCount <= 1 && importName.length > 1) { // The 1 match would be the import itself
        console.log(`${relativePath}: Potentially unused import '${importName}'`);
        unusedImportsCount++;
      }
    });
  }
});

console.log(`\nTotal potentially unused imports found: ${unusedImportsCount}`);

// Analyze the Widgets.js file specifically
console.log('\n=== WIDGETS.JS ANALYSIS ===');
const widgetsPath = path.join(frontendRoot, 'src', 'screens', 'Dashboard', 'components', 'Widgets.js');
const widgetsContent = fs.readFileSync(widgetsPath, 'utf8');

// Check for commented code
const commentedCode = widgetsContent.match(/\/\*[\s\S]*?\*\/|\/\/.*$/gm) || [];
console.log(`Found ${commentedCode.length} commented sections in Widgets.js`);

// Check for unused variables
const declaredVars = [];
const regex = /const\s+(\w+)\s*=/g;
let varMatch;
while ((varMatch = regex.exec(widgetsContent)) !== null) {
  declaredVars.push(varMatch[1]);
}

declaredVars.forEach(variable => {
  // Count occurrences after declaration
  const varRegex = new RegExp(`[^a-zA-Z0-9_]${variable}[^a-zA-Z0-9_]`, 'g');
  const count = (widgetsContent.match(varRegex) || []).length;
  
  if (count <= 1) { // Only appears in declaration
    console.log(`Potentially unused variable in Widgets.js: ${variable}`);
  }
});

console.log('\nAnalysis complete!');