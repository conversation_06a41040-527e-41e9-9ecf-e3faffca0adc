.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.vertical-header {
  writing-mode: vertical-rl;
  transform: rotate(180deg);
  text-align: left;
}


.App-link {
  color: #61dafb;
}

/* .timelineData {
  min-width: 268.5px;
  min-height: 53.02px;
  max-height: 53.02px;
  max-width: 268.5px;
}

.timelineHeader {
  min-height: 53.02px;
  max-height: 53.02px;
  min-width: 158.59px;
  max-width: 158.59px;
} */


.timeSlotLi{
  width: 49.5px;
  font-weight: 400;
  font-size: 14.5px;
  text-align: left;
  height: 40px;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
