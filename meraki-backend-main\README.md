# MERAKI - HR Management

Let's discover **Meraki HRM System**.


## This is <PERSON><PERSON><PERSON> - Backend

--------------------------------

### Demo Link
https://merakihrm.herokuapp.com/

## Getting Started

In the project you have purchase, you get backend project using **Node Js** and frontend project using **React Js**.
This project is using **Mongod DB** as database service. Before start running the project first you must make sure already installed:
- Node - https://nodejs.org/en/
- Mongo DB - https://docs.mongodb.com/manual/installation/

## Environment

Change value inside ```.env``` to your configuration.

## Install Dependencies

```shell
npm install
```

or

```shell
yarn
```

## Run project backend

```shell
npm run start
```

or

```shell
yarn start
```

### Backend running on http://localhost:5000.
