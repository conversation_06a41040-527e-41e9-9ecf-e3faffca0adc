{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\DayWorkSchedule.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Avatar, Box, Typography, IconButton, Tooltip, Button } from '@mui/material';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport SettingsIcon from '@mui/icons-material/Settings';\nimport dayjs from 'dayjs';\nimport PropTypes from 'prop-types';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { UserActions } from 'slices/actions';\nimport { UserSelector } from 'selectors';\nimport DayWorkScheduleForm from './DayWorkScheduleForm';\nimport TimeSpecificScheduleForm from './TimeSpecificScheduleForm';\nimport ScheduleManager from './ScheduleManager';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst hours = Array.from({\n  length: 24\n}, (_, i) => `${i}:00`);\nconst SLOT_WIDTH = 60;\nconst USER_WIDTH = 200;\nconst ROW_HEIGHT = 60;\nconst DayWorkSchedule = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const [open, setOpen] = useState(false);\n  const [selected, setSelected] = useState(null);\n  const [showScheduleManager, setShowScheduleManager] = useState(false);\n  const [managerUser, setManagerUser] = useState(null);\n  const [showTimeSpecific, setShowTimeSpecific] = useState(false);\n  const [timeSpecificData, setTimeSpecificData] = useState(null);\n\n  // Get the current date from dateRange or default to today\n  const currentDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? dayjs(dateRange.startDate) : dayjs();\n  const isDateRange = (dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate) !== (dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate);\n\n  // Fetch users when component mounts\n  useEffect(() => {\n    dispatch(UserActions.getUsers());\n  }, [dispatch]);\n  const handleClick = (user, hour) => {\n    // Open time-specific schedule form for hour clicks\n    setTimeSpecificData({\n      user,\n      hour,\n      date: currentDate.format('YYYY-MM-DD')\n    });\n    setShowTimeSpecific(true);\n  };\n  const handleClose = () => {\n    setOpen(false);\n    setSelected(null);\n  };\n  const handleOpenScheduleManager = user => {\n    setManagerUser(user);\n    setShowScheduleManager(true);\n  };\n  const handleCloseScheduleManager = () => {\n    setShowScheduleManager(false);\n    setManagerUser(null);\n  };\n\n  // Format the date display\n  const getDateDisplayText = () => {\n    if (isDateRange && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      const endDate = dayjs(dateRange.endDate);\n      if (currentDate.month() === endDate.month() && currentDate.year() === endDate.year()) {\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"D, YYYY\")}`;\n      } else if (currentDate.year() === endDate.year()) {\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"MMM D, YYYY\")}`;\n      } else {\n        return `${currentDate.format(\"MMM D, YYYY\")} - ${endDate.format(\"MMM D, YYYY\")}`;\n      }\n    } else {\n      return currentDate.format(\"dddd, MMMM D, YYYY\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        children: [\"Day Work Schedule - \", getDateDisplayText()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        size: \"small\",\n        startIcon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 22\n        }, this),\n        onClick: () => users.length > 0 && handleOpenScheduleManager(users[0]),\n        disabled: users.length === 0,\n        children: \"Manage Schedules\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        overflowX: 'auto',\n        border: '1px solid #ccc',\n        borderRadius: 1,\n        whiteSpace: 'nowrap',\n        '&::-webkit-scrollbar': {\n          height: 8\n        },\n        '&::-webkit-scrollbar-thumb': {\n          backgroundColor: '#999',\n          borderRadius: 4\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minWidth: `${USER_WIDTH + hours.length * SLOT_WIDTH}px`\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            position: 'sticky',\n            top: 0,\n            zIndex: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: USER_WIDTH,\n              height: ROW_HEIGHT,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontWeight: 600,\n              fontSize: 13,\n              borderRight: '1px solid #ccc',\n              borderBottom: '1px solid #ccc',\n              backgroundColor: '#f0f0f0',\n              position: 'sticky',\n              left: 0,\n              zIndex: 3\n            },\n            children: \"User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this), hours.map((hour, idx) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: SLOT_WIDTH,\n              height: ROW_HEIGHT,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: 12,\n              fontWeight: 600,\n              borderRight: '1px solid #ccc',\n              borderBottom: '1px solid #ccc',\n              backgroundColor: '#f0f0f0'\n            },\n            children: hour\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxHeight: 400,\n            overflowY: 'auto'\n          },\n          children: users.map((user, uIdx) => {\n            var _user$designation;\n            return /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: USER_WIDTH,\n                  minHeight: ROW_HEIGHT,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  px: 2,\n                  backgroundColor: '#fff',\n                  borderRight: '1px solid #eee',\n                  borderBottom: '1px solid #eee',\n                  position: 'sticky',\n                  left: 0,\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 32,\n                    height: 32\n                  },\n                  children: user.name ? user.name[0].toUpperCase() : 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    fontWeight: 600,\n                    fontSize: 13,\n                    children: user.name || 'Unknown User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: ((_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation.name) || user.role || 'No Role'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 191,\n                    columnNumber: 21\n                  }, this), user.workSchedule && (user.workSchedule.startTime !== '09:00' || user.workSchedule.endTime !== '17:30' || user.workSchedule.scheduleTemplate !== 'day_shift') && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"primary\",\n                    sx: {\n                      display: 'block'\n                    },\n                    children: [user.workSchedule.startTime, \"-\", user.workSchedule.endTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 17\n              }, this), hours.map((hour, hIdx) => {\n                var _user$workSchedule, _user$workSchedule2;\n                // Check if user has custom work schedule for this time\n                const hasCustomSchedule = user.workSchedule && (user.workSchedule.startTime !== '09:00' || user.workSchedule.endTime !== '17:30' || user.workSchedule.scheduleTemplate !== 'day_shift');\n\n                // Check if this hour falls within user's work schedule\n                const hourNum = parseInt(hour.split(':')[0], 10);\n                const startHour = (_user$workSchedule = user.workSchedule) !== null && _user$workSchedule !== void 0 && _user$workSchedule.startTime ? parseInt(user.workSchedule.startTime.split(':')[0], 10) : 9;\n                const endHour = (_user$workSchedule2 = user.workSchedule) !== null && _user$workSchedule2 !== void 0 && _user$workSchedule2.endTime ? parseInt(user.workSchedule.endTime.split(':')[0], 10) : 17;\n                const isWorkingHour = hourNum >= startHour && hourNum < endHour;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: SLOT_WIDTH,\n                    height: ROW_HEIGHT,\n                    borderRight: '1px solid #eee',\n                    borderBottom: '1px solid #eee',\n                    position: 'relative',\n                    backgroundColor: hasCustomSchedule && isWorkingHour ? '#e3f2fd' : '#fafafa',\n                    '&:hover .add-icon': {\n                      opacity: 1\n                    },\n                    '&:hover .schedule-info': {\n                      opacity: 1\n                    }\n                  },\n                  children: [hasCustomSchedule && isWorkingHour && /*#__PURE__*/_jsxDEV(Box, {\n                    className: \"schedule-info\",\n                    sx: {\n                      position: 'absolute',\n                      top: 2,\n                      left: 2,\n                      right: 2,\n                      fontSize: '8px',\n                      color: 'primary.main',\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      opacity: 0.7,\n                      transition: 'opacity 0.3s',\n                      lineHeight: 1,\n                      overflow: 'hidden'\n                    },\n                    children: [user.workSchedule.scheduleTemplate === 'night_shift' ? 'Night' : 'Day', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 27\n                    }, this), user.workSchedule.startTime, \"-\", user.workSchedule.endTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: hasCustomSchedule && isWorkingHour ? `${user.workSchedule.scheduleTemplate} (${user.workSchedule.startTime}-${user.workSchedule.endTime})` : \"Add schedule\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      className: \"add-icon\",\n                      size: \"small\",\n                      sx: {\n                        position: 'absolute',\n                        bottom: 2,\n                        right: 2,\n                        opacity: 0,\n                        transition: 'opacity 0.3s',\n                        '& .MuiSvgIcon-root': {\n                          fontSize: '12px'\n                        }\n                      },\n                      onClick: () => handleClick(user, hour),\n                      children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 279,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)]\n                }, hIdx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, uIdx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 8\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DayWorkScheduleForm, {\n      open: open,\n      onClose: handleClose,\n      selectedUser: selected === null || selected === void 0 ? void 0 : selected.user,\n      selectedDate: selected === null || selected === void 0 ? void 0 : selected.date,\n      selectedHour: selected === null || selected === void 0 ? void 0 : selected.hour\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ScheduleManager, {\n      open: showScheduleManager,\n      onClose: handleCloseScheduleManager,\n      selectedUser: managerUser,\n      selectedDate: currentDate.format('YYYY-MM-DD')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(DayWorkSchedule, \"uiA3kJvlBsP3OkKb2Gtt2FgeP98=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DayWorkSchedule;\nDayWorkSchedule.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default DayWorkSchedule;\nvar _c;\n$RefreshReg$(_c, \"DayWorkSchedule\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Avatar", "Box", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "AddCircleOutlineIcon", "SettingsIcon", "dayjs", "PropTypes", "useDispatch", "useSelector", "UserActions", "UserSelector", "DayWorkScheduleForm", "TimeSpecificScheduleForm", "ScheduleManager", "jsxDEV", "_jsxDEV", "hours", "Array", "from", "length", "_", "i", "SLOT_WIDTH", "USER_WIDTH", "ROW_HEIGHT", "DayWorkSchedule", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "users", "getUsers", "open", "<PERSON><PERSON><PERSON>", "selected", "setSelected", "showScheduleManager", "setShowScheduleManager", "manager<PERSON>ser", "setManagerUser", "showTimeSpecific", "setShowTimeSpecific", "timeSpecificData", "setTimeSpecificData", "currentDate", "startDate", "isDateRange", "endDate", "handleClick", "user", "hour", "date", "format", "handleClose", "handleOpenScheduleManager", "handleCloseScheduleManager", "getDateDisplayText", "month", "year", "p", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "startIcon", "onClick", "disabled", "width", "overflowX", "border", "borderRadius", "whiteSpace", "height", "backgroundColor", "min<PERSON><PERSON><PERSON>", "position", "top", "zIndex", "fontWeight", "fontSize", "borderRight", "borderBottom", "left", "map", "idx", "maxHeight", "overflowY", "uIdx", "_user$designation", "minHeight", "gap", "px", "name", "toUpperCase", "color", "designation", "role", "workSchedule", "startTime", "endTime", "scheduleTemplate", "hIdx", "_user$workSchedule", "_user$workSchedule2", "hasCustomSchedule", "hourNum", "parseInt", "split", "startHour", "endHour", "isWorkingHour", "opacity", "className", "right", "textAlign", "transition", "lineHeight", "overflow", "title", "bottom", "onClose", "selected<PERSON>ser", "selectedDate", "selected<PERSON>our", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/DayWorkSchedule.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Avatar,\r\n  Box,\r\n  Typography,\r\n  IconButton,\r\n  Tooltip,\r\n  Button\r\n} from '@mui/material';\r\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\r\nimport SettingsIcon from '@mui/icons-material/Settings';\r\nimport dayjs from 'dayjs';\r\nimport PropTypes from 'prop-types';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { UserActions } from 'slices/actions';\r\nimport { UserSelector } from 'selectors';\r\nimport DayWorkScheduleForm from './DayWorkScheduleForm';\r\nimport TimeSpecificScheduleForm from './TimeSpecificScheduleForm';\r\nimport ScheduleManager from './ScheduleManager';\r\n\r\nconst hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);\r\n\r\nconst SLOT_WIDTH = 60;\r\nconst USER_WIDTH = 200;\r\nconst ROW_HEIGHT = 60;\r\n\r\nconst DayWorkSchedule = ({ dateRange }) => {\r\n  const dispatch = useDispatch();\r\n  const users = useSelector(UserSelector.getUsers());\r\n  const [open, setOpen] = useState(false);\r\n  const [selected, setSelected] = useState(null);\r\n  const [showScheduleManager, setShowScheduleManager] = useState(false);\r\n  const [managerUser, setManagerUser] = useState(null);\r\n  const [showTimeSpecific, setShowTimeSpecific] = useState(false);\r\n  const [timeSpecificData, setTimeSpecificData] = useState(null);\r\n\r\n  // Get the current date from dateRange or default to today\r\n  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();\r\n  const isDateRange = dateRange?.startDate !== dateRange?.endDate;\r\n\r\n  // Fetch users when component mounts\r\n  useEffect(() => {\r\n    dispatch(UserActions.getUsers());\r\n  }, [dispatch]);\r\n\r\n  const handleClick = (user, hour) => {\r\n    // Open time-specific schedule form for hour clicks\r\n    setTimeSpecificData({\r\n      user,\r\n      hour,\r\n      date: currentDate.format('YYYY-MM-DD')\r\n    });\r\n    setShowTimeSpecific(true);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setOpen(false);\r\n    setSelected(null);\r\n  };\r\n\r\n  const handleOpenScheduleManager = (user) => {\r\n    setManagerUser(user);\r\n    setShowScheduleManager(true);\r\n  };\r\n\r\n  const handleCloseScheduleManager = () => {\r\n    setShowScheduleManager(false);\r\n    setManagerUser(null);\r\n  };\r\n\r\n  // Format the date display\r\n  const getDateDisplayText = () => {\r\n    if (isDateRange && dateRange?.endDate) {\r\n      const endDate = dayjs(dateRange.endDate);\r\n      if (currentDate.month() === endDate.month() && currentDate.year() === endDate.year()) {\r\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"D, YYYY\")}`;\r\n      } else if (currentDate.year() === endDate.year()) {\r\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"MMM D, YYYY\")}`;\r\n      } else {\r\n        return `${currentDate.format(\"MMM D, YYYY\")} - ${endDate.format(\"MMM D, YYYY\")}`;\r\n      }\r\n    } else {\r\n      return currentDate.format(\"dddd, MMMM D, YYYY\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Box p={3}>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\r\n        <Typography variant=\"h5\">\r\n          Day Work Schedule - {getDateDisplayText()}\r\n        </Typography>\r\n        <Button\r\n          variant=\"outlined\"\r\n          size=\"small\"\r\n          startIcon={<SettingsIcon />}\r\n          onClick={() => users.length > 0 && handleOpenScheduleManager(users[0])}\r\n          disabled={users.length === 0}\r\n        >\r\n          Manage Schedules\r\n        </Button>\r\n      </Box>\r\n\r\n      {/* Scrollable table wrapper */}\r\n      <Box\r\n        sx={{\r\n          width: '100%',\r\n          overflowX: 'auto',\r\n          border: '1px solid #ccc',\r\n          borderRadius: 1,\r\n          whiteSpace: 'nowrap',\r\n          '&::-webkit-scrollbar': {\r\n            height: 8\r\n          },\r\n          '&::-webkit-scrollbar-thumb': {\r\n            backgroundColor: '#999',\r\n            borderRadius: 4\r\n          }\r\n        }}\r\n      >\r\n       <Box sx={{ minWidth: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px` }}>\r\n\r\n          {/* Header */}\r\n          <Box sx={{ display: 'flex', position: 'sticky', top: 0, zIndex: 2 }}>\r\n            <Box\r\n              sx={{\r\n                width: USER_WIDTH,\r\n                height: ROW_HEIGHT,\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                fontWeight: 600,\r\n                fontSize: 13,\r\n                borderRight: '1px solid #ccc',\r\n                borderBottom: '1px solid #ccc',\r\n                backgroundColor: '#f0f0f0',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 3\r\n              }}\r\n            >\r\n              User\r\n            </Box>\r\n            {hours.map((hour, idx) => (\r\n              <Box\r\n                key={idx}\r\n                sx={{\r\n                  width: SLOT_WIDTH,\r\n                  height: ROW_HEIGHT,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  fontSize: 12,\r\n                  fontWeight: 600,\r\n                  borderRight: '1px solid #ccc',\r\n                  borderBottom: '1px solid #ccc',\r\n                  backgroundColor: '#f0f0f0'\r\n                }}\r\n              >\r\n                {hour}\r\n              </Box>\r\n            ))}\r\n          </Box>\r\n\r\n          {/* User Rows */}\r\n          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>\r\n            {users.map((user, uIdx) => (\r\n              <Box key={uIdx} sx={{ display: 'flex' }}>\r\n                {/* User Info */}\r\n                <Box\r\n                  sx={{\r\n                    width: USER_WIDTH,\r\n                    minHeight: ROW_HEIGHT,\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    gap: 1,\r\n                    px: 2,\r\n                    backgroundColor: '#fff',\r\n                    borderRight: '1px solid #eee',\r\n                    borderBottom: '1px solid #eee',\r\n                    position: 'sticky',\r\n                    left: 0,\r\n                    zIndex: 1\r\n                  }}\r\n                > \r\n                  <Avatar sx={{ width: 32, height: 32 }}>\r\n                    {user.name ? user.name[0].toUpperCase() : 'U'}\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Typography fontWeight={600} fontSize={13}>{user.name || 'Unknown User'}</Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      {user.designation?.name || user.role || 'No Role'}\r\n                    </Typography>\r\n                    {/* Show work schedule indicator if different from default */}\r\n                    {user.workSchedule && (\r\n                      user.workSchedule.startTime !== '09:00' ||\r\n                      user.workSchedule.endTime !== '17:30' ||\r\n                      user.workSchedule.scheduleTemplate !== 'day_shift'\r\n                    ) && (\r\n                      <Typography variant=\"caption\" color=\"primary\" sx={{ display: 'block' }}>\r\n                        {user.workSchedule.startTime}-{user.workSchedule.endTime}\r\n                      </Typography>\r\n                    )}\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Time Slots */}\r\n                {hours.map((hour, hIdx) => {\r\n                  // Check if user has custom work schedule for this time\r\n                  const hasCustomSchedule = user.workSchedule && (\r\n                    user.workSchedule.startTime !== '09:00' ||\r\n                    user.workSchedule.endTime !== '17:30' ||\r\n                    user.workSchedule.scheduleTemplate !== 'day_shift'\r\n                  );\r\n\r\n// Check if this hour falls within user's work schedule\r\nconst hourNum = parseInt(hour.split(':')[0], 10);\r\nconst startHour = user.workSchedule?.startTime ? parseInt(user.workSchedule.startTime.split(':')[0], 10) : 9;\r\nconst endHour = user.workSchedule?.endTime ? parseInt(user.workSchedule.endTime.split(':')[0], 10) : 17;\r\nconst isWorkingHour = hourNum >= startHour && hourNum < endHour;\r\n\r\n\r\n                  return (\r\n                    <Box\r\n                      key={hIdx}\r\n                      sx={{\r\n                        width: SLOT_WIDTH,\r\n                        height: ROW_HEIGHT,\r\n                        borderRight: '1px solid #eee',\r\n                        borderBottom: '1px solid #eee',\r\n                        position: 'relative',\r\n                        backgroundColor: hasCustomSchedule && isWorkingHour ? '#e3f2fd' : '#fafafa',\r\n                        '&:hover .add-icon': { opacity: 1 },\r\n                        '&:hover .schedule-info': { opacity: 1 }\r\n                      }}\r\n                    >\r\n                      {/* Show schedule info if custom schedule exists and it's working hour */}\r\n                      {hasCustomSchedule && isWorkingHour && (\r\n                        <Box\r\n                          className=\"schedule-info\"\r\n                          sx={{\r\n                            position: 'absolute',\r\n                            top: 2,\r\n                            left: 2,\r\n                            right: 2,\r\n                            fontSize: '8px',\r\n                            color: 'primary.main',\r\n                            fontWeight: 600,\r\n                            textAlign: 'center',\r\n                            opacity: 0.7,\r\n                            transition: 'opacity 0.3s',\r\n                            lineHeight: 1,\r\n                            overflow: 'hidden'\r\n                          }}\r\n                        >\r\n                          {user.workSchedule.scheduleTemplate === 'night_shift' ? 'Night' : 'Day'}\r\n                          <br />\r\n                          {user.workSchedule.startTime}-{user.workSchedule.endTime}\r\n                        </Box>\r\n                      )}\r\n\r\n                      <Tooltip title={hasCustomSchedule && isWorkingHour ? `${user.workSchedule.scheduleTemplate} (${user.workSchedule.startTime}-${user.workSchedule.endTime})` : \"Add schedule\"\r\n                      }>\r\n                        <IconButton\r\n                          className=\"add-icon\"\r\n                          size=\"small\"\r\n                          sx={{\r\n                            position: 'absolute',\r\n                            bottom: 2,\r\n                            right: 2,\r\n                            opacity: 0,\r\n                            transition: 'opacity 0.3s',\r\n                            '& .MuiSvgIcon-root': {\r\n                              fontSize: '12px'\r\n                            }\r\n                          }}\r\n                          onClick={() => handleClick(user, hour)}\r\n                        >\r\n                          <AddCircleOutlineIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                    </Box>\r\n                  );\r\n                })}\r\n              </Box>\r\n            ))}\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Work Schedule Form */}\r\n      <DayWorkScheduleForm\r\n        open={open}\r\n        onClose={handleClose}\r\n        selectedUser={selected?.user}\r\n        selectedDate={selected?.date}\r\n        selectedHour={selected?.hour}\r\n      />\r\n\r\n      {/* Schedule Manager */}\r\n      <ScheduleManager\r\n        open={showScheduleManager}\r\n        onClose={handleCloseScheduleManager}\r\n        selectedUser={managerUser}\r\n        selectedDate={currentDate.format('YYYY-MM-DD')}\r\n      />\r\n    </Box>\r\n  );\r\n};\r\n\r\nDayWorkSchedule.propTypes = {\r\n  dateRange: PropTypes.shape({\r\n    startDate: PropTypes.string,\r\n    endDate: PropTypes.string\r\n  })\r\n};\r\n\r\nexport default DayWorkSchedule;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,MAAM,QACD,eAAe;AACtB,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,KAAK,CAAC;AAE7D,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,UAAU,GAAG,GAAG;AACtB,MAAMC,UAAU,GAAG,EAAE;AAErB,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,KAAK,GAAGrB,WAAW,CAACE,YAAY,CAACoB,QAAQ,CAAC,CAAC,CAAC;EAClD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACwC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMgD,WAAW,GAAGjB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEkB,SAAS,GAAGvC,KAAK,CAACqB,SAAS,CAACkB,SAAS,CAAC,GAAGvC,KAAK,CAAC,CAAC;EAC/E,MAAMwC,WAAW,GAAG,CAAAnB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkB,SAAS,OAAKlB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEoB,OAAO;;EAE/D;EACAlD,SAAS,CAAC,MAAM;IACdgC,QAAQ,CAACnB,WAAW,CAACqB,QAAQ,CAAC,CAAC,CAAC;EAClC,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;EAEd,MAAMmB,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClC;IACAP,mBAAmB,CAAC;MAClBM,IAAI;MACJC,IAAI;MACJC,IAAI,EAAEP,WAAW,CAACQ,MAAM,CAAC,YAAY;IACvC,CAAC,CAAC;IACFX,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACxBpB,OAAO,CAAC,KAAK,CAAC;IACdE,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;EAED,MAAMmB,yBAAyB,GAAIL,IAAI,IAAK;IAC1CV,cAAc,CAACU,IAAI,CAAC;IACpBZ,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMkB,0BAA0B,GAAGA,CAAA,KAAM;IACvClB,sBAAsB,CAAC,KAAK,CAAC;IAC7BE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIV,WAAW,IAAInB,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEoB,OAAO,EAAE;MACrC,MAAMA,OAAO,GAAGzC,KAAK,CAACqB,SAAS,CAACoB,OAAO,CAAC;MACxC,IAAIH,WAAW,CAACa,KAAK,CAAC,CAAC,KAAKV,OAAO,CAACU,KAAK,CAAC,CAAC,IAAIb,WAAW,CAACc,IAAI,CAAC,CAAC,KAAKX,OAAO,CAACW,IAAI,CAAC,CAAC,EAAE;QACpF,OAAO,GAAGd,WAAW,CAACQ,MAAM,CAAC,OAAO,CAAC,MAAML,OAAO,CAACK,MAAM,CAAC,SAAS,CAAC,EAAE;MACxE,CAAC,MAAM,IAAIR,WAAW,CAACc,IAAI,CAAC,CAAC,KAAKX,OAAO,CAACW,IAAI,CAAC,CAAC,EAAE;QAChD,OAAO,GAAGd,WAAW,CAACQ,MAAM,CAAC,OAAO,CAAC,MAAML,OAAO,CAACK,MAAM,CAAC,aAAa,CAAC,EAAE;MAC5E,CAAC,MAAM;QACL,OAAO,GAAGR,WAAW,CAACQ,MAAM,CAAC,aAAa,CAAC,MAAML,OAAO,CAACK,MAAM,CAAC,aAAa,CAAC,EAAE;MAClF;IACF,CAAC,MAAM;MACL,OAAOR,WAAW,CAACQ,MAAM,CAAC,oBAAoB,CAAC;IACjD;EACF,CAAC;EAED,oBACEpC,OAAA,CAACjB,GAAG;IAAC4D,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACR5C,OAAA,CAACjB,GAAG;MAAC8D,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzF5C,OAAA,CAAChB,UAAU;QAACkE,OAAO,EAAC,IAAI;QAAAN,QAAA,GAAC,sBACH,EAACJ,kBAAkB,CAAC,CAAC;MAAA;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACbtD,OAAA,CAACb,MAAM;QACL+D,OAAO,EAAC,UAAU;QAClBK,IAAI,EAAC,OAAO;QACZC,SAAS,eAAExD,OAAA,CAACX,YAAY;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC5BG,OAAO,EAAEA,CAAA,KAAM3C,KAAK,CAACV,MAAM,GAAG,CAAC,IAAIkC,yBAAyB,CAACxB,KAAK,CAAC,CAAC,CAAC,CAAE;QACvE4C,QAAQ,EAAE5C,KAAK,CAACV,MAAM,KAAK,CAAE;QAAAwC,QAAA,EAC9B;MAED;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNtD,OAAA,CAACjB,GAAG;MACF8D,EAAE,EAAE;QACFc,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,QAAQ;QACpB,sBAAsB,EAAE;UACtBC,MAAM,EAAE;QACV,CAAC;QACD,4BAA4B,EAAE;UAC5BC,eAAe,EAAE,MAAM;UACvBH,YAAY,EAAE;QAChB;MACF,CAAE;MAAAlB,QAAA,eAEH5C,OAAA,CAACjB,GAAG;QAAC8D,EAAE,EAAE;UAAEqB,QAAQ,EAAE,GAAG1D,UAAU,GAAIP,KAAK,CAACG,MAAM,GAAGG,UAAW;QAAK,CAAE;QAAAqC,QAAA,gBAGpE5C,OAAA,CAACjB,GAAG;UAAC8D,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEqB,QAAQ,EAAE,QAAQ;YAAEC,GAAG,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAAzB,QAAA,gBAClE5C,OAAA,CAACjB,GAAG;YACF8D,EAAE,EAAE;cACFc,KAAK,EAAEnD,UAAU;cACjBwD,MAAM,EAAEvD,UAAU;cAClBqC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxBuB,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,EAAE;cACZC,WAAW,EAAE,gBAAgB;cAC7BC,YAAY,EAAE,gBAAgB;cAC9BR,eAAe,EAAE,SAAS;cAC1BE,QAAQ,EAAE,QAAQ;cAClBO,IAAI,EAAE,CAAC;cACPL,MAAM,EAAE;YACV,CAAE;YAAAzB,QAAA,EACH;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACLrD,KAAK,CAAC0E,GAAG,CAAC,CAACzC,IAAI,EAAE0C,GAAG,kBACnB5E,OAAA,CAACjB,GAAG;YAEF8D,EAAE,EAAE;cACFc,KAAK,EAAEpD,UAAU;cACjByD,MAAM,EAAEvD,UAAU;cAClBqC,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxBwB,QAAQ,EAAE,EAAE;cACZD,UAAU,EAAE,GAAG;cACfE,WAAW,EAAE,gBAAgB;cAC7BC,YAAY,EAAE,gBAAgB;cAC9BR,eAAe,EAAE;YACnB,CAAE;YAAArB,QAAA,EAEDV;UAAI,GAdA0C,GAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeL,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtD,OAAA,CAACjB,GAAG;UAAC8D,EAAE,EAAE;YAAEgC,SAAS,EAAE,GAAG;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAlC,QAAA,EAC5C9B,KAAK,CAAC6D,GAAG,CAAC,CAAC1C,IAAI,EAAE8C,IAAI;YAAA,IAAAC,iBAAA;YAAA,oBACpBhF,OAAA,CAACjB,GAAG;cAAY8D,EAAE,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAAAF,QAAA,gBAEtC5C,OAAA,CAACjB,GAAG;gBACF8D,EAAE,EAAE;kBACFc,KAAK,EAAEnD,UAAU;kBACjByE,SAAS,EAAExE,UAAU;kBACrBqC,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBkC,GAAG,EAAE,CAAC;kBACNC,EAAE,EAAE,CAAC;kBACLlB,eAAe,EAAE,MAAM;kBACvBO,WAAW,EAAE,gBAAgB;kBAC7BC,YAAY,EAAE,gBAAgB;kBAC9BN,QAAQ,EAAE,QAAQ;kBAClBO,IAAI,EAAE,CAAC;kBACPL,MAAM,EAAE;gBACV,CAAE;gBAAAzB,QAAA,gBAEF5C,OAAA,CAAClB,MAAM;kBAAC+D,EAAE,EAAE;oBAAEc,KAAK,EAAE,EAAE;oBAAEK,MAAM,EAAE;kBAAG,CAAE;kBAAApB,QAAA,EACnCX,IAAI,CAACmD,IAAI,GAAGnD,IAAI,CAACmD,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;gBAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACTtD,OAAA,CAACjB,GAAG;kBAAA6D,QAAA,gBACF5C,OAAA,CAAChB,UAAU;oBAACsF,UAAU,EAAE,GAAI;oBAACC,QAAQ,EAAE,EAAG;oBAAA3B,QAAA,EAAEX,IAAI,CAACmD,IAAI,IAAI;kBAAc;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACrFtD,OAAA,CAAChB,UAAU;oBAACkE,OAAO,EAAC,SAAS;oBAACoC,KAAK,EAAC,gBAAgB;oBAAA1C,QAAA,EACjD,EAAAoC,iBAAA,GAAA/C,IAAI,CAACsD,WAAW,cAAAP,iBAAA,uBAAhBA,iBAAA,CAAkBI,IAAI,KAAInD,IAAI,CAACuD,IAAI,IAAI;kBAAS;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,EAEZrB,IAAI,CAACwD,YAAY,KAChBxD,IAAI,CAACwD,YAAY,CAACC,SAAS,KAAK,OAAO,IACvCzD,IAAI,CAACwD,YAAY,CAACE,OAAO,KAAK,OAAO,IACrC1D,IAAI,CAACwD,YAAY,CAACG,gBAAgB,KAAK,WAAW,CACnD,iBACC5F,OAAA,CAAChB,UAAU;oBAACkE,OAAO,EAAC,SAAS;oBAACoC,KAAK,EAAC,SAAS;oBAACzC,EAAE,EAAE;sBAAEC,OAAO,EAAE;oBAAQ,CAAE;oBAAAF,QAAA,GACpEX,IAAI,CAACwD,YAAY,CAACC,SAAS,EAAC,GAAC,EAACzD,IAAI,CAACwD,YAAY,CAACE,OAAO;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLrD,KAAK,CAAC0E,GAAG,CAAC,CAACzC,IAAI,EAAE2D,IAAI,KAAK;gBAAA,IAAAC,kBAAA,EAAAC,mBAAA;gBACzB;gBACA,MAAMC,iBAAiB,GAAG/D,IAAI,CAACwD,YAAY,KACzCxD,IAAI,CAACwD,YAAY,CAACC,SAAS,KAAK,OAAO,IACvCzD,IAAI,CAACwD,YAAY,CAACE,OAAO,KAAK,OAAO,IACrC1D,IAAI,CAACwD,YAAY,CAACG,gBAAgB,KAAK,WAAW,CACnD;;gBAEnB;gBACA,MAAMK,OAAO,GAAGC,QAAQ,CAAChE,IAAI,CAACiE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAChD,MAAMC,SAAS,GAAG,CAAAN,kBAAA,GAAA7D,IAAI,CAACwD,YAAY,cAAAK,kBAAA,eAAjBA,kBAAA,CAAmBJ,SAAS,GAAGQ,QAAQ,CAACjE,IAAI,CAACwD,YAAY,CAACC,SAAS,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;gBAC5G,MAAME,OAAO,GAAG,CAAAN,mBAAA,GAAA9D,IAAI,CAACwD,YAAY,cAAAM,mBAAA,eAAjBA,mBAAA,CAAmBJ,OAAO,GAAGO,QAAQ,CAACjE,IAAI,CAACwD,YAAY,CAACE,OAAO,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;gBACvG,MAAMG,aAAa,GAAGL,OAAO,IAAIG,SAAS,IAAIH,OAAO,GAAGI,OAAO;gBAG7C,oBACErG,OAAA,CAACjB,GAAG;kBAEF8D,EAAE,EAAE;oBACFc,KAAK,EAAEpD,UAAU;oBACjByD,MAAM,EAAEvD,UAAU;oBAClB+D,WAAW,EAAE,gBAAgB;oBAC7BC,YAAY,EAAE,gBAAgB;oBAC9BN,QAAQ,EAAE,UAAU;oBACpBF,eAAe,EAAE+B,iBAAiB,IAAIM,aAAa,GAAG,SAAS,GAAG,SAAS;oBAC3E,mBAAmB,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAC;oBACnC,wBAAwB,EAAE;sBAAEA,OAAO,EAAE;oBAAE;kBACzC,CAAE;kBAAA3D,QAAA,GAGDoD,iBAAiB,IAAIM,aAAa,iBACjCtG,OAAA,CAACjB,GAAG;oBACFyH,SAAS,EAAC,eAAe;oBACzB3D,EAAE,EAAE;sBACFsB,QAAQ,EAAE,UAAU;sBACpBC,GAAG,EAAE,CAAC;sBACNM,IAAI,EAAE,CAAC;sBACP+B,KAAK,EAAE,CAAC;sBACRlC,QAAQ,EAAE,KAAK;sBACfe,KAAK,EAAE,cAAc;sBACrBhB,UAAU,EAAE,GAAG;sBACfoC,SAAS,EAAE,QAAQ;sBACnBH,OAAO,EAAE,GAAG;sBACZI,UAAU,EAAE,cAAc;sBAC1BC,UAAU,EAAE,CAAC;sBACbC,QAAQ,EAAE;oBACZ,CAAE;oBAAAjE,QAAA,GAEDX,IAAI,CAACwD,YAAY,CAACG,gBAAgB,KAAK,aAAa,GAAG,OAAO,GAAG,KAAK,eACvE5F,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACLrB,IAAI,CAACwD,YAAY,CAACC,SAAS,EAAC,GAAC,EAACzD,IAAI,CAACwD,YAAY,CAACE,OAAO;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CACN,eAEDtD,OAAA,CAACd,OAAO;oBAAC4H,KAAK,EAAEd,iBAAiB,IAAIM,aAAa,GAAG,GAAGrE,IAAI,CAACwD,YAAY,CAACG,gBAAgB,KAAK3D,IAAI,CAACwD,YAAY,CAACC,SAAS,IAAIzD,IAAI,CAACwD,YAAY,CAACE,OAAO,GAAG,GAAG,cAC5J;oBAAA/C,QAAA,eACC5C,OAAA,CAACf,UAAU;sBACTuH,SAAS,EAAC,UAAU;sBACpBjD,IAAI,EAAC,OAAO;sBACZV,EAAE,EAAE;wBACFsB,QAAQ,EAAE,UAAU;wBACpB4C,MAAM,EAAE,CAAC;wBACTN,KAAK,EAAE,CAAC;wBACRF,OAAO,EAAE,CAAC;wBACVI,UAAU,EAAE,cAAc;wBAC1B,oBAAoB,EAAE;0BACpBpC,QAAQ,EAAE;wBACZ;sBACF,CAAE;sBACFd,OAAO,EAAEA,CAAA,KAAMzB,WAAW,CAACC,IAAI,EAAEC,IAAI,CAAE;sBAAAU,QAAA,eAEvC5C,OAAA,CAACZ,oBAAoB;wBAACmF,QAAQ,EAAC;sBAAO;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GAxDLuC,IAAI;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyDN,CAAC;cAEV,CAAC,CAAC;YAAA,GApHMyB,IAAI;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqHT,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA,CAACJ,mBAAmB;MAClBoB,IAAI,EAAEA,IAAK;MACXgG,OAAO,EAAE3E,WAAY;MACrB4E,YAAY,EAAE/F,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,IAAK;MAC7BiF,YAAY,EAAEhG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,IAAK;MAC7BgF,YAAY,EAAEjG,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgB;IAAK;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,eAGFtD,OAAA,CAACF,eAAe;MACdkB,IAAI,EAAEI,mBAAoB;MAC1B4F,OAAO,EAAEzE,0BAA2B;MACpC0E,YAAY,EAAE3F,WAAY;MAC1B4F,YAAY,EAAEtF,WAAW,CAACQ,MAAM,CAAC,YAAY;IAAE;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA1RIF,eAAe;EAAA,QACFlB,WAAW,EACdC,WAAW;AAAA;AAAA2H,EAAA,GAFrB1G,eAAe;AA4RrBA,eAAe,CAAC2G,SAAS,GAAG;EAC1B1G,SAAS,EAAEpB,SAAS,CAAC+H,KAAK,CAAC;IACzBzF,SAAS,EAAEtC,SAAS,CAACgI,MAAM;IAC3BxF,OAAO,EAAExC,SAAS,CAACgI;EACrB,CAAC;AACH,CAAC;AAED,eAAe7G,eAAe;AAAC,IAAA0G,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}