/**
 * Sprint Selectors
 * 
 * This module provides selector functions for accessing sprint-related state from the Redux store.
 */

/**
 * Get all sprints from the state
 * 
 * @param {Object} state - Redux state
 * @returns {Array} - List of sprints
 */
export const getSprints = (state) => state.sprint.sprints;

/**
 * Get a specific sprint from the state
 * 
 * @param {Object} state - Redux state
 * @returns {Object} - Sprint details
 */
export const getSprint = (state) => state.sprint.sprint;

/**
 * Get pagination information for sprints
 * 
 * @param {Object} state - Redux state
 * @returns {Object} - Pagination details
 */
export const getSprintPagination = (state) => state.sprint.pagination;

/**
 * Check if sprints are currently loading
 * 
 * @param {Object} state - Redux state
 * @returns {boolean} - True if loading, false otherwise
 */
export const isSprintLoading = (state) => state.sprint.loading;

/**
 * Get any error related to sprint operations
 * 
 * @param {Object} state - Redux state
 * @returns {string|null} - Error message or null
 */
export const getSprintError = (state) => state.sprint.error;