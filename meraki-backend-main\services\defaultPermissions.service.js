'use strict';

/**
 * Default permissions service
 * Provides default permissions for different user roles
 *
 * NOTE: Dashboard permission is set by default for all users.
 * Employee Management permission is set by default for admin users.
 * All other permissions must be explicitly assigned through the admin interface.
 */

// Default dashboard permission for all users
const dashboardPermission = {
  feat: 'Dashboard',
  acts: ['read', 'read_all']
};

// Default setting permission for all users (read-only)
const settingPermission = {
  feat: 'Setting',
  acts: ['read']
};

// Default permissions for admin users
const adminPermissions = [
  // Employee Management permissions
  {
    feat: 'User',
    acts: ['read', 'create', 'update', 'delete']
  }
];

/**
 * Get default permissions for a new user based on their roles
 * @param {Array} roles - Array of role IDs
 * @returns {Array} Array of default permissions
 */
exports.getDefaultPermissions = (roles = []) => {
  const permissions = [dashboardPermission];
  const isAdmin = roles.includes('admin');

  if (isAdmin) {
    permissions.push({
      feat: 'Setting',
      acts: ['read', 'update']
    });
    permissions.push(...adminPermissions);
  } else {
    permissions.push(settingPermission);
  }

  return permissions;
};

/**
 * Check if a user has the required permissions
 * @param {Array} userPermissions - User's permissions array
 * @param {String} feature - Feature to check
 * @param {String} action - Action to check
 * @returns {Boolean} True if user has permission, false otherwise
 */
exports.hasPermission = (userPermissions = [], feature, action) => {
  // Special case for dashboard - always allow access
  if (feature.toLowerCase() === 'dashboard') {
    return true;
  }

  // Normalize feature name for comparison
  const normalizedFeature = feature.toLowerCase();

  // Parent-child feature mapping for permission inheritance
  const parentFeatureMap = {
    // Leave Management children
    'leave report': 'leave',
    'approve': 'leave',

    // Project Management children
    'project overview': 'project',
    'project list': 'project',
    'project timesheet': 'project',

    // Expense Management children
    'expense report': 'expense',
    'expense approval': 'expense',

    // Add more parent-child relationships as needed
  };

  // Check if user has the exact permission
  const hasDirectPermission = userPermissions.some(p => {
    if (!p || !p.feat || !Array.isArray(p.acts)) {
      return false;
    }

    // Normalize permission feature name
    const normalizedPermFeature = p.feat.toLowerCase();

    // Check if feature matches
    const featureMatches =
      normalizedPermFeature === normalizedFeature ||
      normalizedPermFeature === normalizedFeature.replace(/s$/, '') ||
      normalizedFeature === normalizedPermFeature.replace(/s$/, '') ||
      `${normalizedPermFeature}s` === normalizedFeature ||
      `${normalizedFeature}s` === normalizedPermFeature ||
      normalizedPermFeature === `my${normalizedFeature}` ||
      normalizedFeature === `my${normalizedPermFeature}` ||
      normalizedPermFeature.replace(/^my/, '') === normalizedFeature ||
      normalizedFeature.replace(/^my/, '') === normalizedPermFeature ||
      // Special case for Project/Projects
      (normalizedPermFeature === 'project' && normalizedFeature === 'projects') ||
      (normalizedPermFeature === 'projects' && normalizedFeature === 'project');

    // Check if action is allowed
    const actionAllowed = p.acts.includes(action) || p.acts.includes('*');

    // For read permissions, also allow if user has higher permissions
    if (action === 'read') {
      return featureMatches && (
        actionAllowed ||
        p.acts.includes('read_all') ||
        p.acts.includes('create') ||
        p.acts.includes('update') ||
        p.acts.includes('delete')
      );
    }

    return featureMatches && actionAllowed;
  });

  // If user has direct permission, return true
  if (hasDirectPermission) {
    return true;
  }

  // Check for parent feature permission (if this feature has a parent)
  const parentFeature = parentFeatureMap[normalizedFeature];
  if (parentFeature) {
    // Check if user has permission for the parent feature
    return userPermissions.some(p => {
      if (!p || !p.feat || !Array.isArray(p.acts)) {
        return false;
      }

      // Normalize parent permission feature name
      const normalizedParentPermFeature = p.feat.toLowerCase();

      // Check if this permission is for the parent feature
      const isParentFeature =
        normalizedParentPermFeature === parentFeature ||
        normalizedParentPermFeature === parentFeature.replace(/s$/, '') ||
        parentFeature === normalizedParentPermFeature.replace(/s$/, '') ||
        `${normalizedParentPermFeature}s` === parentFeature ||
        `${parentFeature}s` === normalizedParentPermFeature;

      if (!isParentFeature) {
        return false;
      }

      // Check if the parent permission includes this action
      const actionAllowed = p.acts.includes(action) || p.acts.includes('*');

      // For read permissions, also allow if parent has higher permissions
      if (action === 'read') {
        return actionAllowed ||
          p.acts.includes('read_all') ||
          p.acts.includes('create') ||
          p.acts.includes('update') ||
          p.acts.includes('delete');
      }

      return actionAllowed;
    });
  }

  // No permission found
  return false;
};
