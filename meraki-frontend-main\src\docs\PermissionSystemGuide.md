# Meraki HR System - Permission System Guide

## Introduction

This guide provides a comprehensive overview of the permission system in the Meraki HR application. It explains how permissions are structured, how they relate to menus and routes, and how to configure them for different user types.

## Permission System Structure

The permission system is built around three key concepts:

1. **Features**: Specific areas or modules of the application (e.g., "User", "Attendance", "Tasks")
2. **Actions**: Operations that can be performed on features (e.g., "read", "create", "update", "delete")
3. **User Roles**: Predefined roles that determine base access levels (e.g., "Admin", "HR Manager", "Employee")

## Permission Components

### 1. Features and Actions

Features and actions are defined in `constants/permission.js`:

```javascript
export const actions = {
  readAll: 'read_all',   // Full read access to all items in a feature
  readSome: 'read_some', // Read access to a subset of items (e.g., department-specific)
  readSelf: 'read_self', // Read access only to the user's own data
  read: 'read',          // Basic read access
  create: 'create',      // Create new items
  update: 'update',      // Update existing items
  delete: 'delete'       // Delete items
};

export const features = {
  // Core features
  user: 'User',
  attendance: 'Attendance',
  expense: 'Expense',
  // ... other features
};
```

### 2. User Roles

Roles are defined in `constants/role.js`:

```javascript
const ROLES = {
  admin: { id: "admin", name: "Admin" },
  humanresource: { id: "humanresource", name: "Human Resource" },
  manager: { id: "manager", name: "Manager" },
  employee: { id: "employee", name: "Employee" }
};
```

### 3. Permission Storage

Permissions are stored in the User model:

```javascript
permissions: [
  {
    feat: String,  // Feature key
    acts: [String] // Allowed actions like ['read', 'update', 'delete']
  }
]
```

## Required Permissions for Routes

Below is a comprehensive list of all routes in the application and the permissions required to access them:

### Dashboard Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/dashboard` | Dashboard | No permission required (redirects based on role) |
| `/app/admin-dashboard` | Dashboard | `{ feat: 'Dashboard', act: 'read_all' }` |
| `/app/user-dashboard` | Dashboard | `{ feat: 'Dashboard', act: 'read' }` |

### User Management Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/user` | User | `{ feat: 'User', act: 'read' }` |
| `/app/user/create` | CreateUser | `{ feat: 'User', act: 'create' }` |
| `/app/user/update/:id` | FormUser | `{ feat: 'User', act: 'update' }` |
| `/app/user/permission/:id` | Permission | `{ feat: 'User', act: 'update' }` |

### Department Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/department` | Department | `{ feat: 'Department', act: 'read' }` |
| `/app/department/create` | FormDepartment | `{ feat: 'Department', act: 'create' }` |
| `/app/department/update/:id` | FormDepartment | `{ feat: 'Department', act: 'update' }` |

### Designation Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/designation` | Designation | `{ feat: 'Designation', act: 'read' }` |
| `/app/designation/create` | FormDesignation | `{ feat: 'Designation', act: 'create' }` |
| `/app/designation/update/:id` | FormDesignation | `{ feat: 'Designation', act: 'update' }` |

### Attendance Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/attendance` | Attendance | `{ feat: 'Attendance', act: 'read' }` |
| `/app/attendance/create` | FormAttendance | `{ feat: 'Attendance', act: 'create' }` |
| `/app/attendance/update/:id` | FormAttendance | `{ feat: 'Attendance', act: 'update' }` |

### Expense Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/expenses` | Expenses | `{ feat: 'Expense', act: 'read' }` |
| `/app/expenses/create` | FormExpenses | `{ feat: 'Expense', act: 'create' }` |
| `/app/expenses/update/:id` | FormExpenses | `{ feat: 'Expense', act: 'update' }` |
| `/app/user/expenses` | Redirect to `/app/expenses` | `{ feat: 'Expense', act: 'read' }` |
| `/app/user/expenses/create` | Redirect to `/app/expenses/create` | `{ feat: 'Expense', act: 'create' }` |
| `/app/user/expenses/update/:id` | Redirect to `/app/expenses/update/:id` | `{ feat: 'Expense', act: 'update' }` |

### Leave Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/leave` | Leaves | `{ feat: 'Leave', act: 'read_all' }` |
| `/app/user/leave` | Leaves | `{ feat: 'Leave', act: 'read' }` |
| `/app/leave/create` | FormLeave | `{ feat: 'Leave', act: 'create' }` |
| `/app/leave/update/:id` | FormLeave | `{ feat: 'Leave', act: 'update' }` |
| `/app/leave/report` | LeaveReport | `{ feat: 'Leave Report', act: 'read_all' }` |
| `/app/leave/approval` | Approval | `{ feat: 'Approve', act: 'read_all' }` |
| `/app/leave/configuration` | LeaveConfiguration | `{ feat: 'Configuration', act: 'read_all' }` |
| `/app/leave/calendar` | LeaveCalendar | `{ feat: 'Calendar', act: 'read_all' }` |

### Project Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/project/list` | ProductList | `{ feat: 'Project List', act: 'read_all' }` |
| `/app/project/overview` | ProductOverview | `{ feat: 'Project Overview', act: 'read_all' }` |
| `/app/project/timesheet` | ProductTimesheet | `{ feat: 'Project Timesheet', act: 'read_all' }` |
| `/app/project/update/:data` | TaskHistoryAdmin | `{ feat: 'Projects', act: 'update' }` |
| `/app/user/projects` | ProductListStaff | `{ feat: 'Projects', act: 'read' }` |
| `/app/user/project/overview` | ProductOverview | `{ feat: 'Project Overview', act: 'read' }` |
| `/app/user/project/timesheet` | ProductTimesheet | `{ feat: 'Project Timesheet', act: 'read' }` |

### Task Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/tasks` | Tasklist | `{ feat: 'Tasks', act: 'read' }` |
| `/app/user/tasklist/note/:data` | TaskListWithNote | `{ feat: 'Task Note', act: 'read' }` |

### Timeline Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/timeline` | Timeline | `{ feat: 'Timeline', act: 'read_all' }` |
| `/app/user/timeline` | Timeline | `{ feat: 'Timeline', act: 'read' }` |
| `/app/timeline/overview` | TimelineOverview | `{ feat: 'Overview', act: 'read_all' }` |
| `/app/timeline/request` | TimeRequest | `{ feat: 'Time Request', act: 'read_all' }` |
| `/app/timeline/taskrequest` | TaskRequest | `{ feat: 'Task Request', act: 'read_all' }` |
| `/app/timeline/workschedule` | WorkSchedule | `{ feat: 'Work Schedule', act: 'read_all' }` |

### Other Routes

| Route | Component | Required Permission |
|-------|-----------|-------------------|
| `/app/client` | Client | `{ feat: 'Client', act: 'read_all' }` |
| `/app/report` | Report | `{ feat: 'Report', act: 'read' }` |
| `/app/setting` | Setting | `{ feat: 'Setting', act: 'read' }` |
| `/app/profile` | Profile | No permission required |

## Required Permissions for Menus

Below is a comprehensive list of all menus in the application and the permissions required to see them:

### Admin Menus

| Menu | Required Permission |
|------|-------------------|
| Admin Dashboard | `{ feat: 'Dashboard', act: 'read_all' }` |
| Employee Management | `{ feat: 'User', act: 'read' }` |
| Department | `{ feat: 'Department', act: 'read' }` |
| Designation | `{ feat: 'Designation', act: 'read' }` |
| Attendance | `{ feat: 'Attendance', act: 'read_all' }` |
| Expenses | `{ feat: 'Expense', act: 'read' }` |
| Leave Management | `{ feat: 'Leave', act: 'read_all' }` |
| Leave Report | `{ feat: 'Leave Report', act: 'read_all' }` |
| Approval | `{ feat: 'Approve', act: 'read_all' }` |
| Calendar | `{ feat: 'Calendar', act: 'read_all' }` |
| Configuration | `{ feat: 'Configuration', act: 'read_all' }` |
| Projects | `{ feat: 'Project List', act: 'read_all' }` |
| Project Overview | `{ feat: 'Project Overview', act: 'read_all' }` |
| Project List | `{ feat: 'Project List', act: 'read_all' }` |
| Project Timesheet | `{ feat: 'Project Timesheet', act: 'read_all' }` |
| Timeline | `{ feat: 'Timeline', act: 'read_all' }` |
| Overview | `{ feat: 'Overview', act: 'read_all' }` |
| Time Request | `{ feat: 'Time Request', act: 'read_all' }` |
| Task Request | `{ feat: 'Task Request', act: 'read_all' }` |
| Work Schedule | `{ feat: 'Work Schedule', act: 'read_all' }` |
| Client | `{ feat: 'Client', act: 'read_all' }` |
| Report | `{ feat: 'Report', act: 'read' }` |
| Setting | `{ feat: 'Setting', act: 'read' }` |

### User Menus

| Menu | Required Permission |
|------|-------------------|
| My Dashboard | `{ feat: 'Dashboard', act: 'read' }` |
| My Attendance | `{ feat: 'Attendance', act: 'read' }` |
| My Leave | `{ feat: 'Leave', act: 'read' }` |
| My Projects | `{ feat: 'Projects', act: 'read' }` |
| Project Overview | `{ feat: 'Project Overview', act: 'read' }` |
| Project Timesheet | `{ feat: 'Project Timesheet', act: 'read' }` |
| My Tasks | `{ feat: 'Tasks', act: 'read' }` |
| My Timeline | `{ feat: 'Timeline', act: 'read' }` |
| My Expenses | `{ feat: 'Expense', act: 'read' }` |

## Permission Checking Logic

The application uses a `Can()` utility function to check if a user has permission to perform an action on a feature. This function is used throughout the application to control access to routes, menus, and UI components.

```javascript
// Example usage
import Can from 'utils/can';

// Check if user can read the User feature
const canReadUser = Can('read', 'User');

// Check if user can create a new Department
const canCreateDepartment = Can('create', 'Department');
```

### Permission Inheritance

If a user has `create`, `update`, or `delete` permission for a feature, they automatically get `read` permission for that feature. This is implemented in the `Can()` utility function.

### Specialized Read Permissions

Some features require more granular read permissions:

- **read_all**: Full read access to all items in a feature
  - Required for admin dashboards and reports
  - Required for viewing all users' data (attendance, leave, etc.)
  - Example: `Can('read_all', 'Dashboard')` for admin dashboard

- **read_some**: Read access to a subset of items (e.g., department-specific)
  - Used for department managers who need to see their team's data
  - Example: `Can('read_some', 'Attendance')` for viewing department attendance

- **read_self**: Read access only to the user's own data
  - Used for regular users to view their own information
  - Example: `Can('read_self', 'Attendance')` for viewing own attendance

## Default Permissions

By default, all users have access to:
- Their profile
- Basic dashboard access

Administrators have additional default permissions for:
- Employee management
- User permission management

## Permission Naming Conventions

To ensure consistency, the system uses the following naming conventions:

1. Feature names are case-sensitive and should match exactly what's in the database
2. Admin features use PascalCase (e.g., "User", "Department")
3. User-specific features use the same name as admin features (e.g., "Dashboard", "Attendance")

## Troubleshooting

If a user is unable to access a feature they should have access to, check:

1. **Permission Format**: Ensure the permission is correctly formatted in the database
2. **Feature Name**: Check that the feature name matches exactly (case-sensitive)
3. **Action Type**: Verify that the user has the correct action permission
4. **Permission Logs**: Check the browser console for permission check logs
5. **Route Definition**: Verify that the route is using the correct permission requirement

## Common Issues and Solutions

### User can't access dashboard

**Issue**: User has dashboard permission but still sees "Access Denied"
**Solution**: Ensure the user has `read_all` permission for the Dashboard feature, not just `read`

```javascript
// Correct permission for user dashboard
{
  feat: "Dashboard",
  acts: ["read_all"]
}
```

### Inconsistent feature names

**Issue**: Some features have different names in different parts of the code
**Solution**: Use the feature mapping in the `Can()` function to normalize feature names

```javascript
// Feature mapping example
const FEATURE_MAPPING = {
  'tasks': 'Tasks',
  'my tasks': 'Tasks',
  'mytasks': 'Tasks'
};
```

### Admin users can't access certain features

**Issue**: Admin users should have access to all features
**Solution**: Ensure admin users have the correct role and permissions

```javascript
// Check if user is admin
const isAdmin = profile.role && profile.role.includes('admin');

// Admin users always have access to admin features
if (isAdmin && adminFeatures.includes(feature)) {
  return true;
}
```

## Best Practices

1. **Use Consistent Feature Names**: Always use the same feature name throughout the application
2. **Prefer Explicit Permissions**: Avoid relying on permission inheritance for critical features
3. **Document Required Permissions**: Document what permissions are required for each feature
4. **Test Permission Changes**: Always test permission changes with different user roles
5. **Use Permission Logging**: Enable permission logging during development to debug issues
