{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\MonthWorkScheduleForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Typography, MenuItem, Box } from '@mui/material';\nimport { useFormik } from 'formik';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport Input from 'components/Input';\nimport SelectField from 'components/SelectField';\nimport { UserActions } from 'slices/actions';\nimport { GeneralSelector } from 'selectors';\nimport { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MonthWorkScheduleForm = ({\n  open,\n  onClose,\n  selectedUser,\n  selectedDate\n}) => {\n  _s();\n  var _selectedUser$workSch, _selectedUser$workSch2, _selectedUser$workSch3, _selectedUser$workSch4, _selectedUser$workSch5, _selectedUser$workSch6;\n  const dispatch = useDispatch();\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  useEffect(() => {\n    if (success) {\n      toast.success(`Work schedule updated successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      onClose();\n    }\n  }, [success, onClose]);\n\n  // Helper function to format date for input field\n  const formatDateForInput = dateValue => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0];\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n  const formik = useFormik({\n    initialValues: {\n      scheduleTemplate: (selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$workSch = selectedUser.workSchedule) === null || _selectedUser$workSch === void 0 ? void 0 : _selectedUser$workSch.scheduleTemplate) || DEFAULT_WORK_SCHEDULE.scheduleTemplate,\n      shiftStart: formatDateForInput(selectedDate || (selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$workSch2 = selectedUser.workSchedule) === null || _selectedUser$workSch2 === void 0 ? void 0 : _selectedUser$workSch2.shiftStart)),\n      shiftEnd: formatDateForInput(selectedDate || (selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$workSch3 = selectedUser.workSchedule) === null || _selectedUser$workSch3 === void 0 ? void 0 : _selectedUser$workSch3.shiftEnd)),\n      startTime: (selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$workSch4 = selectedUser.workSchedule) === null || _selectedUser$workSch4 === void 0 ? void 0 : _selectedUser$workSch4.startTime) || DEFAULT_WORK_SCHEDULE.startTime,\n      endTime: (selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$workSch5 = selectedUser.workSchedule) === null || _selectedUser$workSch5 === void 0 ? void 0 : _selectedUser$workSch5.endTime) || DEFAULT_WORK_SCHEDULE.endTime,\n      minimumHours: (selectedUser === null || selectedUser === void 0 ? void 0 : (_selectedUser$workSch6 = selectedUser.workSchedule) === null || _selectedUser$workSch6 === void 0 ? void 0 : _selectedUser$workSch6.minimumHours) || DEFAULT_WORK_SCHEDULE.minimumHours,\n      workPlan: ''\n    },\n    enableReinitialize: true,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  const handleSubmit = values => {\n    if (!(selectedUser !== null && selectedUser !== void 0 && selectedUser._id)) {\n      toast.error('User information is missing');\n      return;\n    }\n    const params = {\n      id: selectedUser._id,\n      workSchedule: {\n        scheduleTemplate: values.scheduleTemplate,\n        shiftStart: values.shiftStart,\n        shiftEnd: values.shiftEnd,\n        startTime: values.startTime,\n        endTime: values.endTime,\n        minimumHours: parseFloat(values.minimumHours)\n      }\n    };\n    dispatch(UserActions.updateUser(params));\n  };\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(field, value);\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [\"Month Work Schedule - \", selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: [\"Date: \", selectedDate]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: formik.handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Schedule Template\",\n                name: \"scheduleTemplate\",\n                value: formik.values.scheduleTemplate,\n                onChange: e => handleWorkScheduleChange('scheduleTemplate', e.target.value),\n                required: true,\n                children: SCHEDULE_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: template.value,\n                  children: template.label\n                }, template.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Minimum Hours\",\n                name: \"minimumHours\",\n                type: \"number\",\n                step: \"0.1\",\n                value: formik.values.minimumHours,\n                onChange: e => handleWorkScheduleChange('minimumHours', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Shift Start Date\",\n                name: \"shiftStart\",\n                type: \"date\",\n                value: formik.values.shiftStart,\n                onChange: e => handleWorkScheduleChange('shiftStart', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Shift End Date\",\n                name: \"shiftEnd\",\n                type: \"date\",\n                value: formik.values.shiftEnd,\n                onChange: e => handleWorkScheduleChange('shiftEnd', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Start Time\",\n                name: \"startTime\",\n                value: formik.values.startTime,\n                onChange: e => handleWorkScheduleChange('startTime', e.target.value),\n                required: true,\n                children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"End Time\",\n                name: \"endTime\",\n                value: formik.values.endTime,\n                onChange: e => handleWorkScheduleChange('endTime', e.target.value),\n                required: true,\n                children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Work Plan or Notes\",\n                name: \"workPlan\",\n                multiline: true,\n                rows: 4,\n                value: formik.values.workPlan,\n                onChange: e => handleWorkScheduleChange('workPlan', e.target.value),\n                placeholder: \"Enter work plan, goals, or any specific notes for this month...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  p: 2,\n                  bgcolor: 'info.light',\n                  borderRadius: 1,\n                  border: '1px solid',\n                  borderColor: 'info.main'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"info.dark\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Note:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 3\n                  }, this), \" This will update the user\\u2019s default work schedule. The schedule will be applied to their profile and used for attendance calculations.\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        color: \"secondary\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: formik.handleSubmit,\n        variant: \"contained\",\n        color: \"primary\",\n        children: \"Save Schedule\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 5\n  }, this);\n};\n_s(MonthWorkScheduleForm, \"8TrAHkmRpA2h5vkvbA062OMsnck=\", false, function () {\n  return [useDispatch, useSelector, useFormik];\n});\n_c = MonthWorkScheduleForm;\nMonthWorkScheduleForm.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string\n};\nexport default MonthWorkScheduleForm;\nvar _c;\n$RefreshReg$(_c, \"MonthWorkScheduleForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Grid", "Typography", "MenuItem", "Box", "useFormik", "useDispatch", "useSelector", "toast", "PropTypes", "Input", "SelectField", "UserActions", "GeneralSelector", "SCHEDULE_TEMPLATES", "DEFAULT_WORK_SCHEDULE", "TIME_OPTIONS", "jsxDEV", "_jsxDEV", "MonthWorkScheduleForm", "open", "onClose", "selected<PERSON>ser", "selectedDate", "_s", "_selectedUser$workSch", "_selectedUser$workSch2", "_selectedUser$workSch3", "_selectedUser$workSch4", "_selectedUser$workSch5", "_selectedUser$workSch6", "dispatch", "success", "updateUser", "type", "position", "autoClose", "closeOnClick", "formatDateForInput", "dateValue", "Date", "toISOString", "split", "formik", "initialValues", "scheduleTemplate", "workSchedule", "shiftStart", "shiftEnd", "startTime", "endTime", "minimumHours", "workPlan", "enableReinitialize", "onSubmit", "values", "handleSubmit", "_id", "error", "params", "id", "parseFloat", "handleWorkScheduleChange", "field", "value", "setFieldValue", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "variant", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "sx", "mt", "container", "spacing", "item", "xs", "sm", "label", "onChange", "e", "target", "required", "map", "template", "step", "option", "multiline", "rows", "placeholder", "p", "bgcolor", "borderRadius", "border", "borderColor", "onClick", "_c", "propTypes", "bool", "isRequired", "func", "object", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/MonthWorkScheduleForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>alog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Grid,\n  Typography,\n  MenuItem,\n  Box\n} from '@mui/material';\nimport { useFormik } from 'formik';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport Input from 'components/Input';\nimport SelectField from 'components/SelectField';\nimport { UserActions } from 'slices/actions';\nimport { GeneralSelector } from 'selectors';\nimport { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\n\nconst MonthWorkScheduleForm = ({ open, onClose, selectedUser, selectedDate }) => {\n  const dispatch = useDispatch();\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n\n  useEffect(() => {\n    if (success) {\n      toast.success(`Work schedule updated successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true,\n      });\n      onClose();\n    }\n  }, [success, onClose]);\n\n  // Helper function to format date for input field\n  const formatDateForInput = (dateValue) => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0];\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n\n  const formik = useFormik({\n    initialValues: {\n      scheduleTemplate: selectedUser?.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate,\n      shiftStart: formatDateForInput(selectedDate || selectedUser?.workSchedule?.shiftStart),\n      shiftEnd: formatDateForInput(selectedDate || selectedUser?.workSchedule?.shiftEnd),\n      startTime: selectedUser?.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime,\n      endTime: selectedUser?.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime,\n      minimumHours: selectedUser?.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours,\n      workPlan: ''\n    },\n    enableReinitialize: true,\n    onSubmit: (values) => {\n      handleSubmit(values);\n    }\n  });\n\n  const handleSubmit = (values) => {\n    if (!selectedUser?._id) {\n      toast.error('User information is missing');\n      return;\n    }\n\n    const params = {\n      id: selectedUser._id,\n      workSchedule: {\n        scheduleTemplate: values.scheduleTemplate,\n        shiftStart: values.shiftStart,\n        shiftEnd: values.shiftEnd,\n        startTime: values.startTime,\n        endTime: values.endTime,\n        minimumHours: parseFloat(values.minimumHours)\n      }\n    };\n\n    dispatch(UserActions.updateUser(params));\n  };\n\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(field, value);\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        <Typography variant=\"h6\">\n          Month Work Schedule - {selectedUser?.name}\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          Date: {selectedDate}\n        </Typography>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ mt: 2 }}>\n          <form onSubmit={formik.handleSubmit}>\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <SelectField\n                  label=\"Schedule Template\"\n                  name=\"scheduleTemplate\"\n                  value={formik.values.scheduleTemplate}\n                  onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}\n                  required\n                >\n                  {SCHEDULE_TEMPLATES.map((template) => (\n                    <MenuItem key={template.value} value={template.value}>\n                      {template.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <Input\n                  label=\"Minimum Hours\"\n                  name=\"minimumHours\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  value={formik.values.minimumHours}\n                  onChange={(e) => handleWorkScheduleChange('minimumHours', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <Input\n                  label=\"Shift Start Date\"\n                  name=\"shiftStart\"\n                  type=\"date\"\n                  value={formik.values.shiftStart}\n                  onChange={(e) => handleWorkScheduleChange('shiftStart', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <Input\n                  label=\"Shift End Date\"\n                  name=\"shiftEnd\"\n                  type=\"date\"\n                  value={formik.values.shiftEnd}\n                  onChange={(e) => handleWorkScheduleChange('shiftEnd', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <SelectField\n                  label=\"Start Time\"\n                  name=\"startTime\"\n                  value={formik.values.startTime}\n                  onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}\n                  required\n                >\n                  {TIME_OPTIONS.map((option) => (\n                    <MenuItem key={option.value} value={option.value}>\n                      {option.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <SelectField\n                  label=\"End Time\"\n                  name=\"endTime\"\n                  value={formik.values.endTime}\n                  onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}\n                  required\n                >\n                  {TIME_OPTIONS.map((option) => (\n                    <MenuItem key={option.value} value={option.value}>\n                      {option.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12}>\n                <Input\n                  label=\"Work Plan or Notes\"\n                  name=\"workPlan\"\n                  multiline\n                  rows={4}\n                  value={formik.values.workPlan}\n                  onChange={(e) => handleWorkScheduleChange('workPlan', e.target.value)}\n                  placeholder=\"Enter work plan, goals, or any specific notes for this month...\"\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <Box sx={{ \n                  p: 2, \n                  bgcolor: 'info.light', \n                  borderRadius: 1,\n                  border: '1px solid',\n                  borderColor: 'info.main'\n                }}>\n              <Typography variant=\"body2\" color=\"info.dark\">\n  <strong>Note:</strong> This will update the user&rsquo;s default work schedule.\n  The schedule will be applied to their profile and used for attendance calculations.\n</Typography>\n\n                </Box>\n              </Grid>\n            </Grid>\n          </form>\n        </Box>\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={onClose} color=\"secondary\">\n          Cancel\n        </Button>\n        <Button \n          onClick={formik.handleSubmit} \n          variant=\"contained\" \n          color=\"primary\"\n        >\n          Save Schedule\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nMonthWorkScheduleForm.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string\n};\n\nexport default MonthWorkScheduleForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,GAAG,QACE,eAAe;AACtB,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,eAAe,QAAQ,WAAW;AAC3C,SAASC,kBAAkB,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjG,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC/E,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM0B,OAAO,GAAGzB,WAAW,CAACM,eAAe,CAACmB,OAAO,CAACpB,WAAW,CAACqB,UAAU,CAACC,IAAI,CAAC,CAAC;EAEjFvC,SAAS,CAAC,MAAM;IACd,IAAIqC,OAAO,EAAE;MACXxB,KAAK,CAACwB,OAAO,CAAC,qCAAqC,EAAE;QACnDG,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFhB,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACW,OAAO,EAAEX,OAAO,CAAC,CAAC;;EAEtB;EACA,MAAMiB,kBAAkB,GAAIC,SAAS,IAAK;IACxC,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C;IACA,IAAI,OAAOH,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAOA,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAO,IAAIF,IAAI,CAACD,SAAS,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,MAAMC,MAAM,GAAGtC,SAAS,CAAC;IACvBuC,aAAa,EAAE;MACbC,gBAAgB,EAAE,CAAAvB,YAAY,aAAZA,YAAY,wBAAAG,qBAAA,GAAZH,YAAY,CAAEwB,YAAY,cAAArB,qBAAA,uBAA1BA,qBAAA,CAA4BoB,gBAAgB,KAAI9B,qBAAqB,CAAC8B,gBAAgB;MACxGE,UAAU,EAAET,kBAAkB,CAACf,YAAY,KAAID,YAAY,aAAZA,YAAY,wBAAAI,sBAAA,GAAZJ,YAAY,CAAEwB,YAAY,cAAApB,sBAAA,uBAA1BA,sBAAA,CAA4BqB,UAAU,EAAC;MACtFC,QAAQ,EAAEV,kBAAkB,CAACf,YAAY,KAAID,YAAY,aAAZA,YAAY,wBAAAK,sBAAA,GAAZL,YAAY,CAAEwB,YAAY,cAAAnB,sBAAA,uBAA1BA,sBAAA,CAA4BqB,QAAQ,EAAC;MAClFC,SAAS,EAAE,CAAA3B,YAAY,aAAZA,YAAY,wBAAAM,sBAAA,GAAZN,YAAY,CAAEwB,YAAY,cAAAlB,sBAAA,uBAA1BA,sBAAA,CAA4BqB,SAAS,KAAIlC,qBAAqB,CAACkC,SAAS;MACnFC,OAAO,EAAE,CAAA5B,YAAY,aAAZA,YAAY,wBAAAO,sBAAA,GAAZP,YAAY,CAAEwB,YAAY,cAAAjB,sBAAA,uBAA1BA,sBAAA,CAA4BqB,OAAO,KAAInC,qBAAqB,CAACmC,OAAO;MAC7EC,YAAY,EAAE,CAAA7B,YAAY,aAAZA,YAAY,wBAAAQ,sBAAA,GAAZR,YAAY,CAAEwB,YAAY,cAAAhB,sBAAA,uBAA1BA,sBAAA,CAA4BqB,YAAY,KAAIpC,qBAAqB,CAACoC,YAAY;MAC5FC,QAAQ,EAAE;IACZ,CAAC;IACDC,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAGC,MAAM,IAAK;MACpBC,YAAY,CAACD,MAAM,CAAC;IACtB;EACF,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAID,MAAM,IAAK;IAC/B,IAAI,EAACjC,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEmC,GAAG,GAAE;MACtBjD,KAAK,CAACkD,KAAK,CAAC,6BAA6B,CAAC;MAC1C;IACF;IAEA,MAAMC,MAAM,GAAG;MACbC,EAAE,EAAEtC,YAAY,CAACmC,GAAG;MACpBX,YAAY,EAAE;QACZD,gBAAgB,EAAEU,MAAM,CAACV,gBAAgB;QACzCE,UAAU,EAAEQ,MAAM,CAACR,UAAU;QAC7BC,QAAQ,EAAEO,MAAM,CAACP,QAAQ;QACzBC,SAAS,EAAEM,MAAM,CAACN,SAAS;QAC3BC,OAAO,EAAEK,MAAM,CAACL,OAAO;QACvBC,YAAY,EAAEU,UAAU,CAACN,MAAM,CAACJ,YAAY;MAC9C;IACF,CAAC;IAEDpB,QAAQ,CAACnB,WAAW,CAACqB,UAAU,CAAC0B,MAAM,CAAC,CAAC;EAC1C,CAAC;EAED,MAAMG,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACjDrB,MAAM,CAACsB,aAAa,CAACF,KAAK,EAAEC,KAAK,CAAC;EACpC,CAAC;EAED,oBACE9C,OAAA,CAACtB,MAAM;IAACwB,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC6C,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DlD,OAAA,CAACrB,WAAW;MAAAuE,QAAA,gBACVlD,OAAA,CAAChB,UAAU;QAACmE,OAAO,EAAC,IAAI;QAAAD,QAAA,GAAC,wBACD,EAAC9C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgD,IAAI;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACbxD,OAAA,CAAChB,UAAU;QAACmE,OAAO,EAAC,OAAO;QAACM,KAAK,EAAC,gBAAgB;QAAAP,QAAA,GAAC,QAC3C,EAAC7C,YAAY;MAAA;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEdxD,OAAA,CAACpB,aAAa;MAAAsE,QAAA,eACZlD,OAAA,CAACd,GAAG;QAACwE,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,eACjBlD,OAAA;UAAMoC,QAAQ,EAAEX,MAAM,CAACa,YAAa;UAAAY,QAAA,eAClClD,OAAA,CAACjB,IAAI;YAAC6E,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAX,QAAA,gBACzBlD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAd,QAAA,eACvBlD,OAAA,CAACP,WAAW;gBACVwE,KAAK,EAAC,mBAAmB;gBACzBb,IAAI,EAAC,kBAAkB;gBACvBN,KAAK,EAAErB,MAAM,CAACY,MAAM,CAACV,gBAAiB;gBACtCuC,QAAQ,EAAGC,CAAC,IAAKvB,wBAAwB,CAAC,kBAAkB,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBAC9EuB,QAAQ;gBAAAnB,QAAA,EAEPtD,kBAAkB,CAAC0E,GAAG,CAAEC,QAAQ,iBAC/BvE,OAAA,CAACf,QAAQ;kBAAsB6D,KAAK,EAAEyB,QAAQ,CAACzB,KAAM;kBAAAI,QAAA,EAClDqB,QAAQ,CAACN;gBAAK,GADFM,QAAQ,CAACzB,KAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPxD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAd,QAAA,eACvBlD,OAAA,CAACR,KAAK;gBACJyE,KAAK,EAAC,eAAe;gBACrBb,IAAI,EAAC,cAAc;gBACnBpC,IAAI,EAAC,QAAQ;gBACbwD,IAAI,EAAC,KAAK;gBACV1B,KAAK,EAAErB,MAAM,CAACY,MAAM,CAACJ,YAAa;gBAClCiC,QAAQ,EAAGC,CAAC,IAAKvB,wBAAwB,CAAC,cAAc,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBAC1EuB,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPxD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAd,QAAA,eACvBlD,OAAA,CAACR,KAAK;gBACJyE,KAAK,EAAC,kBAAkB;gBACxBb,IAAI,EAAC,YAAY;gBACjBpC,IAAI,EAAC,MAAM;gBACX8B,KAAK,EAAErB,MAAM,CAACY,MAAM,CAACR,UAAW;gBAChCqC,QAAQ,EAAGC,CAAC,IAAKvB,wBAAwB,CAAC,YAAY,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBACxEuB,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPxD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAd,QAAA,eACvBlD,OAAA,CAACR,KAAK;gBACJyE,KAAK,EAAC,gBAAgB;gBACtBb,IAAI,EAAC,UAAU;gBACfpC,IAAI,EAAC,MAAM;gBACX8B,KAAK,EAAErB,MAAM,CAACY,MAAM,CAACP,QAAS;gBAC9BoC,QAAQ,EAAGC,CAAC,IAAKvB,wBAAwB,CAAC,UAAU,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBACtEuB,QAAQ;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPxD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAd,QAAA,eACvBlD,OAAA,CAACP,WAAW;gBACVwE,KAAK,EAAC,YAAY;gBAClBb,IAAI,EAAC,WAAW;gBAChBN,KAAK,EAAErB,MAAM,CAACY,MAAM,CAACN,SAAU;gBAC/BmC,QAAQ,EAAGC,CAAC,IAAKvB,wBAAwB,CAAC,WAAW,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBACvEuB,QAAQ;gBAAAnB,QAAA,EAEPpD,YAAY,CAACwE,GAAG,CAAEG,MAAM,iBACvBzE,OAAA,CAACf,QAAQ;kBAAoB6D,KAAK,EAAE2B,MAAM,CAAC3B,KAAM;kBAAAI,QAAA,EAC9CuB,MAAM,CAACR;gBAAK,GADAQ,MAAM,CAAC3B,KAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPxD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAd,QAAA,eACvBlD,OAAA,CAACP,WAAW;gBACVwE,KAAK,EAAC,UAAU;gBAChBb,IAAI,EAAC,SAAS;gBACdN,KAAK,EAAErB,MAAM,CAACY,MAAM,CAACL,OAAQ;gBAC7BkC,QAAQ,EAAGC,CAAC,IAAKvB,wBAAwB,CAAC,SAAS,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBACrEuB,QAAQ;gBAAAnB,QAAA,EAEPpD,YAAY,CAACwE,GAAG,CAAEG,MAAM,iBACvBzE,OAAA,CAACf,QAAQ;kBAAoB6D,KAAK,EAAE2B,MAAM,CAAC3B,KAAM;kBAAAI,QAAA,EAC9CuB,MAAM,CAACR;gBAAK,GADAQ,MAAM,CAAC3B,KAAK;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPxD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAb,QAAA,eAChBlD,OAAA,CAACR,KAAK;gBACJyE,KAAK,EAAC,oBAAoB;gBAC1Bb,IAAI,EAAC,UAAU;gBACfsB,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACR7B,KAAK,EAAErB,MAAM,CAACY,MAAM,CAACH,QAAS;gBAC9BgC,QAAQ,EAAGC,CAAC,IAAKvB,wBAAwB,CAAC,UAAU,EAAEuB,CAAC,CAACC,MAAM,CAACtB,KAAK,CAAE;gBACtE8B,WAAW,EAAC;cAAiE;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPxD,OAAA,CAACjB,IAAI;cAAC+E,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAb,QAAA,eAChBlD,OAAA,CAACd,GAAG;gBAACwE,EAAE,EAAE;kBACPmB,CAAC,EAAE,CAAC;kBACJC,OAAO,EAAE,YAAY;kBACrBC,YAAY,EAAE,CAAC;kBACfC,MAAM,EAAE,WAAW;kBACnBC,WAAW,EAAE;gBACf,CAAE;gBAAA/B,QAAA,eACJlD,OAAA,CAAChB,UAAU;kBAACmE,OAAO,EAAC,OAAO;kBAACM,KAAK,EAAC,WAAW;kBAAAP,QAAA,gBACzDlD,OAAA;oBAAAkD,QAAA,EAAQ;kBAAK;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,gJAExB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhBxD,OAAA,CAACnB,aAAa;MAAAqE,QAAA,gBACZlD,OAAA,CAAClB,MAAM;QAACoG,OAAO,EAAE/E,OAAQ;QAACsD,KAAK,EAAC,WAAW;QAAAP,QAAA,EAAC;MAE5C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTxD,OAAA,CAAClB,MAAM;QACLoG,OAAO,EAAEzD,MAAM,CAACa,YAAa;QAC7Ba,OAAO,EAAC,WAAW;QACnBM,KAAK,EAAC,SAAS;QAAAP,QAAA,EAChB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAClD,EAAA,CAlNIL,qBAAqB;EAAA,QACRb,WAAW,EACZC,WAAW,EAwBZF,SAAS;AAAA;AAAAgG,EAAA,GA1BpBlF,qBAAqB;AAoN3BA,qBAAqB,CAACmF,SAAS,GAAG;EAChClF,IAAI,EAAEX,SAAS,CAAC8F,IAAI,CAACC,UAAU;EAC/BnF,OAAO,EAAEZ,SAAS,CAACgG,IAAI,CAACD,UAAU;EAClClF,YAAY,EAAEb,SAAS,CAACiG,MAAM;EAC9BnF,YAAY,EAAEd,SAAS,CAACkG;AAC1B,CAAC;AAED,eAAexF,qBAAqB;AAAC,IAAAkF,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}