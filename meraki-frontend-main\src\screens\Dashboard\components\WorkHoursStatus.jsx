import React from 'react';
import PropTypes from 'prop-types';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Divider,
  Alert,
  AlertTitle
} from '@mui/material';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import HourglassEmptyIcon from '@mui/icons-material/HourglassEmpty';
import TimerIcon from '@mui/icons-material/Timer';

/**
 * WorkHoursStatus component displays information about a user's work hours status
 * after they check out, including overtime, half-day, and early checkout information.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when dialog is closed
 * @param {Object} props.statusData - Status data from the checkout API response
 */
const WorkHoursStatus = ({ open, onClose, statusData }) => {
  if (!statusData) {
    return null;
  }

  const {
    earlyCheckOut,
    overtime,
    halfDay,
    actualHours,
    assignedHours,
    overtimeHours = 0,
    autoCheckout = false
  } = statusData;

  // Format hours for display
  const formatHours = (hours) => {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    return `${wholeHours}h ${minutes}m`;
  };

  // Calculate difference between actual and assigned hours
  const hoursDifference = Math.abs(actualHours - assignedHours);
  const formattedDifference = formatHours(hoursDifference);

  // Helper function to get status color
  const getStatusColor = (isHalfDay, isOvertime, isEarlyCheckout) => {
    if (isHalfDay) {
      return 'warning.main';
    }
    if (isOvertime) {
      return 'success.main';
    }
    if (isEarlyCheckout) {
      return 'info.main';
    }
    return 'success.main';
  };

  // Helper function to get status text
  const getStatusText = (isHalfDay, isOvertime, isEarlyCheckout) => {
    if (isHalfDay) {
      return 'Half-Day';
    }
    if (isOvertime) {
      return 'Overtime';
    }
    if (isEarlyCheckout) {
      return 'Early Checkout';
    }
    return 'Complete';
  };

  // Helper function to get hours color
  const getHoursColor = (isOvertime, isEarlyCheckout) => {
    if (isOvertime) {
      return 'success.main';
    }
    if (isEarlyCheckout) {
      return 'text.secondary';
    }
    return 'text.primary';
  };

  // Determine status message and alert type
  let statusMessage = '';
  let alertType = 'info';
  let alertTitle = '';
  let icon = <AccessTimeIcon />;

  if (halfDay) {
    statusMessage = `You've worked less than half of your assigned hours (${formatHours(assignedHours / 2)}). This will be marked as a half-day.`;
    alertType = 'warning';
    alertTitle = 'Half-Day Detected';
    icon = <HourglassEmptyIcon />;
  } else if (earlyCheckOut) {
    statusMessage = `You've checked out ${formattedDifference} earlier than your assigned work hours.`;
    alertType = 'info';
    alertTitle = 'Early Checkout';
    icon = <AccessTimeIcon />;
  } else if (overtime) {
    statusMessage = `You've worked ${formattedDifference} overtime beyond your assigned hours.`;
    alertType = 'success';
    alertTitle = 'Overtime Detected';
    icon = <TimerIcon />;
  } else {
    statusMessage = 'You have completed your assigned work hours for today.';
    alertType = 'success';
    alertTitle = 'Work Complete';
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        Work Hours Status
      </DialogTitle>
      <DialogContent>
        <Alert
          severity={alertType}
          icon={icon}
          sx={{ mb: 2 }}
        >
          <AlertTitle>{alertTitle}</AlertTitle>
          {statusMessage}
        </Alert>

        <Box sx={{ mt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Work Hours Summary
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Assigned Hours:</Typography>
            <Typography variant="body2" fontWeight="bold">
              {formatHours(assignedHours)}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Actual Hours Worked:</Typography>
            <Typography
              variant="body2"
              fontWeight="bold"
              color={getHoursColor(overtime, earlyCheckOut)}
            >
              {formatHours(actualHours)}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Status:</Typography>
            <Typography
              variant="body2"
              fontWeight="bold"
              color={getStatusColor(halfDay, overtime, earlyCheckOut)}
            >
              {getStatusText(halfDay, overtime, earlyCheckOut)}
            </Typography>
          </Box>

          {overtime && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
              <Typography variant="body2">Overtime Hours:</Typography>
              <Typography
                variant="body2"
                fontWeight="bold"
                color="success.main"
              >
                {formatHours(overtimeHours)}
              </Typography>
            </Box>
          )}
        </Box>

        {autoCheckout && (
          <Alert severity="info" sx={{ mt: 2 }}>
            <AlertTitle>Auto-Checkout</AlertTitle>
            Your checkout was processed automatically by the system because you didn&apos;t manually check out.
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

WorkHoursStatus.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func.isRequired,
  statusData: PropTypes.shape({
    earlyCheckOut: PropTypes.bool,
    overtime: PropTypes.bool,
    halfDay: PropTypes.bool,
    actualHours: PropTypes.number,
    assignedHours: PropTypes.number,
    overtimeHours: PropTypes.number,
    autoCheckout: PropTypes.bool
  })
};

export default WorkHoursStatus;
