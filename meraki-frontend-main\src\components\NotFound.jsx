import React from 'react';
import { Box, Typography, Button, Paper } from '@mui/material';
import { useHistory } from 'react-router-dom';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

/**
 * Component to display when a route is not found
 */
const NotFound = () => {
  const history = useHistory();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '70vh',
        p: 3
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          maxWidth: 600,
          textAlign: 'center',
          borderTop: '4px solid #ff9800'
        }}
      >
        <ErrorOutlineIcon sx={{ fontSize: 60, color: '#ff9800', mb: 2 }} />

        <Typography variant="h4" gutterBottom>
          Page Not Found
        </Typography>

        <Typography variant="body1" paragraph>
          The page you are looking for does not exist or has been moved.
        </Typography>

        <Typography variant="body2" paragraph color="textSecondary">
          Please check the URL or navigate to another page.
        </Typography>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              // Check if we're already on a dashboard route to prevent loops
              const currentPath = history.location.pathname;
              if (currentPath.includes('dashboard')) {
                // If already on a dashboard route, go to root
                history.push('/');
              } else {
                // Otherwise go to user dashboard
                history.push('/app/user-dashboard');
              }
            }}
          >
            Go to Dashboard
          </Button>
          <Button
            variant="outlined"
            onClick={() => history.goBack()}
          >
            Go Back
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default NotFound;
