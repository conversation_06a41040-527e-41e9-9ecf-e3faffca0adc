'use strict';

/**
 * User Service
 *
 * This service provides methods for user-related database operations.
 * It handles CRUD operations for users and specialized operations like leave management.
 */

const bcrypt = require('bcryptjs');
const { db } = require("../models");
const User = db.user;
const DefaultPermissionsService = require('./defaultPermissions.service');

/**
 * Create multiple users at once
 * @param {Array} data - Array of user objects to create
 * @returns {Promise<Array>} Array of created user documents
 */
exports.createManyUsers = async (data) => {
    try {
        const processedData = [];
        for (const item of data) {
            const permissions = item.permissions || DefaultPermissionsService.getDefaultPermissions(item.role);

            processedData.push({
                ...item,
                password: bcrypt.hashSync(item.password, 8),
                permissions
            });
        }

        return await User.insertMany(processedData);
    } catch (error) {
        throw new Error(`Error creating multiple users: ${error.message}`);
    }
}

/**
 * Get users with filtering, pagination, and sorting
 *
 * @param {Object} queries - Query parameters
 * @param {number} queries.limit - Number of results per page
 * @param {number} queries.page - Page number
 * @param {Object} queries.sort - Sort criteria
 * @param {Object} queries.query - Filter criteria
 * @returns {Promise<Object>} Object with pagination info and user data
 */
exports.getUsersByQuery = async (queries) => {
    try {
        // Set default values for pagination and sorting
        const limit = queries.limit ?? 20;
        const page = queries.page ?? 1;
        const skip = limit * (page - 1);
        const sort = queries.sort ?? { createdAt: -1 };
        const query = queries.query ?? {};

        // Fetch users with pagination, sorting, and populate references
        const results = await User
            .find(query)
            .skip(skip)
            .limit(limit)
            .sort(sort)
            .populate({ path: "department", select: "name" })
            .populate({ path: "designation", select: "name" });

        // Count total matching documents for pagination
        const counts = await User.countDocuments(query);

        // Return formatted response with pagination info
        return {
            query,
            pagination: {
                perPage: limit,
                currentPage: page,
                counts,
                pages: Math.ceil(counts / limit)
            },
            data: results
        };
    } catch (error) {
        throw new Error(`Error fetching users: ${error.message}`);
    }
}

/**
 * Create a new user
 * @param {Object} data - User data
 * @returns {Promise<Object>} Created user document
 */
exports.createUser = async (data) => {
    try {
        if (!data.permissions || !Array.isArray(data.permissions) || data.permissions.length === 0) {
            data.permissions = DefaultPermissionsService.getDefaultPermissions(data.role);
        }
        return await User.create(data);
    } catch (error) {
        throw new Error(`Error creating user: ${error.message}`);
    }
};

/**
 * Get a user by ID
 *
 * @param {string} id - User ID
 * @returns {Promise<Object|null>} User document or null if not found
 */
exports.getUserById = async (id) => {
    try {
        // Find user by ID and populate references
        return await User.findById(id)
            .populate({ path: "department", select: "name" })
            .populate({ path: "designation", select: "name" });
    } catch (error) {
        // Log error and return null to indicate user not found
        console.error(`Error fetching user by ID ${id}:`, error.message);
        return null;
    }
};

/**
 * Update a user
 * @param {string} id - User ID
 * @param {Object} data - Updated user data
 * @returns {Promise<Object|null>} Updated user document or null if not found
 */
exports.updateUser = async (id, data) => {
    try {
        if (data.role && (!data.permissions || !Array.isArray(data.permissions))) {
            const currentUser = await User.findById(id);
            const currentRoles = currentUser.role || [];
            const newRoles = data.role || [];

            const currentRolesStr = JSON.stringify(currentRoles.sort());
            const newRolesStr = JSON.stringify(newRoles.sort());

            if (currentRolesStr !== newRolesStr) {
                data.permissions = DefaultPermissionsService.getDefaultPermissions(newRoles);
            }
        }

        return await User.findByIdAndUpdate(id, data, { new: true });
    } catch (error) {
        throw new Error(`Error updating user: ${error.message}`);
    }
};

/**
 * Delete a user
 *
 * @param {string} id - User ID
 * @returns {Promise<Object|null>} Deleted user document or null if not found
 */
exports.deleteUser = async (id) => {
    try {
        return await User.findByIdAndDelete(id);
    } catch (error) {
        throw new Error(`Error deleting user: ${error.message}`);
    }
};

/**
 * Update a user's leave information
 *
 * @param {string} id - User ID
 * @param {Object} body - Leave data
 * @param {string} body.type - Leave type ('fullday' or 'halfday')
 * @param {string} body.specifictype - Specific leave type ('paidleave', 'unpaidleave', or other)
 * @returns {Promise<Object>} Updated user document
 */
exports.updateUserLeave = async (id, body) => {
    try {
        let update = {};

        // Determine leave amount based on type (full day or half day)
        switch (body.type) {
            case "fullday":
                update = { $inc: { availableLeaves: -1, leaveTaken: 1 } };
                break;
            case "halfday":
                update = { $inc: { availableLeaves: -0.5, leaveTaken: 0.5 } };
                break;
            default:
                throw new Error("Invalid leave type. Must be 'fullday' or 'halfday'.");
        }

        // Determine specific leave category
        if (body.specifictype === "paidleave") {
            update.$inc.paidLeaves = body.type === "fullday" ? 1 : 0.5;
        } else if (body.specifictype === "unpaidleave") {
            update.$inc.unpaidLeaves = body.type === "fullday" ? 1 : 0.5;
        } else {
            update.$inc.otherLeaves = body.type === "fullday" ? 1 : 0.5;
        }

        // Update user document with leave information
        const result = await User.findOneAndUpdate(
            { _id: id },
            update,
            { returnOriginal: false, new: true }
        );

        if (!result) {
            throw new Error(`User with ID ${id} not found`);
        }

        return result;
    } catch (error) {
        throw new Error(`Error updating user leave: ${error.message}`);
    }
};

/**
 * Update a user's available leaves by admin
 *
 * @param {string} id - User ID
 * @param {Object} body - Request body containing leave data
 * @returns {Promise<Object>} Updated user document
 */
exports.updateUserLeaveAdmin = async (id, body) => {
    try {
        // Parse leave data from request body
        const data = JSON.parse(body.body);

        // Update user's available leaves
        const result = await User.findOneAndUpdate(
            { _id: id },
            { $set: { availableLeaves: data.leaveAvailable } },
            { new: true }
        );

        if (!result) {
            throw new Error(`User with ID ${id} not found`);
        }

        return result;
    } catch (error) {
        throw new Error(`Error updating user leave by admin: ${error.message}`);
    }
};
