import React from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Link } from 'react-router-dom';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Tooltip,
  Typography,
  CircularProgress
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ListAltIcon from '@mui/icons-material/ListAlt';
import Can from '../../../utils/can';
import { ProductSelector } from '../../../selectors/ProductSelector';

/**
 * Sprint List Component
 * 
 * Displays a list of sprints with actions for edit, delete, start, and complete.
 * Shows different actions based on user permissions and sprint ownership.
 */
const SprintList = ({ 
  sprints, 
  loading, 
  onEdit, 
  onDelete, 
  onStart, 
  onComplete, 
  isAdmin,
  currentUserId 
}) => {
  // Get products to display product names
  const products = useSelector(ProductSelector.getProducts());
  
  // Helper function to format dates
  const formatDate = (dateString) => {
    if (!dateString) { return 'Not set' }
    return new Date(dateString).toLocaleDateString();
  };

  // Helper function to determine sprint status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'success';
      case 'completed':
        return 'secondary';
      case 'planned':
        return 'primary';
      default:
        return 'default';
    }
  };

  // Helper function to get product name from ID
  const getProductName = (productId) => {
    if (!productId) { return 'Global' }
    
    // Handle case where productId might be an object with _id property
    const id = typeof productId === 'object' ? productId._id : productId;
    
    // Check if the product exists in our products list
    const product = products.find(p => String(p._id) === String(id));
    
    // If product is found, return its name
    if (product) { return product.productName }
    
    // If productId is an object with productName property, use that
    if (typeof productId === 'object' && productId.productName) {
      return productId.productName;
    }
    
    return 'Unknown Product';
  };

  // Check if user can edit/delete a specific sprint
  const canModifySprint = (sprint) => {
    // Admins can modify any sprint
    if (isAdmin) { return true }
    
    // Regular users can only modify their own sprints
    return sprint.createdBy === currentUserId;
  };

  // If loading, show loading indicator
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  // If no sprints, show message
  if (!sprints || sprints.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1">No sprints found. Create a new sprint to get started.</Typography>
      </Paper>
    );
  }

  return (
    <TableContainer component={Paper}>
      <Table sx={{ minWidth: 650 }} aria-label="sprints table">
        <TableHead>
          <TableRow>
            <TableCell>Name</TableCell>
            <TableCell>Goal</TableCell>
            <TableCell>Product</TableCell>
            <TableCell>Start Date</TableCell>
            <TableCell>End Date</TableCell>
            <TableCell>Status</TableCell>
            {isAdmin && <TableCell>Created By</TableCell>}
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {sprints.map((sprint) => (
            <TableRow key={sprint.id || sprint._id}>
              <TableCell component="th" scope="row">
                {sprint.name}
              </TableCell>
              <TableCell>{sprint.goal}</TableCell>
              <TableCell>{getProductName(sprint.productId)}</TableCell>
              <TableCell>{formatDate(sprint.startDate)}</TableCell>
              <TableCell>{formatDate(sprint.endDate)}</TableCell>
              <TableCell>
                <Chip 
                  label={sprint.status || 'Planned'} 
                  color={getStatusColor(sprint.status)} 
                  size="small" 
                />
              </TableCell>
              {isAdmin && (
                <TableCell>
                  {sprint.createdBy && sprint.createdBy.name ? sprint.createdBy.name : (sprint.createdByName || 'Unknown')}
                </TableCell>
              )}
              <TableCell align="right">
                <Tooltip title="View Tasks">
                  <IconButton 
                    size="small" 
                    component={Link}
                    to={`/app/${isAdmin ? '' : 'user/'}sprint/${sprint.id || sprint._id}/tasks`}
                    color="info"
                    aria-label="view tasks"
                  >
                    <ListAltIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                
                {canModifySprint(sprint) && Can('update', 'Sprint') && (
                  <Tooltip title="Edit">
                    <IconButton 
                      size="small" 
                      onClick={() => onEdit(sprint)}
                      aria-label="edit"
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                
                {sprint.status !== 'active' && sprint.status !== 'completed' && 
                 canModifySprint(sprint) && Can('update', 'Sprint') && (
                  <Tooltip title="Start Sprint">
                    <IconButton 
                      size="small" 
                      onClick={() => onStart(sprint.id || sprint._id)}
                      color="primary"
                      aria-label="start sprint"
                    >
                      <PlayArrowIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                
                {sprint.status === 'active' && 
                 canModifySprint(sprint) && Can('update', 'Sprint') && (
                  <Tooltip title="Complete Sprint">
                    <IconButton 
                      size="small" 
                      onClick={() => onComplete(sprint.id || sprint._id)}
                      color="success"
                      aria-label="complete sprint"
                    >
                      <CheckCircleIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
                
                {canModifySprint(sprint) && Can('delete', 'Sprint') && (
                  <Tooltip title="Delete">
                    <IconButton 
                      size="small" 
                      onClick={() => onDelete(sprint.id || sprint._id)}
                      color="error"
                      aria-label="delete"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

SprintList.propTypes = {
  sprints: PropTypes.array,
  loading: PropTypes.bool,
  onEdit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  onStart: PropTypes.func.isRequired,
  onComplete: PropTypes.func.isRequired,
  isAdmin: PropTypes.bool,
  currentUserId: PropTypes.string
};

SprintList.defaultProps = {
  sprints: [],
  loading: false,
  isAdmin: false
};

export default SprintList;