{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\DayWorkSchedule.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Avatar, Box, Typography, IconButton, Tooltip } from '@mui/material';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\nimport dayjs from 'dayjs';\nimport PropTypes from 'prop-types';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { UserActions } from 'slices/actions';\nimport { UserSelector } from 'selectors';\nimport DayWorkScheduleForm from './DayWorkScheduleForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst hours = Array.from({\n  length: 24\n}, (_, i) => `${i}:00`);\nconst SLOT_WIDTH = 60;\nconst USER_WIDTH = 200;\nconst ROW_HEIGHT = 60;\nconst DayWorkSchedule = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const [open, setOpen] = useState(false);\n  const [selected, setSelected] = useState(null);\n\n  // Get the current date from dateRange or default to today\n  const currentDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? dayjs(dateRange.startDate) : dayjs();\n  const isDateRange = (dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate) !== (dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate);\n\n  // Fetch users when component mounts\n  useEffect(() => {\n    dispatch(UserActions.getUsers());\n  }, [dispatch]);\n  const handleClick = (user, hour) => {\n    setSelected({\n      user,\n      hour,\n      date: currentDate.format('YYYY-MM-DD')\n    });\n    setOpen(true);\n  };\n  const handleClose = () => {\n    setOpen(false);\n    setSelected(null);\n  };\n\n  // Format the date display\n  const getDateDisplayText = () => {\n    if (isDateRange && dateRange !== null && dateRange !== void 0 && dateRange.endDate) {\n      const endDate = dayjs(dateRange.endDate);\n      if (currentDate.month() === endDate.month() && currentDate.year() === endDate.year()) {\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"D, YYYY\")}`;\n      } else if (currentDate.year() === endDate.year()) {\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"MMM D, YYYY\")}`;\n      } else {\n        return `${currentDate.format(\"MMM D, YYYY\")} - ${endDate.format(\"MMM D, YYYY\")}`;\n      }\n    } else {\n      return currentDate.format(\"dddd, MMMM D, YYYY\");\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      mb: 2,\n      children: [\"Day Work Schedule - \", getDateDisplayText()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        overflowX: 'auto',\n        border: '1px solid #ccc',\n        borderRadius: 1,\n        whiteSpace: 'nowrap',\n        '&::-webkit-scrollbar': {\n          height: 8\n        },\n        '&::-webkit-scrollbar-thumb': {\n          backgroundColor: '#999',\n          borderRadius: 4\n        }\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minWidth: `${USER_WIDTH + hours.length * SLOT_WIDTH}px`\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            position: 'sticky',\n            top: 0,\n            zIndex: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: USER_WIDTH,\n              height: ROW_HEIGHT,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontWeight: 600,\n              fontSize: 13,\n              borderRight: '1px solid #ccc',\n              borderBottom: '1px solid #ccc',\n              backgroundColor: '#f0f0f0',\n              position: 'sticky',\n              left: 0,\n              zIndex: 3\n            },\n            children: \"User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this), hours.map((hour, idx) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: SLOT_WIDTH,\n              height: ROW_HEIGHT,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: 12,\n              fontWeight: 600,\n              borderRight: '1px solid #ccc',\n              borderBottom: '1px solid #ccc',\n              backgroundColor: '#f0f0f0'\n            },\n            children: hour\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxHeight: 400,\n            overflowY: 'auto'\n          },\n          children: users.map((user, uIdx) => {\n            var _user$designation;\n            return /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: USER_WIDTH,\n                  minHeight: ROW_HEIGHT,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  px: 2,\n                  backgroundColor: '#fff',\n                  borderRight: '1px solid #eee',\n                  borderBottom: '1px solid #eee',\n                  position: 'sticky',\n                  left: 0,\n                  zIndex: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 32,\n                    height: 32\n                  },\n                  children: user.name ? user.name[0].toUpperCase() : 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    fontWeight: 600,\n                    fontSize: 13,\n                    children: user.name || 'Unknown User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: ((_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation.name) || user.role || 'No Role'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this), user.workSchedule && (user.workSchedule.startTime !== '09:00' || user.workSchedule.endTime !== '17:30' || user.workSchedule.scheduleTemplate !== 'day_shift') && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"primary\",\n                    sx: {\n                      display: 'block'\n                    },\n                    children: [user.workSchedule.startTime, \"-\", user.workSchedule.endTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 17\n              }, this), hours.map((hour, hIdx) => {\n                var _user$workSchedule, _user$workSchedule2;\n                // Check if user has custom work schedule for this time\n                const hasCustomSchedule = user.workSchedule && (user.workSchedule.startTime !== '09:00' || user.workSchedule.endTime !== '17:30' || user.workSchedule.scheduleTemplate !== 'day_shift');\n                // Check if this hour falls within user's work schedule\n                const hourNum = parseInt(hour.split(':')[0], 10);\n                const startHour = (_user$workSchedule = user.workSchedule) !== null && _user$workSchedule !== void 0 && _user$workSchedule.startTime ? parseInt(user.workSchedule.startTime.split(':')[0], 10) : 9;\n                const endHour = (_user$workSchedule2 = user.workSchedule) !== null && _user$workSchedule2 !== void 0 && _user$workSchedule2.endTime ? parseInt(user.workSchedule.endTime.split(':')[0], 10) : 17;\n                const isWorkingHour = hourNum >= startHour && hourNum < endHour;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: SLOT_WIDTH,\n                    height: ROW_HEIGHT,\n                    borderRight: '1px solid #eee',\n                    borderBottom: '1px solid #eee',\n                    position: 'relative',\n                    backgroundColor: hasCustomSchedule && isWorkingHour ? '#e3f2fd' : '#fafafa',\n                    '&:hover .add-icon': {\n                      opacity: 1\n                    },\n                    '&:hover .schedule-info': {\n                      opacity: 1\n                    }\n                  },\n                  children: [hasCustomSchedule && isWorkingHour && /*#__PURE__*/_jsxDEV(Box, {\n                    className: \"schedule-info\",\n                    sx: {\n                      position: 'absolute',\n                      top: 2,\n                      left: 2,\n                      right: 2,\n                      fontSize: '8px',\n                      color: 'primary.main',\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      opacity: 0.7,\n                      transition: 'opacity 0.3s',\n                      lineHeight: 1,\n                      overflow: 'hidden'\n                    },\n                    children: [user.workSchedule.scheduleTemplate === 'night_shift' ? 'Night' : 'Day', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 27\n                    }, this), user.workSchedule.startTime, \"-\", user.workSchedule.endTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: hasCustomSchedule && isWorkingHour ? `${user.workSchedule.scheduleTemplate} (${user.workSchedule.startTime}-${user.workSchedule.endTime})` : \"Add schedule\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      className: \"add-icon\",\n                      size: \"small\",\n                      sx: {\n                        position: 'absolute',\n                        bottom: 2,\n                        right: 2,\n                        opacity: 0,\n                        transition: 'opacity 0.3s',\n                        '& .MuiSvgIcon-root': {\n                          fontSize: '12px'\n                        }\n                      },\n                      onClick: () => handleClick(user, hour),\n                      children: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 23\n                  }, this)]\n                }, hIdx, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 21\n                }, this);\n              })]\n            }, uIdx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 8\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DayWorkScheduleForm, {\n      open: open,\n      onClose: handleClose,\n      selectedUser: selected === null || selected === void 0 ? void 0 : selected.user,\n      selectedDate: selected === null || selected === void 0 ? void 0 : selected.date,\n      selectedHour: selected === null || selected === void 0 ? void 0 : selected.hour\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(DayWorkSchedule, \"4I5PpVTiva6zDCBxM7mwwoS3W9Q=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DayWorkSchedule;\nDayWorkSchedule.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default DayWorkSchedule;\nvar _c;\n$RefreshReg$(_c, \"DayWorkSchedule\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Avatar", "Box", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "AddCircleOutlineIcon", "dayjs", "PropTypes", "useDispatch", "useSelector", "UserActions", "UserSelector", "DayWorkScheduleForm", "jsxDEV", "_jsxDEV", "hours", "Array", "from", "length", "_", "i", "SLOT_WIDTH", "USER_WIDTH", "ROW_HEIGHT", "DayWorkSchedule", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "users", "getUsers", "open", "<PERSON><PERSON><PERSON>", "selected", "setSelected", "currentDate", "startDate", "isDateRange", "endDate", "handleClick", "user", "hour", "date", "format", "handleClose", "getDateDisplayText", "month", "year", "p", "children", "variant", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "width", "overflowX", "border", "borderRadius", "whiteSpace", "height", "backgroundColor", "min<PERSON><PERSON><PERSON>", "display", "position", "top", "zIndex", "alignItems", "justifyContent", "fontWeight", "fontSize", "borderRight", "borderBottom", "left", "map", "idx", "maxHeight", "overflowY", "uIdx", "_user$designation", "minHeight", "gap", "px", "name", "toUpperCase", "color", "designation", "role", "workSchedule", "startTime", "endTime", "scheduleTemplate", "hIdx", "_user$workSchedule", "_user$workSchedule2", "hasCustomSchedule", "hourNum", "parseInt", "split", "startHour", "endHour", "isWorkingHour", "opacity", "className", "right", "textAlign", "transition", "lineHeight", "overflow", "title", "size", "bottom", "onClick", "onClose", "selected<PERSON>ser", "selectedDate", "selected<PERSON>our", "_c", "propTypes", "shape", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/DayWorkSchedule.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Avatar,\r\n  Box,\r\n  Typography,\r\n  IconButton,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';\r\nimport dayjs from 'dayjs';\r\nimport PropTypes from 'prop-types';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { UserActions } from 'slices/actions';\r\nimport { UserSelector } from 'selectors';\r\nimport DayWorkScheduleForm from './DayWorkScheduleForm';\r\n\r\nconst hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);\r\n\r\nconst SLOT_WIDTH = 60;\r\nconst USER_WIDTH = 200;\r\nconst ROW_HEIGHT = 60;\r\n\r\nconst DayWorkSchedule = ({ dateRange }) => {\r\n  const dispatch = useDispatch();\r\n  const users = useSelector(UserSelector.getUsers());\r\n  const [open, setOpen] = useState(false);\r\n  const [selected, setSelected] = useState(null);\r\n\r\n  // Get the current date from dateRange or default to today\r\n  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();\r\n  const isDateRange = dateRange?.startDate !== dateRange?.endDate;\r\n\r\n  // Fetch users when component mounts\r\n  useEffect(() => {\r\n    dispatch(UserActions.getUsers());\r\n  }, [dispatch]);\r\n\r\n  const handleClick = (user, hour) => {\r\n    setSelected({\r\n      user,\r\n      hour,\r\n      date: currentDate.format('YYYY-MM-DD')\r\n    });\r\n    setOpen(true);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setOpen(false);\r\n    setSelected(null);\r\n  };\r\n\r\n  // Format the date display\r\n  const getDateDisplayText = () => {\r\n    if (isDateRange && dateRange?.endDate) {\r\n      const endDate = dayjs(dateRange.endDate);\r\n      if (currentDate.month() === endDate.month() && currentDate.year() === endDate.year()) {\r\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"D, YYYY\")}`;\r\n      } else if (currentDate.year() === endDate.year()) {\r\n        return `${currentDate.format(\"MMM D\")} - ${endDate.format(\"MMM D, YYYY\")}`;\r\n      } else {\r\n        return `${currentDate.format(\"MMM D, YYYY\")} - ${endDate.format(\"MMM D, YYYY\")}`;\r\n      }\r\n    } else {\r\n      return currentDate.format(\"dddd, MMMM D, YYYY\");\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Box p={3}>\r\n      <Typography variant=\"h5\" mb={2}>\r\n        Day Work Schedule - {getDateDisplayText()}\r\n      </Typography>\r\n\r\n      {/* Scrollable table wrapper */}\r\n      <Box\r\n        sx={{\r\n          width: '100%',\r\n          overflowX: 'auto',\r\n          border: '1px solid #ccc',\r\n          borderRadius: 1,\r\n          whiteSpace: 'nowrap',\r\n          '&::-webkit-scrollbar': {\r\n            height: 8\r\n          },\r\n          '&::-webkit-scrollbar-thumb': {\r\n            backgroundColor: '#999',\r\n            borderRadius: 4\r\n          }\r\n        }}\r\n      >\r\n       <Box sx={{ minWidth: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px` }}>\r\n\r\n\r\n          {/* Header */}\r\n          <Box sx={{ display: 'flex', position: 'sticky', top: 0, zIndex: 2 }}>\r\n            <Box\r\n              sx={{\r\n                width: USER_WIDTH,\r\n                height: ROW_HEIGHT,\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                fontWeight: 600,\r\n                fontSize: 13,\r\n                borderRight: '1px solid #ccc',\r\n                borderBottom: '1px solid #ccc',\r\n                backgroundColor: '#f0f0f0',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 3\r\n              }}\r\n            >\r\n              User\r\n            </Box>\r\n            {hours.map((hour, idx) => (\r\n              <Box\r\n                key={idx}\r\n                sx={{\r\n                  width: SLOT_WIDTH,\r\n                  height: ROW_HEIGHT,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  fontSize: 12,\r\n                  fontWeight: 600,\r\n                  borderRight: '1px solid #ccc',\r\n                  borderBottom: '1px solid #ccc',\r\n                  backgroundColor: '#f0f0f0'\r\n                }}\r\n              >\r\n                {hour}\r\n              </Box>\r\n            ))}\r\n          </Box>\r\n\r\n          {/* User Rows */}\r\n          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>\r\n            {users.map((user, uIdx) => (\r\n              <Box key={uIdx} sx={{ display: 'flex' }}>\r\n                {/* User Info */}\r\n                <Box\r\n                  sx={{\r\n                    width: USER_WIDTH,\r\n                    minHeight: ROW_HEIGHT,\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    gap: 1,\r\n                    px: 2,\r\n                    backgroundColor: '#fff',\r\n                    borderRight: '1px solid #eee',\r\n                    borderBottom: '1px solid #eee',\r\n                    position: 'sticky',\r\n                    left: 0,\r\n                    zIndex: 1\r\n                  }}\r\n                >\r\n                  <Avatar sx={{ width: 32, height: 32 }}>\r\n                    {user.name ? user.name[0].toUpperCase() : 'U'}\r\n                  </Avatar>\r\n                  <Box>\r\n                    <Typography fontWeight={600} fontSize={13}>{user.name || 'Unknown User'}</Typography>\r\n                    <Typography variant=\"caption\" color=\"text.secondary\">\r\n                      {user.designation?.name || user.role || 'No Role'}\r\n                    </Typography>\r\n                    {/* Show work schedule indicator if different from default */}\r\n                    {user.workSchedule && (\r\n                      user.workSchedule.startTime !== '09:00' ||\r\n                      user.workSchedule.endTime !== '17:30' ||\r\n                      user.workSchedule.scheduleTemplate !== 'day_shift'\r\n                    ) && (\r\n                      <Typography variant=\"caption\" color=\"primary\" sx={{ display: 'block' }}>\r\n                        {user.workSchedule.startTime}-{user.workSchedule.endTime}\r\n                      </Typography>\r\n                    )}\r\n                  </Box>\r\n                </Box>\r\n\r\n                {/* Time Slots */}\r\n                {hours.map((hour, hIdx) => {\r\n                  // Check if user has custom work schedule for this time\r\n                  const hasCustomSchedule = user.workSchedule && (\r\n                    user.workSchedule.startTime !== '09:00' ||\r\n                    user.workSchedule.endTime !== '17:30' ||\r\n                    user.workSchedule.scheduleTemplate !== 'day_shift'\r\n                  );\r\n// Check if this hour falls within user's work schedule\r\nconst hourNum = parseInt(hour.split(':')[0], 10);\r\nconst startHour = user.workSchedule?.startTime ? parseInt(user.workSchedule.startTime.split(':')[0], 10) : 9;\r\nconst endHour = user.workSchedule?.endTime ? parseInt(user.workSchedule.endTime.split(':')[0], 10) : 17;\r\nconst isWorkingHour = hourNum >= startHour && hourNum < endHour;\r\n\r\n\r\n                  return (\r\n                    <Box\r\n                      key={hIdx}\r\n                      sx={{\r\n                        width: SLOT_WIDTH,\r\n                        height: ROW_HEIGHT,\r\n                        borderRight: '1px solid #eee',\r\n                        borderBottom: '1px solid #eee',\r\n                        position: 'relative',\r\n                        backgroundColor: hasCustomSchedule && isWorkingHour ? '#e3f2fd' : '#fafafa',\r\n                        '&:hover .add-icon': { opacity: 1 },\r\n                        '&:hover .schedule-info': { opacity: 1 }\r\n                      }}\r\n                    >\r\n                      {/* Show schedule info if custom schedule exists and it's working hour */}\r\n                      {hasCustomSchedule && isWorkingHour && (\r\n                        <Box\r\n                          className=\"schedule-info\"\r\n                          sx={{\r\n                            position: 'absolute',\r\n                            top: 2,\r\n                            left: 2,\r\n                            right: 2,\r\n                            fontSize: '8px',\r\n                            color: 'primary.main',\r\n                            fontWeight: 600,\r\n                            textAlign: 'center',\r\n                            opacity: 0.7,\r\n                            transition: 'opacity 0.3s',\r\n                            lineHeight: 1,\r\n                            overflow: 'hidden'\r\n                          }}\r\n                        >\r\n                          {user.workSchedule.scheduleTemplate === 'night_shift' ? 'Night' : 'Day'}\r\n                          <br />\r\n                          {user.workSchedule.startTime}-{user.workSchedule.endTime}\r\n                        </Box>\r\n                      )}\r\n\r\n                      <Tooltip title={hasCustomSchedule && isWorkingHour ?\r\n                        `${user.workSchedule.scheduleTemplate} (${user.workSchedule.startTime}-${user.workSchedule.endTime})` :\r\n                        \"Add schedule\"\r\n                      }>\r\n                        <IconButton\r\n                          className=\"add-icon\"\r\n                          size=\"small\"\r\n                          sx={{\r\n                            position: 'absolute',\r\n                            bottom: 2,\r\n                            right: 2,\r\n                            opacity: 0,\r\n                            transition: 'opacity 0.3s',\r\n                            '& .MuiSvgIcon-root': {\r\n                              fontSize: '12px'\r\n                            }\r\n                          }}\r\n                          onClick={() => handleClick(user, hour)}\r\n                        >\r\n                          <AddCircleOutlineIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                    </Box>\r\n                  );\r\n                })}\r\n              </Box>\r\n            ))}\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Work Schedule Form */}\r\n      <DayWorkScheduleForm\r\n        open={open}\r\n        onClose={handleClose}\r\n        selectedUser={selected?.user}\r\n        selectedDate={selected?.date}\r\n        selectedHour={selected?.hour}\r\n      />\r\n    </Box>\r\n  );\r\n};\r\n\r\nDayWorkSchedule.propTypes = {\r\n  dateRange: PropTypes.shape({\r\n    startDate: PropTypes.string,\r\n    endDate: PropTypes.string\r\n  })\r\n};\r\n\r\nexport default DayWorkSchedule;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,OAAOC,oBAAoB,MAAM,sCAAsC;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,mBAAmB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,KAAK,CAAC;AAE7D,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,UAAU,GAAG,GAAG;AACtB,MAAMC,UAAU,GAAG,EAAE;AAErB,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAMoB,KAAK,GAAGnB,WAAW,CAACE,YAAY,CAACkB,QAAQ,CAAC,CAAC,CAAC;EAClD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACA,MAAMoC,WAAW,GAAGT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEU,SAAS,GAAG7B,KAAK,CAACmB,SAAS,CAACU,SAAS,CAAC,GAAG7B,KAAK,CAAC,CAAC;EAC/E,MAAM8B,WAAW,GAAG,CAAAX,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,SAAS,OAAKV,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEY,OAAO;;EAE/D;EACAtC,SAAS,CAAC,MAAM;IACd4B,QAAQ,CAACjB,WAAW,CAACmB,QAAQ,CAAC,CAAC,CAAC;EAClC,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;EAEd,MAAMW,WAAW,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;IAClCP,WAAW,CAAC;MACVM,IAAI;MACJC,IAAI;MACJC,IAAI,EAAEP,WAAW,CAACQ,MAAM,CAAC,YAAY;IACvC,CAAC,CAAC;IACFX,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMY,WAAW,GAAGA,CAAA,KAAM;IACxBZ,OAAO,CAAC,KAAK,CAAC;IACdE,WAAW,CAAC,IAAI,CAAC;EACnB,CAAC;;EAED;EACA,MAAMW,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIR,WAAW,IAAIX,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEY,OAAO,EAAE;MACrC,MAAMA,OAAO,GAAG/B,KAAK,CAACmB,SAAS,CAACY,OAAO,CAAC;MACxC,IAAIH,WAAW,CAACW,KAAK,CAAC,CAAC,KAAKR,OAAO,CAACQ,KAAK,CAAC,CAAC,IAAIX,WAAW,CAACY,IAAI,CAAC,CAAC,KAAKT,OAAO,CAACS,IAAI,CAAC,CAAC,EAAE;QACpF,OAAO,GAAGZ,WAAW,CAACQ,MAAM,CAAC,OAAO,CAAC,MAAML,OAAO,CAACK,MAAM,CAAC,SAAS,CAAC,EAAE;MACxE,CAAC,MAAM,IAAIR,WAAW,CAACY,IAAI,CAAC,CAAC,KAAKT,OAAO,CAACS,IAAI,CAAC,CAAC,EAAE;QAChD,OAAO,GAAGZ,WAAW,CAACQ,MAAM,CAAC,OAAO,CAAC,MAAML,OAAO,CAACK,MAAM,CAAC,aAAa,CAAC,EAAE;MAC5E,CAAC,MAAM;QACL,OAAO,GAAGR,WAAW,CAACQ,MAAM,CAAC,aAAa,CAAC,MAAML,OAAO,CAACK,MAAM,CAAC,aAAa,CAAC,EAAE;MAClF;IACF,CAAC,MAAM;MACL,OAAOR,WAAW,CAACQ,MAAM,CAAC,oBAAoB,CAAC;IACjD;EACF,CAAC;EAED,oBACE5B,OAAA,CAACb,GAAG;IAAC8C,CAAC,EAAE,CAAE;IAAAC,QAAA,gBACRlC,OAAA,CAACZ,UAAU;MAAC+C,OAAO,EAAC,IAAI;MAACC,EAAE,EAAE,CAAE;MAAAF,QAAA,GAAC,sBACV,EAACJ,kBAAkB,CAAC,CAAC;IAAA;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eAGbxC,OAAA,CAACb,GAAG;MACFsD,EAAE,EAAE;QACFC,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE,QAAQ;QACpB,sBAAsB,EAAE;UACtBC,MAAM,EAAE;QACV,CAAC;QACD,4BAA4B,EAAE;UAC5BC,eAAe,EAAE,MAAM;UACvBH,YAAY,EAAE;QAChB;MACF,CAAE;MAAAX,QAAA,eAEHlC,OAAA,CAACb,GAAG;QAACsD,EAAE,EAAE;UAAEQ,QAAQ,EAAE,GAAGzC,UAAU,GAAIP,KAAK,CAACG,MAAM,GAAGG,UAAW;QAAK,CAAE;QAAA2B,QAAA,gBAIpElC,OAAA,CAACb,GAAG;UAACsD,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,GAAG,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBAClElC,OAAA,CAACb,GAAG;YACFsD,EAAE,EAAE;cACFC,KAAK,EAAElC,UAAU;cACjBuC,MAAM,EAAEtC,UAAU;cAClByC,OAAO,EAAE,MAAM;cACfI,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,EAAE;cACZC,WAAW,EAAE,gBAAgB;cAC7BC,YAAY,EAAE,gBAAgB;cAC9BX,eAAe,EAAE,SAAS;cAC1BG,QAAQ,EAAE,QAAQ;cAClBS,IAAI,EAAE,CAAC;cACPP,MAAM,EAAE;YACV,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACLvC,KAAK,CAAC4D,GAAG,CAAC,CAACnC,IAAI,EAAEoC,GAAG,kBACnB9D,OAAA,CAACb,GAAG;YAEFsD,EAAE,EAAE;cACFC,KAAK,EAAEnC,UAAU;cACjBwC,MAAM,EAAEtC,UAAU;cAClByC,OAAO,EAAE,MAAM;cACfI,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,QAAQ,EAAE,EAAE;cACZD,UAAU,EAAE,GAAG;cACfE,WAAW,EAAE,gBAAgB;cAC7BC,YAAY,EAAE,gBAAgB;cAC9BX,eAAe,EAAE;YACnB,CAAE;YAAAd,QAAA,EAEDR;UAAI,GAdAoC,GAAG;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeL,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNxC,OAAA,CAACb,GAAG;UAACsD,EAAE,EAAE;YAAEsB,SAAS,EAAE,GAAG;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAA9B,QAAA,EAC5CpB,KAAK,CAAC+C,GAAG,CAAC,CAACpC,IAAI,EAAEwC,IAAI;YAAA,IAAAC,iBAAA;YAAA,oBACpBlE,OAAA,CAACb,GAAG;cAAYsD,EAAE,EAAE;gBAAES,OAAO,EAAE;cAAO,CAAE;cAAAhB,QAAA,gBAEtClC,OAAA,CAACb,GAAG;gBACFsD,EAAE,EAAE;kBACFC,KAAK,EAAElC,UAAU;kBACjB2D,SAAS,EAAE1D,UAAU;kBACrByC,OAAO,EAAE,MAAM;kBACfI,UAAU,EAAE,QAAQ;kBACpBc,GAAG,EAAE,CAAC;kBACNC,EAAE,EAAE,CAAC;kBACLrB,eAAe,EAAE,MAAM;kBACvBU,WAAW,EAAE,gBAAgB;kBAC7BC,YAAY,EAAE,gBAAgB;kBAC9BR,QAAQ,EAAE,QAAQ;kBAClBS,IAAI,EAAE,CAAC;kBACPP,MAAM,EAAE;gBACV,CAAE;gBAAAnB,QAAA,gBAEFlC,OAAA,CAACd,MAAM;kBAACuD,EAAE,EAAE;oBAAEC,KAAK,EAAE,EAAE;oBAAEK,MAAM,EAAE;kBAAG,CAAE;kBAAAb,QAAA,EACnCT,IAAI,CAAC6C,IAAI,GAAG7C,IAAI,CAAC6C,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG;gBAAG;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACTxC,OAAA,CAACb,GAAG;kBAAA+C,QAAA,gBACFlC,OAAA,CAACZ,UAAU;oBAACoE,UAAU,EAAE,GAAI;oBAACC,QAAQ,EAAE,EAAG;oBAAAvB,QAAA,EAAET,IAAI,CAAC6C,IAAI,IAAI;kBAAc;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACrFxC,OAAA,CAACZ,UAAU;oBAAC+C,OAAO,EAAC,SAAS;oBAACqC,KAAK,EAAC,gBAAgB;oBAAAtC,QAAA,EACjD,EAAAgC,iBAAA,GAAAzC,IAAI,CAACgD,WAAW,cAAAP,iBAAA,uBAAhBA,iBAAA,CAAkBI,IAAI,KAAI7C,IAAI,CAACiD,IAAI,IAAI;kBAAS;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,EAEZf,IAAI,CAACkD,YAAY,KAChBlD,IAAI,CAACkD,YAAY,CAACC,SAAS,KAAK,OAAO,IACvCnD,IAAI,CAACkD,YAAY,CAACE,OAAO,KAAK,OAAO,IACrCpD,IAAI,CAACkD,YAAY,CAACG,gBAAgB,KAAK,WAAW,CACnD,iBACC9E,OAAA,CAACZ,UAAU;oBAAC+C,OAAO,EAAC,SAAS;oBAACqC,KAAK,EAAC,SAAS;oBAAC/B,EAAE,EAAE;sBAAES,OAAO,EAAE;oBAAQ,CAAE;oBAAAhB,QAAA,GACpET,IAAI,CAACkD,YAAY,CAACC,SAAS,EAAC,GAAC,EAACnD,IAAI,CAACkD,YAAY,CAACE,OAAO;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CACb;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGLvC,KAAK,CAAC4D,GAAG,CAAC,CAACnC,IAAI,EAAEqD,IAAI,KAAK;gBAAA,IAAAC,kBAAA,EAAAC,mBAAA;gBACzB;gBACA,MAAMC,iBAAiB,GAAGzD,IAAI,CAACkD,YAAY,KACzClD,IAAI,CAACkD,YAAY,CAACC,SAAS,KAAK,OAAO,IACvCnD,IAAI,CAACkD,YAAY,CAACE,OAAO,KAAK,OAAO,IACrCpD,IAAI,CAACkD,YAAY,CAACG,gBAAgB,KAAK,WAAW,CACnD;gBACnB;gBACA,MAAMK,OAAO,GAAGC,QAAQ,CAAC1D,IAAI,CAAC2D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;gBAChD,MAAMC,SAAS,GAAG,CAAAN,kBAAA,GAAAvD,IAAI,CAACkD,YAAY,cAAAK,kBAAA,eAAjBA,kBAAA,CAAmBJ,SAAS,GAAGQ,QAAQ,CAAC3D,IAAI,CAACkD,YAAY,CAACC,SAAS,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;gBAC5G,MAAME,OAAO,GAAG,CAAAN,mBAAA,GAAAxD,IAAI,CAACkD,YAAY,cAAAM,mBAAA,eAAjBA,mBAAA,CAAmBJ,OAAO,GAAGO,QAAQ,CAAC3D,IAAI,CAACkD,YAAY,CAACE,OAAO,CAACQ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;gBACvG,MAAMG,aAAa,GAAGL,OAAO,IAAIG,SAAS,IAAIH,OAAO,GAAGI,OAAO;gBAG7C,oBACEvF,OAAA,CAACb,GAAG;kBAEFsD,EAAE,EAAE;oBACFC,KAAK,EAAEnC,UAAU;oBACjBwC,MAAM,EAAEtC,UAAU;oBAClBiD,WAAW,EAAE,gBAAgB;oBAC7BC,YAAY,EAAE,gBAAgB;oBAC9BR,QAAQ,EAAE,UAAU;oBACpBH,eAAe,EAAEkC,iBAAiB,IAAIM,aAAa,GAAG,SAAS,GAAG,SAAS;oBAC3E,mBAAmB,EAAE;sBAAEC,OAAO,EAAE;oBAAE,CAAC;oBACnC,wBAAwB,EAAE;sBAAEA,OAAO,EAAE;oBAAE;kBACzC,CAAE;kBAAAvD,QAAA,GAGDgD,iBAAiB,IAAIM,aAAa,iBACjCxF,OAAA,CAACb,GAAG;oBACFuG,SAAS,EAAC,eAAe;oBACzBjD,EAAE,EAAE;sBACFU,QAAQ,EAAE,UAAU;sBACpBC,GAAG,EAAE,CAAC;sBACNQ,IAAI,EAAE,CAAC;sBACP+B,KAAK,EAAE,CAAC;sBACRlC,QAAQ,EAAE,KAAK;sBACfe,KAAK,EAAE,cAAc;sBACrBhB,UAAU,EAAE,GAAG;sBACfoC,SAAS,EAAE,QAAQ;sBACnBH,OAAO,EAAE,GAAG;sBACZI,UAAU,EAAE,cAAc;sBAC1BC,UAAU,EAAE,CAAC;sBACbC,QAAQ,EAAE;oBACZ,CAAE;oBAAA7D,QAAA,GAEDT,IAAI,CAACkD,YAAY,CAACG,gBAAgB,KAAK,aAAa,GAAG,OAAO,GAAG,KAAK,eACvE9E,OAAA;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACLf,IAAI,CAACkD,YAAY,CAACC,SAAS,EAAC,GAAC,EAACnD,IAAI,CAACkD,YAAY,CAACE,OAAO;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CACN,eAEDxC,OAAA,CAACV,OAAO;oBAAC0G,KAAK,EAAEd,iBAAiB,IAAIM,aAAa,GAChD,GAAG/D,IAAI,CAACkD,YAAY,CAACG,gBAAgB,KAAKrD,IAAI,CAACkD,YAAY,CAACC,SAAS,IAAInD,IAAI,CAACkD,YAAY,CAACE,OAAO,GAAG,GACrG,cACD;oBAAA3C,QAAA,eACClC,OAAA,CAACX,UAAU;sBACTqG,SAAS,EAAC,UAAU;sBACpBO,IAAI,EAAC,OAAO;sBACZxD,EAAE,EAAE;wBACFU,QAAQ,EAAE,UAAU;wBACpB+C,MAAM,EAAE,CAAC;wBACTP,KAAK,EAAE,CAAC;wBACRF,OAAO,EAAE,CAAC;wBACVI,UAAU,EAAE,cAAc;wBAC1B,oBAAoB,EAAE;0BACpBpC,QAAQ,EAAE;wBACZ;sBACF,CAAE;sBACF0C,OAAO,EAAEA,CAAA,KAAM3E,WAAW,CAACC,IAAI,EAAEC,IAAI,CAAE;sBAAAQ,QAAA,eAEvClC,OAAA,CAACT,oBAAoB;wBAACkE,QAAQ,EAAC;sBAAO;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GA1DLuC,IAAI;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2DN,CAAC;cAEV,CAAC,CAAC;YAAA,GArHMyB,IAAI;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsHT,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxC,OAAA,CAACF,mBAAmB;MAClBkB,IAAI,EAAEA,IAAK;MACXoF,OAAO,EAAEvE,WAAY;MACrBwE,YAAY,EAAEnF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,IAAK;MAC7B6E,YAAY,EAAEpF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,IAAK;MAC7B4E,YAAY,EAAErF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ;IAAK;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC5B,EAAA,CA1PIF,eAAe;EAAA,QACFhB,WAAW,EACdC,WAAW;AAAA;AAAA6G,EAAA,GAFrB9F,eAAe;AA4PrBA,eAAe,CAAC+F,SAAS,GAAG;EAC1B9F,SAAS,EAAElB,SAAS,CAACiH,KAAK,CAAC;IACzBrF,SAAS,EAAE5B,SAAS,CAACkH,MAAM;IAC3BpF,OAAO,EAAE9B,SAAS,CAACkH;EACrB,CAAC;AACH,CAAC;AAED,eAAejG,eAAe;AAAC,IAAA8F,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}