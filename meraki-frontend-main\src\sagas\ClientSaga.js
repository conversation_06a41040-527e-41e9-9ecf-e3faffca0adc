import {all, call, put, takeLatest} from 'redux-saga/effects'
import {ClientService} from "../services";
import { GeneralActions, ClientActions} from "../slices/actions";


function *createClient({type, payload}) {

    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.log("Create Client ",payload)
        const result = yield call(ClientService.createClient, payload.data);
        const clients = yield call(ClientService.getClient,payload.filter)
        console.log("Client Get ",clients)
        yield put(ClientActions.getSuccessfullyClients(clients))
        yield put(GeneralActions.stopLoading(type))
        
    } catch (err) {
       
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

function *updateClient({type,payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.log(" Update Client ",payload.data)
        const result = yield call(ClientService.updateClient, payload.id,payload.data);
        const clients = yield call(ClientService.getClient,payload.filter)
        // const resultClient = yield clients.json()
        // console.log("Client Get ",resultClient.data)
        yield put(ClientActions.getSuccessfullyClients(clients))
        yield put(GeneralActions.stopLoading(type))
        
    } catch (err) {
       
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

function *deleteClient({type,payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.log(" Delete  Client ",payload)
        const result = yield call(ClientService.deleteClient, payload.id);
        const clients = yield call(ClientService.getClient,payload.filter)
        yield put(ClientActions.getSuccessfullyClients(clients))
        yield put(GeneralActions.stopLoading(type))
        
    } catch (err) {
       
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

function *getClient({type,payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.log("Client Get ")
        const clients = yield call(ClientService.getClient,payload)
       
        yield put(ClientActions.getSuccessfullyClients(clients))
        yield put(GeneralActions.stopLoading(type));
       
    } catch (err) {
       
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.message
        }));
    }
}

export function *ClientWatcher() {
    yield all([
        yield takeLatest(ClientActions.createClient.type, createClient),
        yield takeLatest(ClientActions.updateClient.type, updateClient),
        yield takeLatest(ClientActions.deleteClient.type, deleteClient),
        yield takeLatest(ClientActions.getClients.type, getClient),

    ]);
}