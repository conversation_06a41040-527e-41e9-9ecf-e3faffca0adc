'use strict';

const { db } = require("../models");
const Product = db.product;
const Sprint = db.sprint;
const mongoose = require('mongoose');

//Fetch paginated products with sorting.
exports.getProducts = async (queries) => {
    const limit = parseInt(queries?.limit) || 20;  // Default limit is 20
    const page = parseInt(queries?.page) || 1;     // Default page is 1
    const skip = limit * (page - 1);
    const sort = { createdAt: -1 };  // Sort by newest first

    let results = await Product.find().skip(skip).limit(limit).sort(sort);
    const totalRecords = await Product.countDocuments(); // Get total count
    console.log(" Get Products result ", results)
    return {
        pagination: {
            perPage: limit,
            currentPage: page,
            totalRecords,
            totalPages: Math.ceil(totalRecords / limit)
        },
        data: results
    };
};

// Create a new product.
exports.createProduct = async (body) => {
    console.log("Creating Product:", body);

    const { productName, description, reporter, startDate, endDate, taskTags, priority, client, members, visibility, sprintId } = body;

    try {
        // Create the product
        let product = await Product.create({
            productName,
            description,
            startDate,
            endDate,
            taskTags,
            priority,
            client,
            reporter,
            members,
            visibility
        });

        // If sprintId is provided, add the product to the sprint
        if (sprintId && mongoose.Types.ObjectId.isValid(sprintId)) {
            // Check if sprint exists
            const sprint = await db.sprint.findById(sprintId);
            if (sprint) {
                // Update the sprint with the product ID
                sprint.productId = product._id;
                await sprint.save();
                
                console.log(`Product ${product._id} added to sprint ${sprintId}`);
            }
        }

        return {
            message: "Product created successfully",
            data: product
        };
    } catch (error) {
        console.error("Error creating product:", error);
        throw error;
    }
};

//Update product details.
exports.updateProduct = async (id, body) => {
    let existingProduct = await Product.findById(id);
    if (!existingProduct) throw new Error('Product not found');

    console.log("Updating Product:", body);

    // Update fields only if provided in the request
    if (body?.description) existingProduct.description = body.description;
    if (body?.taskTitle) existingProduct.title = body.taskTitle;
    if (body?.startDate) existingProduct.startDate = body.startDate;
    if (body?.endDate) existingProduct.endDate = body.endDate;
    if (body?.members) existingProduct.members = body.members;
    if (body?.sprintId !== undefined) existingProduct.sprintId = body.sprintId;

    await existingProduct.save();
    return existingProduct;
};

//Delete a product.
exports.deleteProduct = async (body) => {
    return await Product.deleteOne({ _id: body._id });
};

// Get a product by ID.
exports.getProductById = async (id) => {
    let result = await Product.findById(id);
    return { data: result };
};

// Create a new task within a product.
exports.createTask = async (id, body) => {
    console.log("Creating Task with body:", body);

    try {
        let existingProduct = await Product.findById(id);
        if (!existingProduct) throw new Error("Product not found");

        // Ensure taskArr exists
        if (!existingProduct.taskArr) {
            existingProduct.taskArr = [];
        }

        // Create task object with sprintId if provided
        let taskObj = {
            taskTitle: body.taskTitle,
            assignee: Array.isArray(body.assignee) ? body.assignee : [body.assignee], // Ensure array format
            reporter: body.reporter,
            priority: body.priority || "Medium", // Default to "Medium" if not provided
            sprintId: body.sprintId || null, // Include sprintId directly in the task object
            addToSprint: body.sprintId ? true : false, // Always set to true if sprintId is provided
            pauseTimes: [], // Initialize empty pauseTimes array
            hours: {} // Initialize empty hours object for date-wise tracking
        };

        console.log("Task object being created:", taskObj);

        // Add task to product
        existingProduct.taskArr.push(taskObj);
        await existingProduct.save();

        // Get the newly created task
        const newTask = existingProduct.taskArr[existingProduct.taskArr.length - 1];
        console.log("New task created:", newTask);

        return {
            message: "Task created successfully",
            data: newTask
        };
    } catch (error) {
        console.error("Error creating task:", error);
        throw error;
    }
};

//Get products by user ID.
exports.getProductsByUser = async (id) => {
    try {
        let result = await Product.find({
            $or: [
                { members: { $in: [id] } },  // User is a member
                { visibility: true }         // Product is visible to all
            ]
        });

        console.log("Products for User:", result);
        return { data: result };
    } catch (error) {
        console.error("Error fetching products:", error);
        throw error;
    }
};


//Update an existing task within a product.
exports.updateTask = async (productId, taskId, body) => {
    console.log("=== Update Task ===");
    console.log("Product ID:", productId);
    console.log("Task ID:", taskId);
    console.log("Payload:", body);

    if (!mongoose.Types.ObjectId.isValid(productId)) {
        throw new Error("Invalid product ID format");
    }

    const product = await Product.findById(productId);
    if (!product) {
        console.error("Product not found");
        throw new Error("Product not found");
    }

    if (!product.taskArr || !Array.isArray(product.taskArr)) {
        throw new Error("Product has no taskArr");
    }

    const task = product.taskArr.id(taskId);
    if (!task) {
        console.error("Task not found in taskArr");
        throw new Error("Task not found");
    }

    // === Update fields based on body ===
    if (body.taskTitle !== undefined) task.taskTitle = body.taskTitle;
    if (body.priority !== undefined) task.priority = body.priority;
    if (body.billingStatus !== undefined) task.billingStatus = body.billingStatus;
    if (body.taskType !== undefined) task.taskType = body.taskType;
    if (body.totalActiveTime !== undefined) {
        // If totalActiveTime is provided, use it to update totalSpent and totalHours
        const activeTimeHours = body.totalActiveTime / 3600; // Convert seconds to hours
        task.totalSpent = activeTimeHours;
        task.totalHours = activeTimeHours;
    }
    
    // Handle sprint assignment
    if (body.sprintId !== undefined) {
        // If sprintId is null, remove the task from any sprint
        if (body.sprintId === null) {
            task.sprintId = null;
            task.addToSprint = false;
        } 
        // Otherwise, assign to the specified sprint
        else if (mongoose.Types.ObjectId.isValid(body.sprintId)) {
            task.sprintId = body.sprintId;
            task.addToSprint = true; // Always set to true when sprintId is provided
        }
    }

    // Handle assignedHour and assignedMinute into task.hours
    if (body.assignedHour || body.assignedMinute) {
        task.hours = task.hours || {};
        task.hours.assignedHour = body.assignedHour || "00";
        task.hours.assignedMinute = body.assignedMinute || "00";
        task.markModified("hours");
    }

    await product.save();
    console.log("After Updating Task ", product);
    return task;
};


//Start a task timer.
exports.startTask = async (productId, taskId, date) => {
  try {
    console.log("startTask called with:", { productId, taskId, date });

    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error("Invalid product ID format");
    }
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      throw new Error("Invalid task ID format");
    }

    const product = await Product.findById(productId);
    if (!product) throw new Error("Product not found");

    const task = product.taskArr.id(taskId);
    if (!task) throw new Error("Task not found");

    const startTime = new Date();
    const dateKey = date || startTime.toISOString().split("T")[0];

    if (task.taskStatus === "In Progress") {
      throw new Error("Task is already running");
    }

    if (task.taskStatus === "Pause") {
      console.log("Resuming paused task");
      // When resuming, track when we resumed
      task.lastResumeTime = startTime;
      
      // Don't reset the first start time or pause history
    } else {
      // New task - reset tracking data
      task.firstStartTime = startTime; // Track the very first start time
      task.pauseTimes = [];
      task.totalPausedTime = 0; // Reset total paused time in seconds
      
      // Initialize hours Map for new task
      task.hours = new Map();
      console.log("Starting new task");
    }

    // Set current start time and status
    task.startTime = startTime;
    task.currentDate = dateKey;
    task.taskStatus = "In Progress";

    // Tell Mongoose the nested task was modified
    const taskIndex = product.taskArr.findIndex(t => t._id.toString() === taskId.toString());
    product.markModified(`taskArr.${taskIndex}`);
    product.markModified(`taskArr.${taskIndex}.startTime`);
    product.markModified(`taskArr.${taskIndex}.firstStartTime`);
    product.markModified(`taskArr.${taskIndex}.taskStatus`);
    product.markModified(`taskArr.${taskIndex}.pauseTimes`);
    product.markModified(`taskArr.${taskIndex}.totalPausedTime`);
    product.markModified(`taskArr.${taskIndex}.hours`);
    product.markModified(`taskArr.${taskIndex}.lastResumeTime`);

    await product.save();

    const taskToReturn = task.toObject ? task.toObject() : JSON.parse(JSON.stringify(task));
    console.log("Task resumed or started with startTime:", taskToReturn.startTime);

    return taskToReturn;
  } catch (error) {
    console.error("Error in startTask:", error.message);
    throw error;
  }
};


exports.submitTask = async (productId, taskId, elapsedTime, date, pauseHistory) => {
  try {
    console.log("stopTask called with:", { productId, taskId, elapsedTime, date });

    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error("Invalid product ID format");
    }

    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      throw new Error("Invalid task ID format");
    }

    const product = await Product.findById(productId);
    if (!product) throw new Error("Product not found");

    const task = product.taskArr.id(taskId);
    if (!task) throw new Error("Task not found");

    if (!task.startTime) throw new Error("Task was never started");

    const now = new Date();
    const dateKey = date || now.toISOString().split("T")[0];

    // If this is a direct stop (without pause), add the current session time
    if (task.taskStatus !== "Pause") {
      const currentStartTime = new Date(task.startTime);
      const currentSessionSeconds = Math.floor((now - currentStartTime) / 1000);
      
      // Record this final session in pauseTimes for consistency
      task.pauseTimes = task.pauseTimes || [];
      task.pauseTimes.push({
        startTime: currentStartTime,
        pauseTime: now,
        elapsedSeconds: currentSessionSeconds,
        date: dateKey
      });
      
      // Add to total active time
      const currentSessionHours = currentSessionSeconds / 3600;
      const currentHours = task.hours.get(dateKey) || 0;
      task.hours.set(dateKey, currentHours + currentSessionHours);
    }

    // Calculate total time using start time - end time - total pause time
    let totalTimeSeconds = 0;
    
    if (task.firstStartTime) {
      // Calculate total elapsed time from first start to now
      const firstStart = new Date(task.firstStartTime);
      totalTimeSeconds = Math.floor((now - firstStart) / 1000);
      
      // Subtract total paused time
      totalTimeSeconds -= (task.totalPausedTime || 0);
      
      console.log(`Total time calculation: ${totalTimeSeconds} seconds`);
      console.log(`  First start: ${firstStart}`);
      console.log(`  End time: ${now}`);
      console.log(`  Total paused: ${task.totalPausedTime || 0} seconds`);
    } else {
      // Fallback to summing hours if firstStartTime is not available
      task.hours.forEach((value, key) => {
        if (typeof value === 'number' && /^\d{4}-\d{2}-\d{2}$/.test(key)) {
          totalTimeSeconds += value * 3600; // Convert hours to seconds
        }
      });
    }
    
    // Convert total seconds to hours
    const totalHours = totalTimeSeconds / 3600;

    // Update fields
    task.totalSpent = totalHours;
    task.totalHours = totalHours;
    task.endTime = now;
    task.startTime = null;
    task.taskStatus = "Completed";

    console.log(`Task stopped. Total hours: ${totalHours}`);

    // Ensure mongoose knows the nested object was modified
    product.markModified("taskArr");
    const taskIndex = product.taskArr.indexOf(task);
    product.markModified(`taskArr.${taskIndex}.hours`);
    product.markModified(`taskArr.${taskIndex}.pauseTimes`);
    product.markModified(`taskArr.${taskIndex}.totalPausedTime`);
    product.markModified(`taskArr.${taskIndex}.endTime`);

    await product.save();

    // Create a clean object to return
    const taskToReturn = task.toObject ? task.toObject() : JSON.parse(JSON.stringify(task));
    return taskToReturn;
  } catch (error) {
    console.error("Error in stopTask:", error.message, error.stack);
    throw error;
  }
};

exports.pauseTask = async (productId, taskId, elapsedTime, pauseTime, date, startTime) => {
  try {
    console.log("pauseTask called with:", { productId, taskId, elapsedTime, pauseTime, date, startTime });

    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error("Invalid product ID format");
    }

    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      throw new Error("Invalid task ID format");
    }

    const product = await Product.findById(productId);
    if (!product) throw new Error("Product not found");

    const task = product.taskArr.id(taskId);
    if (!task) throw new Error("Task not found");
    if (!task.startTime) throw new Error("Task was never started");

    const pauseTimeDate = pauseTime ? new Date(pauseTime) : new Date();
    const taskStartTime = new Date(task.startTime);
    
    // Calculate pause duration in seconds
    let pauseDurationSeconds;
    if (elapsedTime !== undefined) {
      // If frontend provides elapsed time, use it to calculate pause duration
      // This is the time since the last resume or start
      pauseDurationSeconds = elapsedTime;
      console.log(`Using frontend elapsed time: ${pauseDurationSeconds} seconds`);
    } else {
      // Calculate from timestamps
      pauseDurationSeconds = Math.floor((pauseTimeDate - taskStartTime) / 1000);
      console.log(`Calculated pause duration: ${pauseDurationSeconds} seconds`);
    }

    const dateKey = date || pauseTimeDate.toISOString().split("T")[0];
    console.log(`Recording pause for date: ${dateKey}`);

    // Record pause event in pauseTimes array
    task.pauseTimes = task.pauseTimes || [];
    task.pauseTimes.push({
      startTime: taskStartTime,
      pauseTime: pauseTimeDate,
      elapsedSeconds: pauseDurationSeconds,
      date: dateKey
    });

    // Update total paused time
    task.totalPausedTime = (task.totalPausedTime || 0) + pauseDurationSeconds;
    console.log(`Updated total paused time: ${task.totalPausedTime} seconds`);

    // Initialize hours as Map if it doesn't exist
    if (!task.hours) {
      task.hours = new Map();
    }
    
    // Calculate active time for this session in hours
    const activeTimeHours = pauseDurationSeconds / 3600;
    
    // Store this session's duration in hours object
    const currentHours = task.hours.get(dateKey) || 0;
    task.hours.set(dateKey, currentHours + activeTimeHours);

    task.lastPauseTime = pauseTimeDate;
    task.taskStatus = "Pause";

    // Calculate total hours from active time
    // We'll do a more accurate calculation in stopTask
    let totalHours = 0;
    task.hours.forEach((value, key) => {
      if (typeof value === 'number' && /^\d{4}-\d{2}-\d{2}$/.test(key)) {
        totalHours += value;
      }
    });

    task.totalSpent = totalHours;
    task.totalHours = totalHours;

    // Ensure mongoose knows the nested objects were modified
    product.markModified("taskArr");
    const taskIndex = product.taskArr.indexOf(task);
    product.markModified(`taskArr.${taskIndex}.hours`);
    product.markModified(`taskArr.${taskIndex}.pauseTimes`);
    product.markModified(`taskArr.${taskIndex}.totalPausedTime`);
    product.markModified(`taskArr.${taskIndex}.lastPauseTime`);
    product.markModified(`taskArr.${taskIndex}.lastResumeTime`);

    await product.save();

    // Create a clean object to return
    const taskToReturn = task.toObject ? task.toObject() : JSON.parse(JSON.stringify(task));
    console.log("Task to return:", JSON.stringify(taskToReturn));

    return taskToReturn;
  } catch (error) {
    console.error("Error in pauseTask:", error);
    throw error;
  }
};


exports.deleteCheckOutTime = async (_id) => {
 
  console.log("Erase Check Out Time : ",_id)
  const updatedDoc = await Activity.findByIdAndUpdate(
    _id,
    { $unset: { checkOutTime: "" } },
    { new: true } // <-- This returns the updated document
  );
  console.log("Erase Check Out Time Updated Doc : ",updatedDoc)
 
  return updatedDoc;
}
 
 
exports.todayActivity = async (params) => {
  console.log("Parameter for today's activity:", params);
 
  const startOfDay = new Date();
  startOfDay.setHours(0, 0, 0, 0);
 
  const endOfDay = new Date();
  endOfDay.setHours(23, 59, 59, 999);
 
  try {
    const result = await Activity.findOne({
      user: params.id,
      createdAt: { $gte: startOfDay, $lte: endOfDay }
    });
    console.log("Today Parameter result : ",result)
    return  result
  
  } catch (error) {
    console.error("Error fetching today's activity:", error);
    throw new Error("Error fetching today's activity");
  }
};



// Helper function to calculate total hours from the hours object
function calculateTotalHours(hoursObj) {
  if (!hoursObj || typeof hoursObj !== 'object') return 0;
  
  // Filter out non-date keys (like assignedHour and assignedMinute)
  const dateKeys = Object.keys(hoursObj).filter(key => 
    /^\d{4}-\d{2}-\d{2}$/.test(key)
  );
  
  // Sum all values for date keys
  return dateKeys.reduce((total, key) => {
    const hours = hoursObj[key];
    return total + (typeof hours === 'number' ? hours : 0);
  }, 0);
}