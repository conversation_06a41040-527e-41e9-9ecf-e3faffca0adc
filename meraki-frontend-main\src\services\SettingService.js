import { get, patch, del } from "../utils/api";

async function GetSetting() {
    try {
        const response = await get('setting');
        return response.data;
    } catch (error) {
        console.error("GetSetting error:", error);
        throw error;
    }
}

async function UpdateSetting(id, params) {
    try {
        const response = await patch(`setting/${id}`, params);
        return response;
    } catch (error) {
        console.error("UpdateSetting error:", error);
        throw error;
    }
}

async function AddCompanyLeave(id, params) {
    try {
        const response = await patch(`setting/company/leave/${id}`, params);
        return response.data;
    } catch (error) {
        console.error("AddCompanyLeave error:", error);
        throw error;
    }
}

async function DeleteCompanyLeaves(id) {
    try {
        const response = await del(`setting/delete/company/leave/${id}`);
        return response.data;
    } catch (error) {
        console.error("DeleteCompanyLeaves error:", error);
        throw error;
    }
}

export const SettingService = {
    GetSetting,
    UpdateSetting,
    AddCompanyLeave,
    DeleteCompanyLeaves
};