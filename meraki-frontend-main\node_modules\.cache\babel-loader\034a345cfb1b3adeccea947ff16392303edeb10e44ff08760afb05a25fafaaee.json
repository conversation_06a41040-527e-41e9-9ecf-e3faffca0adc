{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\ScheduleManager.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, Box, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Chip, Alert, Tooltip } from '@mui/material';\nimport { Edit, Delete, Schedule, AccessTime, CalendarToday } from '@mui/icons-material';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport { UserActions, GeneralActions } from 'slices/actions';\nimport { GeneralSelector } from 'selectors';\nimport WorkScheduleUtils from 'utils/workScheduleUtils';\nimport OptimizedScheduleForm from './OptimizedScheduleForm';\nimport dayjs from 'dayjs';\nimport { Divider } from '@mui/material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ScheduleManager = ({\n  open,\n  onClose,\n  selectedUser,\n  selectedDate\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [showAdvancedForm, setShowAdvancedForm] = useState(false);\n  const [editingSchedule, setEditingSchedule] = useState(null);\n  const [scheduleType, setScheduleType] = useState('daily');\n  useEffect(() => {\n    if (success) {\n      toast.success(`Schedule updated successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      // Reset success state\n      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));\n    }\n  }, [success, dispatch]);\n  const userSchedules = (selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.workSchedules) || [];\n  const defaultSchedule = selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.workSchedule;\n\n  // Get effective schedule for the selected date\n  const effectiveSchedule = selectedUser ? WorkScheduleUtils.getEffectiveSchedule(selectedUser, selectedDate) : null;\n  const handleAddSchedule = type => {\n    setScheduleType(type);\n    setEditingSchedule(null);\n    setShowAdvancedForm(true);\n  };\n  const handleEditSchedule = schedule => {\n    setEditingSchedule(schedule);\n    setScheduleType(schedule.type);\n    setShowAdvancedForm(true);\n  };\n  const handleDeleteSchedule = scheduleId => {\n    if (!selectedUser) {\n      return;\n    }\n    const updatedSchedules = userSchedules.filter(s => s.id !== scheduleId);\n    const params = {\n      id: selectedUser._id,\n      workSchedules: updatedSchedules\n    };\n    dispatch(UserActions.updateUser(params));\n    toast.success('Schedule deleted successfully!');\n  };\n  const getScheduleTypeIcon = type => {\n    switch (type) {\n      case 'time_specific':\n        return /*#__PURE__*/_jsxDEV(AccessTime, {\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 36\n        }, this);\n      case 'daily':\n        return /*#__PURE__*/_jsxDEV(CalendarToday, {\n          color: \"secondary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 28\n        }, this);\n      case 'weekly':\n        return /*#__PURE__*/_jsxDEV(Schedule, {\n          color: \"info\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 29\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Schedule, {\n          color: \"action\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 23\n        }, this);\n    }\n  };\n  const getScheduleTypeColor = type => {\n    switch (type) {\n      case 'time_specific':\n        return 'error';\n      case 'daily':\n        return 'warning';\n      case 'weekly':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n  const formatScheduleDisplay = schedule => {\n    const startTime = schedule.startTime;\n    const endTime = schedule.endTime;\n    const template = schedule.scheduleTemplate === 'day_shift' ? 'Day' : 'Night';\n    let dateInfo = '';\n    if (schedule.type === 'time_specific' && schedule.specificDate) {\n      dateInfo = ` on ${dayjs(schedule.specificDate).format('MMM D, YYYY')}`;\n    } else if (schedule.type === 'daily' && schedule.specificDate) {\n      dateInfo = ` on ${dayjs(schedule.specificDate).format('MMM D, YYYY')}`;\n    } else if (schedule.type === 'weekly' && schedule.daysOfWeek) {\n      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n      const days = schedule.daysOfWeek.map(d => dayNames[d]).join(', ');\n      dateInfo = ` on ${days}`;\n    }\n    return `${template} Shift: ${startTime}-${endTime}${dateInfo}`;\n  };\n  const isScheduleActive = schedule => {\n    if (!selectedDate) {\n      return false;\n    }\n    return WorkScheduleUtils.isScheduleApplicable(schedule, dayjs(selectedDate));\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Dialog, {\n      open: open,\n      onClose: onClose,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            children: [\"Schedule Manager - \", selectedUser === null || selectedUser === void 0 ? void 0 : selectedUser.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: \"Manage multiple work schedules with priority-based resolution\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [effectiveSchedule && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: [\"Current Effective Schedule for \", selectedDate, \":\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: [effectiveSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift', \":\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 19\n              }, this), \" \", ' ', effectiveSchedule.startTime, \" - \", effectiveSchedule.endTime, \" (\", effectiveSchedule.minimumHours, \"h)\", effectiveSchedule.type && /*#__PURE__*/_jsxDEV(Chip, {\n                label: effectiveSchedule.type.replace('_', ' ').toUpperCase(),\n                size: \"small\",\n                color: getScheduleTypeColor(effectiveSchedule.type),\n                sx: {\n                  ml: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Add New Schedule:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 1,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(AccessTime, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleAddSchedule('time_specific'),\n              children: \"Time-Specific\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(CalendarToday, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleAddSchedule('daily'),\n              children: \"Daily\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              size: \"small\",\n              startIcon: /*#__PURE__*/_jsxDEV(Schedule, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 28\n              }, this),\n              onClick: () => handleAddSchedule('weekly'),\n              children: \"Weekly\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), defaultSchedule && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Default Schedule:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            sx: {\n              bgcolor: 'grey.50',\n              borderRadius: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: formatScheduleDisplay(defaultSchedule),\n              secondary: \"Base schedule used when no specific schedule applies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Chip, {\n              label: \"DEFAULT\",\n              size: \"small\",\n              color: \"default\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: [\"Specific Schedules (\", userSchedules.length, \"):\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), userSchedules.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"No specific schedules created. The default schedule will be used for all times.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          children: userSchedules.sort((a, b) => (b.priority || 1) - (a.priority || 1)).map((schedule, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            sx: {\n              bgcolor: isScheduleActive(schedule) ? 'primary.light' : 'transparent',\n              borderRadius: 1,\n              mb: 1,\n              border: isScheduleActive(schedule) ? '1px solid' : 'none',\n              borderColor: 'primary.main'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                mr: 1\n              },\n              children: getScheduleTypeIcon(schedule.type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                },\n                children: [formatScheduleDisplay(schedule), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `Priority ${schedule.priority || 1}`,\n                  size: \"small\",\n                  color: getScheduleTypeColor(schedule.type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 25\n                }, this), isScheduleActive(schedule) && /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"ACTIVE\",\n                  size: \"small\",\n                  color: \"primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 23\n              }, this),\n              secondary: /*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  children: schedule.description || 'No description'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"text.secondary\",\n                  children: [\"Effective: \", dayjs(schedule.effectiveFrom).format('MMM D'), \" - \", dayjs(schedule.effectiveTo).format('MMM D, YYYY')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n              children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Edit Schedule\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: () => handleEditSchedule(schedule),\n                  sx: {\n                    mr: 1\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"Delete Schedule\",\n                children: /*#__PURE__*/_jsxDEV(IconButton, {\n                  edge: \"end\",\n                  onClick: () => handleDeleteSchedule(schedule.id),\n                  color: \"error\",\n                  children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this)]\n          }, schedule.id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: 'info.light',\n            borderRadius: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"info.dark\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Schedule Priority:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 15\n            }, this), \" Time-Specific (4) \\u2192 Daily (3) \\u2192 Weekly (2) \\u2192 Default (1). Higher priority schedules override lower ones when multiple schedules apply to the same time.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: onClose,\n          color: \"secondary\",\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(OptimizedScheduleForm, {\n      open: showAdvancedForm,\n      onClose: () => {\n        setShowAdvancedForm(false);\n        setEditingSchedule(null);\n      },\n      selectedUser: selectedUser,\n      selectedDate: selectedDate,\n      scheduleType: scheduleType,\n      editingSchedule: editingSchedule\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 309,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ScheduleManager, \"B2hREI/dL4zTV9rl/uvJWoPC0UE=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = ScheduleManager;\nScheduleManager.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string\n};\nexport default ScheduleManager;\nvar _c;\n$RefreshReg$(_c, \"ScheduleManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "Box", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "IconButton", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Edit", "Delete", "Schedule", "AccessTime", "CalendarToday", "useDispatch", "useSelector", "toast", "PropTypes", "UserActions", "GeneralActions", "GeneralSelector", "WorkScheduleUtils", "OptimizedScheduleForm", "dayjs", "Divider", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScheduleManager", "open", "onClose", "selected<PERSON>ser", "selectedDate", "_s", "dispatch", "success", "updateUser", "type", "showAdvancedForm", "setShowAdvancedForm", "editingSchedule", "setEditingSchedule", "scheduleType", "setScheduleType", "position", "autoClose", "closeOnClick", "removeSuccess", "userSchedules", "workSchedules", "defaultSchedule", "workSchedule", "effectiveSchedule", "getEffectiveSchedule", "handleAddSchedule", "handleEditSchedule", "schedule", "handleDeleteSchedule", "scheduleId", "updatedSchedules", "filter", "s", "id", "params", "_id", "getScheduleTypeIcon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getScheduleTypeColor", "formatScheduleDisplay", "startTime", "endTime", "template", "scheduleTemplate", "dateInfo", "specificDate", "format", "daysOfWeek", "dayNames", "days", "map", "d", "join", "isScheduleActive", "isScheduleApplicable", "children", "max<PERSON><PERSON><PERSON>", "fullWidth", "sx", "display", "alignItems", "gap", "variant", "name", "mb", "gutterBottom", "severity", "minimumHours", "label", "replace", "toUpperCase", "size", "ml", "flexWrap", "startIcon", "onClick", "my", "bgcolor", "borderRadius", "primary", "secondary", "length", "sort", "a", "b", "priority", "index", "border", "borderColor", "mr", "description", "effectiveFrom", "effectiveTo", "title", "edge", "mt", "p", "_c", "propTypes", "bool", "isRequired", "func", "object", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/ScheduleManager.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Typography,\n  Box,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  IconButton,\n  Chip,\n  Alert,\n  Tooltip\n} from '@mui/material';\nimport { Edit, Delete, Schedule, AccessTime, CalendarToday } from '@mui/icons-material';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport { UserActions, GeneralActions } from 'slices/actions';\nimport { GeneralSelector } from 'selectors';\nimport WorkScheduleUtils from 'utils/workScheduleUtils';\nimport OptimizedScheduleForm from './OptimizedScheduleForm';\nimport dayjs from 'dayjs';\nimport { Divider } from '@mui/material';\n\nconst ScheduleManager = ({ open, onClose, selectedUser, selectedDate }) => {\n  const dispatch = useDispatch();\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [showAdvancedForm, setShowAdvancedForm] = useState(false);\n  const [editingSchedule, setEditingSchedule] = useState(null);\n  const [scheduleType, setScheduleType] = useState('daily');\n\n  useEffect(() => {\n    if (success) {\n      toast.success(`Schedule updated successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true,\n      });\n      // Reset success state\n      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));\n    }\n  }, [success, dispatch]);\n\n  const userSchedules = selectedUser?.workSchedules || [];\n  const defaultSchedule = selectedUser?.workSchedule;\n\n  // Get effective schedule for the selected date\n  const effectiveSchedule = selectedUser ? WorkScheduleUtils.getEffectiveSchedule(selectedUser, selectedDate) : null;\n\n  const handleAddSchedule = (type) => {\n    setScheduleType(type);\n    setEditingSchedule(null);\n    setShowAdvancedForm(true);\n  };\n\n  const handleEditSchedule = (schedule) => {\n    setEditingSchedule(schedule);\n    setScheduleType(schedule.type);\n    setShowAdvancedForm(true);\n  };\n\n  const handleDeleteSchedule = (scheduleId) => {\n    if (!selectedUser) {\n      return;\n    }\n\n    const updatedSchedules = userSchedules.filter(s => s.id !== scheduleId);\n    \n    const params = {\n      id: selectedUser._id,\n      workSchedules: updatedSchedules\n    };\n\n    dispatch(UserActions.updateUser(params));\n    toast.success('Schedule deleted successfully!');\n  };\n\n  const getScheduleTypeIcon = (type) => {\n    switch (type) {\n      case 'time_specific': return <AccessTime color=\"primary\" />;\n      case 'daily': return <CalendarToday color=\"secondary\" />;\n      case 'weekly': return <Schedule color=\"info\" />;\n      default: return <Schedule color=\"action\" />;\n    }\n  };\n\n  const getScheduleTypeColor = (type) => {\n    switch (type) {\n      case 'time_specific': return 'error';\n      case 'daily': return 'warning';\n      case 'weekly': return 'info';\n      default: return 'default';\n    }\n  };\n\n  const formatScheduleDisplay = (schedule) => {\n    const startTime = schedule.startTime;\n    const endTime = schedule.endTime;\n    const template = schedule.scheduleTemplate === 'day_shift' ? 'Day' : 'Night';\n    \n    let dateInfo = '';\n    if (schedule.type === 'time_specific' && schedule.specificDate) {\n      dateInfo = ` on ${dayjs(schedule.specificDate).format('MMM D, YYYY')}`;\n    } else if (schedule.type === 'daily' && schedule.specificDate) {\n      dateInfo = ` on ${dayjs(schedule.specificDate).format('MMM D, YYYY')}`;\n    } else if (schedule.type === 'weekly' && schedule.daysOfWeek) {\n      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n      const days = schedule.daysOfWeek.map(d => dayNames[d]).join(', ');\n      dateInfo = ` on ${days}`;\n    }\n\n    return `${template} Shift: ${startTime}-${endTime}${dateInfo}`;\n  };\n\n  const isScheduleActive = (schedule) => {\n    if (!selectedDate) {\n      return false;\n    }\n    return WorkScheduleUtils.isScheduleApplicable(schedule, dayjs(selectedDate));\n  };\n\n  return (\n    <>\n      <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <Schedule />\n            <Typography variant=\"h6\">\n              Schedule Manager - {selectedUser?.name}\n            </Typography>\n          </Box>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Manage multiple work schedules with priority-based resolution\n          </Typography>\n        </DialogTitle>\n        \n        <DialogContent>\n          {/* Current Effective Schedule */}\n          {effectiveSchedule && (\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Current Effective Schedule for {selectedDate}:\n              </Typography>\n              <Alert severity=\"info\" sx={{ mb: 2 }}>\n                <Typography variant=\"body2\">\n                  <strong>{effectiveSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}:</strong> {' '}\n                  {effectiveSchedule.startTime} - {effectiveSchedule.endTime} ({effectiveSchedule.minimumHours}h)\n                  {effectiveSchedule.type && (\n                    <Chip \n                      label={effectiveSchedule.type.replace('_', ' ').toUpperCase()} \n                      size=\"small\" \n                      color={getScheduleTypeColor(effectiveSchedule.type)}\n                      sx={{ ml: 1 }}\n                    />\n                  )}\n                </Typography>\n              </Alert>\n            </Box>\n          )}\n\n          {/* Add Schedule Buttons */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"subtitle1\" gutterBottom>\n              Add New Schedule:\n            </Typography>\n            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                startIcon={<AccessTime />}\n                onClick={() => handleAddSchedule('time_specific')}\n              >\n                Time-Specific\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                startIcon={<CalendarToday />}\n                onClick={() => handleAddSchedule('daily')}\n              >\n                Daily\n              </Button>\n              <Button\n                variant=\"outlined\"\n                size=\"small\"\n                startIcon={<Schedule />}\n                onClick={() => handleAddSchedule('weekly')}\n              >\n                Weekly\n              </Button>\n            </Box>\n          </Box>\n\n          <Divider sx={{ my: 2 }} />\n\n          {/* Default Schedule */}\n          {defaultSchedule && (\n            <Box sx={{ mb: 3 }}>\n              <Typography variant=\"subtitle1\" gutterBottom>\n                Default Schedule:\n              </Typography>\n              <ListItem sx={{ bgcolor: 'grey.50', borderRadius: 1 }}>\n                <ListItemText\n                  primary={formatScheduleDisplay(defaultSchedule)}\n                  secondary=\"Base schedule used when no specific schedule applies\"\n                />\n                <Chip label=\"DEFAULT\" size=\"small\" color=\"default\" />\n              </ListItem>\n            </Box>\n          )}\n\n          {/* Specific Schedules */}\n          <Typography variant=\"subtitle1\" gutterBottom>\n            Specific Schedules ({userSchedules.length}):\n          </Typography>\n          \n          {userSchedules.length === 0 ? (\n            <Alert severity=\"info\">\n              No specific schedules created. The default schedule will be used for all times.\n            </Alert>\n          ) : (\n            <List>\n              {userSchedules.sort((a, b) => (b.priority || 1) - (a.priority || 1)).map((schedule, index) => (\n                <ListItem \n                  key={schedule.id || index}\n                  sx={{ \n                    bgcolor: isScheduleActive(schedule) ? 'primary.light' : 'transparent',\n                    borderRadius: 1,\n                    mb: 1,\n                    border: isScheduleActive(schedule) ? '1px solid' : 'none',\n                    borderColor: 'primary.main'\n                  }}\n                >\n                  <Box sx={{ mr: 1 }}>\n                    {getScheduleTypeIcon(schedule.type)}\n                  </Box>\n                  <ListItemText\n                    primary={\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                        {formatScheduleDisplay(schedule)}\n                        <Chip \n                          label={`Priority ${schedule.priority || 1}`} \n                          size=\"small\" \n                          color={getScheduleTypeColor(schedule.type)}\n                        />\n                        {isScheduleActive(schedule) && (\n                          <Chip label=\"ACTIVE\" size=\"small\" color=\"primary\" />\n                        )}\n                      </Box>\n                    }\n                    secondary={\n                      <Box>\n                        <Typography variant=\"caption\" display=\"block\">\n                          {schedule.description || 'No description'}\n                        </Typography>\n                        <Typography variant=\"caption\" color=\"text.secondary\">\n                          Effective: {dayjs(schedule.effectiveFrom).format('MMM D')} - {dayjs(schedule.effectiveTo).format('MMM D, YYYY')}\n                        </Typography>\n                      </Box>\n                    }\n                  />\n                  <ListItemSecondaryAction>\n                    <Tooltip title=\"Edit Schedule\">\n                      <IconButton \n                        edge=\"end\" \n                        onClick={() => handleEditSchedule(schedule)}\n                        sx={{ mr: 1 }}\n                      >\n                        <Edit />\n                      </IconButton>\n                    </Tooltip>\n                    <Tooltip title=\"Delete Schedule\">\n                      <IconButton \n                        edge=\"end\" \n                        onClick={() => handleDeleteSchedule(schedule.id)}\n                        color=\"error\"\n                      >\n                        <Delete />\n                      </IconButton>\n                    </Tooltip>\n                  </ListItemSecondaryAction>\n                </ListItem>\n              ))}\n            </List>\n          )}\n\n          {/* Schedule Priority Explanation */}\n          <Box sx={{ mt: 3, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>\n            <Typography variant=\"caption\" color=\"info.dark\">\n              <strong>Schedule Priority:</strong> Time-Specific (4) → Daily (3) → Weekly (2) → Default (1). \n              Higher priority schedules override lower ones when multiple schedules apply to the same time.\n            </Typography>\n          </Box>\n        </DialogContent>\n\n        <DialogActions>\n          <Button onClick={onClose} color=\"secondary\">\n            Close\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Optimized Work Schedule Form */}\n      <OptimizedScheduleForm\n        open={showAdvancedForm}\n        onClose={() => {\n          setShowAdvancedForm(false);\n          setEditingSchedule(null);\n        }}\n        selectedUser={selectedUser}\n        selectedDate={selectedDate}\n        scheduleType={scheduleType}\n        editingSchedule={editingSchedule}\n      />\n    </>\n  );\n};\n\nScheduleManager.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string\n};\n\nexport default ScheduleManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,OAAO,QACF,eAAe;AACtB,SAASC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,aAAa,QAAQ,qBAAqB;AACvF,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAC5D,SAASC,eAAe,QAAQ,WAAW;AAC3C,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExC,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,YAAY;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,OAAO,GAAGrB,WAAW,CAACK,eAAe,CAACgB,OAAO,CAAClB,WAAW,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC;EACjF,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,OAAO,CAAC;EAEzDC,SAAS,CAAC,MAAM;IACd,IAAI2C,OAAO,EAAE;MACXpB,KAAK,CAACoB,OAAO,CAAC,gCAAgC,EAAE;QAC9CS,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF;MACAZ,QAAQ,CAAChB,cAAc,CAAC6B,aAAa,CAAC,CAAC9B,WAAW,CAACmB,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,EAAE,CAACF,OAAO,EAAED,QAAQ,CAAC,CAAC;EAEvB,MAAMc,aAAa,GAAG,CAAAjB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEkB,aAAa,KAAI,EAAE;EACvD,MAAMC,eAAe,GAAGnB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoB,YAAY;;EAElD;EACA,MAAMC,iBAAiB,GAAGrB,YAAY,GAAGX,iBAAiB,CAACiC,oBAAoB,CAACtB,YAAY,EAAEC,YAAY,CAAC,GAAG,IAAI;EAElH,MAAMsB,iBAAiB,GAAIjB,IAAI,IAAK;IAClCM,eAAe,CAACN,IAAI,CAAC;IACrBI,kBAAkB,CAAC,IAAI,CAAC;IACxBF,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMgB,kBAAkB,GAAIC,QAAQ,IAAK;IACvCf,kBAAkB,CAACe,QAAQ,CAAC;IAC5Bb,eAAe,CAACa,QAAQ,CAACnB,IAAI,CAAC;IAC9BE,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMkB,oBAAoB,GAAIC,UAAU,IAAK;IAC3C,IAAI,CAAC3B,YAAY,EAAE;MACjB;IACF;IAEA,MAAM4B,gBAAgB,GAAGX,aAAa,CAACY,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKJ,UAAU,CAAC;IAEvE,MAAMK,MAAM,GAAG;MACbD,EAAE,EAAE/B,YAAY,CAACiC,GAAG;MACpBf,aAAa,EAAEU;IACjB,CAAC;IAEDzB,QAAQ,CAACjB,WAAW,CAACmB,UAAU,CAAC2B,MAAM,CAAC,CAAC;IACxChD,KAAK,CAACoB,OAAO,CAAC,gCAAgC,CAAC;EACjD,CAAC;EAED,MAAM8B,mBAAmB,GAAI5B,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,eAAe;QAAE,oBAAOZ,OAAA,CAACd,UAAU;UAACuD,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3D,KAAK,OAAO;QAAE,oBAAO7C,OAAA,CAACb,aAAa;UAACsD,KAAK,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACxD,KAAK,QAAQ;QAAE,oBAAO7C,OAAA,CAACf,QAAQ;UAACwD,KAAK,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/C;QAAS,oBAAO7C,OAAA,CAACf,QAAQ;UAACwD,KAAK,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAIlC,IAAI,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,eAAe;QAAE,OAAO,OAAO;MACpC,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,QAAQ;QAAE,OAAO,MAAM;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMmC,qBAAqB,GAAIhB,QAAQ,IAAK;IAC1C,MAAMiB,SAAS,GAAGjB,QAAQ,CAACiB,SAAS;IACpC,MAAMC,OAAO,GAAGlB,QAAQ,CAACkB,OAAO;IAChC,MAAMC,QAAQ,GAAGnB,QAAQ,CAACoB,gBAAgB,KAAK,WAAW,GAAG,KAAK,GAAG,OAAO;IAE5E,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAIrB,QAAQ,CAACnB,IAAI,KAAK,eAAe,IAAImB,QAAQ,CAACsB,YAAY,EAAE;MAC9DD,QAAQ,GAAG,OAAOvD,KAAK,CAACkC,QAAQ,CAACsB,YAAY,CAAC,CAACC,MAAM,CAAC,aAAa,CAAC,EAAE;IACxE,CAAC,MAAM,IAAIvB,QAAQ,CAACnB,IAAI,KAAK,OAAO,IAAImB,QAAQ,CAACsB,YAAY,EAAE;MAC7DD,QAAQ,GAAG,OAAOvD,KAAK,CAACkC,QAAQ,CAACsB,YAAY,CAAC,CAACC,MAAM,CAAC,aAAa,CAAC,EAAE;IACxE,CAAC,MAAM,IAAIvB,QAAQ,CAACnB,IAAI,KAAK,QAAQ,IAAImB,QAAQ,CAACwB,UAAU,EAAE;MAC5D,MAAMC,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAClE,MAAMC,IAAI,GAAG1B,QAAQ,CAACwB,UAAU,CAACG,GAAG,CAACC,CAAC,IAAIH,QAAQ,CAACG,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;MACjER,QAAQ,GAAG,OAAOK,IAAI,EAAE;IAC1B;IAEA,OAAO,GAAGP,QAAQ,WAAWF,SAAS,IAAIC,OAAO,GAAGG,QAAQ,EAAE;EAChE,CAAC;EAED,MAAMS,gBAAgB,GAAI9B,QAAQ,IAAK;IACrC,IAAI,CAACxB,YAAY,EAAE;MACjB,OAAO,KAAK;IACd;IACA,OAAOZ,iBAAiB,CAACmE,oBAAoB,CAAC/B,QAAQ,EAAElC,KAAK,CAACU,YAAY,CAAC,CAAC;EAC9E,CAAC;EAED,oBACEP,OAAA,CAAAE,SAAA;IAAA6D,QAAA,gBACE/D,OAAA,CAAChC,MAAM;MAACoC,IAAI,EAAEA,IAAK;MAACC,OAAO,EAAEA,OAAQ;MAAC2D,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAAF,QAAA,gBAC3D/D,OAAA,CAAC/B,WAAW;QAAA8F,QAAA,gBACV/D,OAAA,CAAC1B,GAAG;UAAC4F,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAN,QAAA,gBACzD/D,OAAA,CAACf,QAAQ;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACZ7C,OAAA,CAAC3B,UAAU;YAACiG,OAAO,EAAC,IAAI;YAAAP,QAAA,GAAC,qBACJ,EAACzD,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiE,IAAI;UAAA;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN7C,OAAA,CAAC3B,UAAU;UAACiG,OAAO,EAAC,OAAO;UAAC7B,KAAK,EAAC,gBAAgB;UAAAsB,QAAA,EAAC;QAEnD;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEd7C,OAAA,CAAC9B,aAAa;QAAA6F,QAAA,GAEXpC,iBAAiB,iBAChB3B,OAAA,CAAC1B,GAAG;UAAC4F,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACjB/D,OAAA,CAAC3B,UAAU;YAACiG,OAAO,EAAC,WAAW;YAACG,YAAY;YAAAV,QAAA,GAAC,iCACZ,EAACxD,YAAY,EAAC,GAC/C;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAACnB,KAAK;YAAC6F,QAAQ,EAAC,MAAM;YAACR,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACnC/D,OAAA,CAAC3B,UAAU;cAACiG,OAAO,EAAC,OAAO;cAAAP,QAAA,gBACzB/D,OAAA;gBAAA+D,QAAA,GAASpC,iBAAiB,CAACwB,gBAAgB,KAAK,WAAW,GAAG,WAAW,GAAG,aAAa,EAAC,GAAC;cAAA;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC,GAAG,EACxGlB,iBAAiB,CAACqB,SAAS,EAAC,KAAG,EAACrB,iBAAiB,CAACsB,OAAO,EAAC,IAAE,EAACtB,iBAAiB,CAACgD,YAAY,EAAC,IAC7F,EAAChD,iBAAiB,CAACf,IAAI,iBACrBZ,OAAA,CAACpB,IAAI;gBACHgG,KAAK,EAAEjD,iBAAiB,CAACf,IAAI,CAACiE,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAE;gBAC9DC,IAAI,EAAC,OAAO;gBACZtC,KAAK,EAAEK,oBAAoB,CAACnB,iBAAiB,CAACf,IAAI,CAAE;gBACpDsD,EAAE,EAAE;kBAAEc,EAAE,EAAE;gBAAE;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACN,eAGD7C,OAAA,CAAC1B,GAAG;UAAC4F,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACjB/D,OAAA,CAAC3B,UAAU;YAACiG,OAAO,EAAC,WAAW;YAACG,YAAY;YAAAV,QAAA,EAAC;UAE7C;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAAC1B,GAAG;YAAC4F,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,CAAC;cAAEY,QAAQ,EAAE;YAAO,CAAE;YAAAlB,QAAA,gBACrD/D,OAAA,CAAC5B,MAAM;cACLkG,OAAO,EAAC,UAAU;cAClBS,IAAI,EAAC,OAAO;cACZG,SAAS,eAAElF,OAAA,CAACd,UAAU;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BsC,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAAC,eAAe,CAAE;cAAAkC,QAAA,EACnD;YAED;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA,CAAC5B,MAAM;cACLkG,OAAO,EAAC,UAAU;cAClBS,IAAI,EAAC,OAAO;cACZG,SAAS,eAAElF,OAAA,CAACb,aAAa;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7BsC,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAAC,OAAO,CAAE;cAAAkC,QAAA,EAC3C;YAED;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT7C,OAAA,CAAC5B,MAAM;cACLkG,OAAO,EAAC,UAAU;cAClBS,IAAI,EAAC,OAAO;cACZG,SAAS,eAAElF,OAAA,CAACf,QAAQ;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBsC,OAAO,EAAEA,CAAA,KAAMtD,iBAAiB,CAAC,QAAQ,CAAE;cAAAkC,QAAA,EAC5C;YAED;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7C,OAAA,CAACF,OAAO;UAACoE,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAA1C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAGzBpB,eAAe,iBACdzB,OAAA,CAAC1B,GAAG;UAAC4F,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACjB/D,OAAA,CAAC3B,UAAU;YAACiG,OAAO,EAAC,WAAW;YAACG,YAAY;YAAAV,QAAA,EAAC;UAE7C;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7C,OAAA,CAACxB,QAAQ;YAAC0F,EAAE,EAAE;cAAEmB,OAAO,EAAE,SAAS;cAAEC,YAAY,EAAE;YAAE,CAAE;YAAAvB,QAAA,gBACpD/D,OAAA,CAACvB,YAAY;cACX8G,OAAO,EAAExC,qBAAqB,CAACtB,eAAe,CAAE;cAChD+D,SAAS,EAAC;YAAsD;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACF7C,OAAA,CAACpB,IAAI;cAACgG,KAAK,EAAC,SAAS;cAACG,IAAI,EAAC,OAAO;cAACtC,KAAK,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACN,eAGD7C,OAAA,CAAC3B,UAAU;UAACiG,OAAO,EAAC,WAAW;UAACG,YAAY;UAAAV,QAAA,GAAC,sBACvB,EAACxC,aAAa,CAACkE,MAAM,EAAC,IAC5C;QAAA;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZtB,aAAa,CAACkE,MAAM,KAAK,CAAC,gBACzBzF,OAAA,CAACnB,KAAK;UAAC6F,QAAQ,EAAC,MAAM;UAAAX,QAAA,EAAC;QAEvB;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER7C,OAAA,CAACzB,IAAI;UAAAwF,QAAA,EACFxC,aAAa,CAACmE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,QAAQ,IAAI,CAAC,KAAKF,CAAC,CAACE,QAAQ,IAAI,CAAC,CAAC,CAAC,CAACnC,GAAG,CAAC,CAAC3B,QAAQ,EAAE+D,KAAK,kBACvF9F,OAAA,CAACxB,QAAQ;YAEP0F,EAAE,EAAE;cACFmB,OAAO,EAAExB,gBAAgB,CAAC9B,QAAQ,CAAC,GAAG,eAAe,GAAG,aAAa;cACrEuD,YAAY,EAAE,CAAC;cACfd,EAAE,EAAE,CAAC;cACLuB,MAAM,EAAElC,gBAAgB,CAAC9B,QAAQ,CAAC,GAAG,WAAW,GAAG,MAAM;cACzDiE,WAAW,EAAE;YACf,CAAE;YAAAjC,QAAA,gBAEF/D,OAAA,CAAC1B,GAAG;cAAC4F,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE,CAAE;cAAAlC,QAAA,EAChBvB,mBAAmB,CAACT,QAAQ,CAACnB,IAAI;YAAC;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eACN7C,OAAA,CAACvB,YAAY;cACX8G,OAAO,eACLvF,OAAA,CAAC1B,GAAG;gBAAC4F,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEC,UAAU,EAAE,QAAQ;kBAAEC,GAAG,EAAE;gBAAE,CAAE;gBAAAN,QAAA,GACxDhB,qBAAqB,CAAChB,QAAQ,CAAC,eAChC/B,OAAA,CAACpB,IAAI;kBACHgG,KAAK,EAAE,YAAY7C,QAAQ,CAAC8D,QAAQ,IAAI,CAAC,EAAG;kBAC5Cd,IAAI,EAAC,OAAO;kBACZtC,KAAK,EAAEK,oBAAoB,CAACf,QAAQ,CAACnB,IAAI;gBAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC,EACDgB,gBAAgB,CAAC9B,QAAQ,CAAC,iBACzB/B,OAAA,CAACpB,IAAI;kBAACgG,KAAK,EAAC,QAAQ;kBAACG,IAAI,EAAC,OAAO;kBAACtC,KAAK,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACN;cACD2C,SAAS,eACPxF,OAAA,CAAC1B,GAAG;gBAAAyF,QAAA,gBACF/D,OAAA,CAAC3B,UAAU;kBAACiG,OAAO,EAAC,SAAS;kBAACH,OAAO,EAAC,OAAO;kBAAAJ,QAAA,EAC1ChC,QAAQ,CAACmE,WAAW,IAAI;gBAAgB;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACb7C,OAAA,CAAC3B,UAAU;kBAACiG,OAAO,EAAC,SAAS;kBAAC7B,KAAK,EAAC,gBAAgB;kBAAAsB,QAAA,GAAC,aACxC,EAAClE,KAAK,CAACkC,QAAQ,CAACoE,aAAa,CAAC,CAAC7C,MAAM,CAAC,OAAO,CAAC,EAAC,KAAG,EAACzD,KAAK,CAACkC,QAAQ,CAACqE,WAAW,CAAC,CAAC9C,MAAM,CAAC,aAAa,CAAC;gBAAA;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACF7C,OAAA,CAACtB,uBAAuB;cAAAqF,QAAA,gBACtB/D,OAAA,CAAClB,OAAO;gBAACuH,KAAK,EAAC,eAAe;gBAAAtC,QAAA,eAC5B/D,OAAA,CAACrB,UAAU;kBACT2H,IAAI,EAAC,KAAK;kBACVnB,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAACC,QAAQ,CAAE;kBAC5CmC,EAAE,EAAE;oBAAE+B,EAAE,EAAE;kBAAE,CAAE;kBAAAlC,QAAA,eAEd/D,OAAA,CAACjB,IAAI;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACV7C,OAAA,CAAClB,OAAO;gBAACuH,KAAK,EAAC,iBAAiB;gBAAAtC,QAAA,eAC9B/D,OAAA,CAACrB,UAAU;kBACT2H,IAAI,EAAC,KAAK;kBACVnB,OAAO,EAAEA,CAAA,KAAMnD,oBAAoB,CAACD,QAAQ,CAACM,EAAE,CAAE;kBACjDI,KAAK,EAAC,OAAO;kBAAAsB,QAAA,eAEb/D,OAAA,CAAChB,MAAM;oBAAA0D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACa,CAAC;UAAA,GAxDrBd,QAAQ,CAACM,EAAE,IAAIyD,KAAK;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyDjB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,eAGD7C,OAAA,CAAC1B,GAAG;UAAC4F,EAAE,EAAE;YAAEqC,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEnB,OAAO,EAAE,YAAY;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAvB,QAAA,eAC/D/D,OAAA,CAAC3B,UAAU;YAACiG,OAAO,EAAC,SAAS;YAAC7B,KAAK,EAAC,WAAW;YAAAsB,QAAA,gBAC7C/D,OAAA;cAAA+D,QAAA,EAAQ;YAAkB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2KAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAEhB7C,OAAA,CAAC7B,aAAa;QAAA4F,QAAA,eACZ/D,OAAA,CAAC5B,MAAM;UAAC+G,OAAO,EAAE9E,OAAQ;UAACoC,KAAK,EAAC,WAAW;UAAAsB,QAAA,EAAC;QAE5C;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7C,OAAA,CAACJ,qBAAqB;MACpBQ,IAAI,EAAES,gBAAiB;MACvBR,OAAO,EAAEA,CAAA,KAAM;QACbS,mBAAmB,CAAC,KAAK,CAAC;QAC1BE,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAE;MACFV,YAAY,EAAEA,YAAa;MAC3BC,YAAY,EAAEA,YAAa;MAC3BU,YAAY,EAAEA,YAAa;MAC3BF,eAAe,EAAEA;IAAgB;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;AAACrC,EAAA,CApSIL,eAAe;EAAA,QACFf,WAAW,EACZC,WAAW;AAAA;AAAAoH,EAAA,GAFvBtG,eAAe;AAsSrBA,eAAe,CAACuG,SAAS,GAAG;EAC1BtG,IAAI,EAAEb,SAAS,CAACoH,IAAI,CAACC,UAAU;EAC/BvG,OAAO,EAAEd,SAAS,CAACsH,IAAI,CAACD,UAAU;EAClCtG,YAAY,EAAEf,SAAS,CAACuH,MAAM;EAC9BvG,YAAY,EAAEhB,SAAS,CAACwH;AAC1B,CAAC;AAED,eAAe5G,eAAe;AAAC,IAAAsG,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}