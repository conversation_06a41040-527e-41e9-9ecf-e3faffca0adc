import {get, post, patch, del} from "../utils/api";
import { getApiUrl } from "../utils/apiConfig";

// Use the department endpoint directly without the full URL
// The api.js utility will handle adding the base URL
const GetDepartments = async (params) => get('department', params);

const GetDepartmentById = async (id) => get(`department/${id}`);

const CreateDepartment = async (params) => post('department', params);

const UpdateDepartment = async (id, params) => patch(`department/${id}`, params);

const DeleteDepartment = async (id) => del(`department/${id}`);

export const DepartmentService = {
    GetDepartments,
    GetDepartmentById,
    CreateDepartment,
    UpdateDepartment,
    DeleteDepartment
};
