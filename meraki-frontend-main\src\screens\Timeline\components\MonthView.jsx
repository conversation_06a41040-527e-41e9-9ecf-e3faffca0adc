import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography, Paper, Grid, Tooltip } from '@mui/material';
import dayjs from 'dayjs';

const MonthView = ({ data, startDate }) => {
  const [calendarDays, setCalendarDays] = useState([]);
  const [monthData, setMonthData] = useState({
    month: '',
    year: '',
    daysInMonth: 0,
    firstDayOfMonth: 0
  });

  useEffect(() => {
    if (!startDate) { return; }

    const date = dayjs(startDate);
    const month = date.month();
    const year = date.year();
    const daysInMonth = date.daysInMonth();
    const firstDayOfMonth = date.startOf('month').day(); // 0 = Sunday, 1 = Monday, etc.

    setMonthData({
      month,
      year,
      daysInMonth,
      firstDayOfMonth
    });

    // Generate calendar days
    generateCalendarDays(daysInMonth, firstDayOfMonth, month, year, data);
  }, [startDate, data]);

  // Generate calendar days with data
  const generateCalendarDays = (daysInMonth, firstDayOfMonth, month, year, timelineData) => {
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push({ day: null, isEmpty: true });
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const currentDate = dayjs(new Date(year, month, day));
      const formattedDate = currentDate.format('DD-MM-YYYY');

      // Find matching data for this day
      const dayData = timelineData.find(item => {
        const itemDate = item.date.split(' ')[0]; // Extract date part (DD-MM-YYYY)
        return itemDate === formattedDate;
      });

      days.push({
        day,
        isEmpty: false,
        isToday: currentDate.isSame(dayjs(), 'day'),
        isWeekend: currentDate.day() === 0 || currentDate.day() === 6,
        data: dayData || null,
        isHoliday: dayData && dayData.atwork === "--" && dayData.clockin === "--"
      });
    }

    // Add empty cells to complete the last row if needed
    const totalCells = Math.ceil(days.length / 7) * 7;
    for (let i = days.length; i < totalCells; i++) {
      days.push({ day: null, isEmpty: true });
    }

    setCalendarDays(days);
  };

  // Get month name
  const getMonthName = (month) => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return monthNames[month];
  };

  // Render day cell
  const renderDayCell = (dayInfo) => {
    if (dayInfo.isEmpty) {
      return <Box className="calendar-day empty" sx={{
        height: '90px',
        border: '1px solid #f0f0f0',
        backgroundColor: '#fafafa'
      }}></Box>;
    }

    let backgroundColor = dayInfo.isWeekend ? '#f9f9f9' : 'white';
    let textColor = 'inherit';
    let workTime = '--';
    let borderColor = '1px solid #e0e0e0';

    if (dayInfo.isToday) {
      backgroundColor = '#e3f2fd';
      borderColor = '2px solid #2196f3';
    }

    if (dayInfo.isHoliday) {
      backgroundColor = '#fff8e1';
      textColor = '#f44336';
      workTime = 'Holiday';
    } else if (dayInfo.data && dayInfo.data.atwork !== "--") {
      workTime = dayInfo.data.atwork;
      textColor = '#4CAF50';
    }

    return (
      <Box
        className="calendar-day"
        sx={{
          height: '90px',
          border: borderColor,
          p: 0.75,
          backgroundColor,
          position: 'relative',
          display: 'flex',
          flexDirection: 'column',
          '&:hover': {
            backgroundColor: dayInfo.isToday ? '#bbdefb' : '#f5f5f5'
          }
        }}
      >
        {/* Day number in top-right corner */}
        <Box sx={{
          alignSelf: 'flex-end',
          mb: 0.5,
          mr: 0.5
        }}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: dayInfo.isToday ? 'bold' : 'normal',
              fontSize: '0.85rem'
            }}
          >
            {dayInfo.day}
          </Typography>
        </Box>

        {/* Main content - centered in the cell */}
        <Box sx={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '100%'
        }}>
          {dayInfo.isHoliday ? (
            <Typography
              variant="body2"
              sx={{
                color: '#f44336',
                fontWeight: 'bold'
              }}
            >
              Holiday
            </Typography>
          ) : (
            <Box sx={{ textAlign: 'center', width: '100%' }}>
              <Typography
                variant="body2"
                sx={{
                  color: textColor,
                  fontWeight: workTime !== '--' ? 'bold' : 'normal'
                }}
              >
                {workTime}
              </Typography>

              {/* Add mini progress bar for days with data */}
              {dayInfo.data && dayInfo.data.atwork !== "--" && (
                <Box sx={{ mt: 1, mb: 1 }}>
                  {renderMiniProgressBar(dayInfo.data)}
                </Box>
              )}

              {dayInfo.data && dayInfo.data.clockin !== "--" && (
                <>
                  {/* Clock In Time */}
                  <Typography variant="caption" sx={{ mt: 0.5, color: 'text.secondary', display: 'inline' }}>
                    {dayInfo.data.clockin}
                  </Typography>

                  {/* Separator and Clock Out or Online */}
                  {dayInfo.data.clockout && dayInfo.data.clockout !== "--" ? (
                    <Typography variant="caption" sx={{ color: 'text.secondary', display: 'inline' }}>
                      &nbsp;- {dayInfo.data.clockout}
                    </Typography>
                  ) : (
                    <Typography variant="caption" sx={{ color: 'primary.main', display: 'inline' }}>
                      &nbsp;- Online
                    </Typography>
                  )}
                </>
              )}
            </Box>
          )}
        </Box>
      </Box>
    );
  };

  // Function to render mini progress bar for each day
  const renderMiniProgressBar = (dayData) => {
    // Parse time values to determine bar width
    const parseTime = (timeStr) => {
      if (!timeStr || timeStr === "--") { return 0; }
      const match = timeStr.match(/(?<hours>\d+)h\s*(?<minutes>\d+)m/);
      if (!match) { return 0; }
      const hours = parseInt(match.groups.hours, 10) || 0;
      const minutes = parseInt(match.groups.minutes, 10) || 0;
      return (hours * 60) + minutes; // Return total minutes
    };

    // Ensure all days show similar working details by setting minimum values for activity types
    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility

    // Parse all time values
    const atWorkMinutes = parseTime(dayData.atwork);

    // Ensure minimum values for all activity types to make them visible
    let productivityMinutes = parseTime(dayData.productivitytime);
    let idleMinutes = parseTime(dayData.idletime);
    let privateMinutes = parseTime(dayData.privatetime);

    // If we have work time but no activity breakdown, ensure minimum values
    if (atWorkMinutes > 0) {
      // Ensure at least some productivity time is shown
      if (productivityMinutes === 0) {
        productivityMinutes = MIN_ACTIVITY_MINUTES;
      }

      // Ensure at least some idle time is shown
      if (idleMinutes === 0) {
        idleMinutes = MIN_ACTIVITY_MINUTES;
      }

      // Ensure at least some break time is shown
      if (privateMinutes === 0) {
        privateMinutes = MIN_ACTIVITY_MINUTES;
      }
    }

    // Ensure minimum visibility for each activity type if it exists
    const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists

    // Calculate initial percentages
    let productivityPercent = atWorkMinutes > 0 ? (productivityMinutes / atWorkMinutes) * 100 : 0;
    let idlePercent = atWorkMinutes > 0 ? (idleMinutes / atWorkMinutes) * 100 : 0;
    let privatePercent = atWorkMinutes > 0 ? (privateMinutes / atWorkMinutes) * 100 : 0;

    // Ensure minimum visibility for activities that exist
    if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {
      productivityPercent = minVisibilityPercent;
    }

    if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {
      idlePercent = minVisibilityPercent;
    }

    if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {
      privatePercent = minVisibilityPercent;
    }

    // Normalize percentages to ensure they sum to 100%
    const newTotalPercent = productivityPercent + idlePercent + privatePercent;
    if (newTotalPercent > 0) {
      const scaleFactor = 100 / newTotalPercent;
      productivityPercent *= scaleFactor;
      idlePercent *= scaleFactor;
      privatePercent *= scaleFactor;
    }

    // Determine total bar width (max 100%)
    const barWidth = Math.min(100, (atWorkMinutes / (8 * 60)) * 100);

    // Format time for tooltips
    const formatTimeForTooltip = (minutes) => {
      if (minutes === 0) {
        return "0m";
      }
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
    };

    return (
      <Box sx={{
        height: '6px',
        width: '100%',
        backgroundColor: '#f0f0f0',
        borderRadius: '3px',
        overflow: 'hidden',
        position: 'relative'
      }}>
        {/* Container for the colored segments */}
        <Box sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          height: '100%',
          width: `${barWidth}%`,
          display: 'flex',
          borderRadius: '3px',
          overflow: 'hidden',
          border: '1px solid rgba(0,0,0,0.1)'
        }}>
          {/* Time at Work (light green background) - only show if there's work time */}
          {atWorkMinutes > 0 && (
            <Tooltip title={`Time at Work: ${dayData.atwork}`} arrow placement="top">
              <Box sx={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                backgroundColor: '#E8F5E9', // Light green background for total work time
                zIndex: 0
              }} />
            </Tooltip>
          )}

          {/* Activity segments */}
          <Box sx={{ display: 'flex', width: '100%', height: '100%', position: 'relative', zIndex: 1 }}>
            {/* Productivity time (dark green) */}
            {productivityMinutes > 0 && (
              <Tooltip title={`Productive: ${dayData.productivitytime}`} arrow placement="top">
                <Box sx={{
                  width: `${productivityPercent}%`,
                  height: '100%',
                  backgroundColor: '#2E7D32', // Dark green
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '1px',
                    height: '100%',
                    backgroundColor: 'rgba(0,0,0,0.1)'
                  }
                }} />
              </Tooltip>
            )}

            {/* Idle time (yellow) */}
            {idleMinutes > 0 && (
              <Tooltip title={`Idle: ${dayData.idletime}`} arrow placement="top">
                <Box sx={{
                  width: `${idlePercent}%`,
                  height: '100%',
                  backgroundColor: '#FFC107', // Yellow
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '1px',
                    height: '100%',
                    backgroundColor: 'rgba(0,0,0,0.1)'
                  }
                }} />
              </Tooltip>
            )}

            {/* Private/break time (red) */}
            {privateMinutes > 0 && (
              <Tooltip title={`Break: ${dayData.privatetime}`} arrow placement="top">
                <Box sx={{
                  width: `${privatePercent}%`,
                  height: '100%',
                  backgroundColor: '#F44336', // Red
                  position: 'relative'
                }} />
              </Tooltip>
            )}
          </Box>
        </Box>
      </Box>
    );
  };

  // Calculate monthly totals
  const calculateMonthlyTotals = () => {
    if (!data || data.length === 0) { return { workDays: 0, totalHours: '--' }; }

    let totalMinutes = 0;
    let workDays = 0;

    data.forEach(day => {
      if (day.atwork !== "--") {
        workDays++;

        // Parse time values (format: "Xh Ym")
        const match = day.atwork.match(/(?<hours>\d+)h\s*(?<minutes>\d+)m/);
        if (match) {
          const hours = parseInt(match.groups.hours, 10) || 0;
          const minutes = parseInt(match.groups.minutes, 10) || 0;
          totalMinutes += (hours * 60) + minutes;
        }
      }
    });

    // Format minutes back to "Xh Ym" format
    const formatMinutes = (minutes) => {
      if (minutes === 0) { return "--"; }
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}h ${mins}m`;
    };

    return {
      workDays,
      totalHours: formatMinutes(totalMinutes)
    };
  };

  const monthlyTotals = calculateMonthlyTotals();

  return (
    <Box sx={{ width: '100%', maxWidth: '1200px', mx: 'auto' }}>
      {/* Calendar */}
      <Paper sx={{
        p: 0,
        border: '1px solid #e0e0e0',
        borderRadius: '4px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {/* Day headers */}
        <Grid container spacing={0} sx={{ borderBottom: '2px solid #e0e0e0' }}>
          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day) => (
            <Grid item xs={12/7} key={day}>
              <Box sx={{
                textAlign: 'center',
                py: 1,
                fontWeight: 'bold',
                backgroundColor: '#f5f5f5'
              }}>
                <Typography variant="body2">{day}</Typography>
              </Box>
            </Grid>
          ))}
        </Grid>

        {/* Calendar days */}
        <Grid container spacing={0}>
          {calendarDays.map((dayInfo, index) => (
            <Grid item xs={12/7} key={index}>
              {renderDayCell(dayInfo)}
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Box>
  );
};

MonthView.propTypes = {
  data: PropTypes.array,
  startDate: PropTypes.object
};

export default MonthView;
