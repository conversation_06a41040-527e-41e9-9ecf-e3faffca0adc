'use strict';

const express = require('express');
const router = express.Router();
const multer = require('multer')().none();
const sprint = require('../controllers/sprint.contoller');

module.exports = (app) => {
    // Sprint CRUD routes
    router.post("/create", multer, sprint.createSprint);
    router.get("/", sprint.getAllSprints);
    router.get("/project/:projectId", sprint.getSprintsByProject);
    router.get("/user/:userId", sprint.getSprintsByUser);
    router.get("/:sprintId/details", sprint.getSprintDetails);
    router.patch("/update/:sprintId", multer, sprint.updateSprint);
    router.delete("/delete/:sprintId", sprint.deleteSprint);
    
    // Task management in sprints
    router.post("/add-task", multer, sprint.addTaskToSprint);
    router.delete("/:sprintId/task/:taskId", sprint.removeTaskFromSprint);
    
    // Project management in sprints
    router.post("/add-project", multer, sprint.addProjectToSprint);
    
    // Sprint status management
    router.post("/start/:sprintId", sprint.startSprint);
    router.post("/complete/:sprintId", sprint.completeSprint);
    
    app.use("/api/sprint", router);
};