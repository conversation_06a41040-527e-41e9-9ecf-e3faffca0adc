'use strict';

const express = require('express');
const router = express.Router();
const multer = require('multer')().none();
const permission = require('../controllers/permissions.controller');
const checkAdmin = require('../middlewares/checkAdmin'); // ✅ Import middleware

module.exports = (app) => {
  // ✅ Protect all permission routes with admin-only access
  router.use(checkAdmin);

  router.get("/:userId", permission.getUserPermissions);
  router.put("/:userId", multer, permission.updateUserPermissions);

  app.use("/api/permissions", router);
};
