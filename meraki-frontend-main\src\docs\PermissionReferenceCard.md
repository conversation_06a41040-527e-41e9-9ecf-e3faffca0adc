# Meraki HR Permission System - Quick Reference Card

## Permission Structure

```javascript
{
  feat: "FeatureName",  // Feature to access
  acts: ["action1", "action2"]  // Allowed actions
}
```

## Available Actions

| Action | Description | Use Case |
|--------|-------------|----------|
| `read` | Basic read access | View basic information |
| `read_all` | Full read access to all items | Admin views, reports |
| `read_some` | Read access to a subset of items | Department-specific views |
| `read_self` | Read access to own data only | Personal information |
| `create` | Create new items | Add new records |
| `update` | Update existing items | Edit records |
| `delete` | Delete items | Remove records |

## Core Features

| Feature | Description | Admin Permission | User Permission |
|---------|-------------|------------------|----------------|
| `Dashboard` | System dashboard | `read_all` | `read` |
| `User` | Employee management | `read`, `create`, `update`, `delete` | None |
| `Department` | Department management | `read`, `create`, `update`, `delete` | None |
| `Designation` | Job designation management | `read`, `create`, `update`, `delete` | None |
| `Attendance` | Attendance tracking | `read_all` | `read`, `read_self` |
| `Expense` | Expense management | `read_all` | `read_self`, `create` |
| `Leave` | Leave management | `read_all` | `read_self`, `create` |
| `Projects` | Project management | `read_all` | `read_self` |
| `Tasks` | Task management | `read_all` | `read_self` |
| `Timeline` | Timeline management | `read_all` | `read_self` |
| `Report` | Reporting | `read` | None |
| `Setting` | System settings | `read`, `update` | None |

## Leave Management Features

| Feature | Description | Admin Permission | User Permission |
|---------|-------------|------------------|----------------|
| `Leave Report` | Leave reports | `read_all` | None |
| `Approve` | Leave approval | `read_all`, `update` | None |
| `Calendar` | Leave calendar | `read_all` | None |
| `Configuration` | Leave configuration | `read_all`, `update` | None |

## Project Features

| Feature | Description | Admin Permission | User Permission |
|---------|-------------|------------------|----------------|
| `Project List` | List of projects | `read_all` | None |
| `Project Overview` | Project overview | `read_all` | `read` |
| `Project Timesheet` | Project timesheet | `read_all` | `read` |
| `Client` | Client management | `read_all`, `create`, `update` | None |

## Timeline Features

| Feature | Description | Admin Permission | User Permission |
|---------|-------------|------------------|----------------|
| `Overview` | Timeline overview | `read_all` | None |
| `Time Request` | Time request management | `read_all`, `update` | None |
| `Task Request` | Task request management | `read_all`, `update` | None |
| `Work Schedule` | Work schedule management | `read_all`, `update` | None |

## Task Features

| Feature | Description | Admin Permission | User Permission |
|---------|-------------|------------------|----------------|
| `Tasks` | Task management | `read_all` | `read`, `update` |
| `Task Note` | Task notes | `read_all` | `read`, `create` |

## Common Permission Combinations

### Admin User

```javascript
[
  { feat: "Dashboard", acts: ["read_all"] },
  { feat: "User", acts: ["read", "create", "update", "delete"] },
  { feat: "Department", acts: ["read", "create", "update", "delete"] },
  { feat: "Designation", acts: ["read", "create", "update", "delete"] },
  { feat: "Attendance", acts: ["read_all"] },
  { feat: "Expense", acts: ["read_all"] },
  { feat: "Leave", acts: ["read_all"] },
  { feat: "Projects", acts: ["read_all"] },
  { feat: "Tasks", acts: ["read_all"] }
]
```

### Department Manager

```javascript
[
  { feat: "Dashboard", acts: ["read"] },
  { feat: "User", acts: ["read_some"] },
  { feat: "Attendance", acts: ["read_some"] },
  { feat: "Leave", acts: ["read_some", "update"] },
  { feat: "Projects", acts: ["read_some"] },
  { feat: "Tasks", acts: ["read_some"] }
]
```

### Regular Employee

```javascript
[
  { feat: "Dashboard", acts: ["read"] },
  { feat: "Attendance", acts: ["read_self"] },
  { feat: "Expense", acts: ["read_self", "create"] },
  { feat: "Leave", acts: ["read_self", "create"] },
  { feat: "Projects", acts: ["read_self"] },
  { feat: "Tasks", acts: ["read_self", "update"] }
]
```

## Permission Checking

```javascript
// Check if user can read the User feature
const canReadUser = Can('read', 'User');

// Check if user can create a new Department
const canCreateDepartment = Can('create', 'Department');

// Check if user can view all attendance records
const canViewAllAttendance = Can('read_all', 'Attendance');
```

## Troubleshooting

1. Check browser console for permission logs
2. Verify feature name matches exactly (case-sensitive)
3. Ensure user has the correct action permission
4. Check if user role is set correctly
5. Verify permission is correctly formatted in the database
