{"ast": null, "code": "!function (e, i) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = i() : \"function\" == typeof define && define.amd ? define(i) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isSameOrBefore = i();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, i) {\n    i.prototype.isSameOrBefore = function (e, i) {\n      return this.isSame(e, i) || this.isBefore(e, i);\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "i", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_isSameOrBefore", "prototype", "isSameOrBefore", "isSame", "isBefore"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/node_modules/dayjs/plugin/isSameOrBefore.js"], "sourcesContent": ["!function(e,i){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=i():\"function\"==typeof define&&define.amd?define(i):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isSameOrBefore=i()}(this,(function(){\"use strict\";return function(e,i){i.prototype.isSameOrBefore=function(e,i){return this.isSame(e,i)||this.isBefore(e,i)}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,2BAA2B,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAAC;IAACA,CAAC,CAACQ,SAAS,CAACC,cAAc,GAAC,UAASV,CAAC,EAACC,CAAC,EAAC;MAAC,OAAO,IAAI,CAACU,MAAM,CAACX,CAAC,EAACC,CAAC,CAAC,IAAE,IAAI,CAACW,QAAQ,CAACZ,CAAC,EAACC,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}