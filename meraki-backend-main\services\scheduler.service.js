/**
 * Scheduler Service
 *
 * This service handles scheduled tasks for the work hours tracking system:
 * - Auto-checkout: Automatically checks out users who forgot to clock out
 * - Absent marking: Marks users as absent who didn't clock in
 */

const cron = require('node-cron');
const { db } = require("../models");
const Activity = db.activity;
const Attendance = db.attendance;
const User = db.user;

/**
 * Auto-checkout users who forgot to clock out
 *
 * This function finds all users who checked in yesterday but didn't check out,
 * and automatically checks them out at the end of the workday (6:00 PM).
 * It also calculates work hours, overtime, and half-day status.
 *
 * @returns {Object} Result with success status and count of auto-checked out users
 */
const autoCheckoutUsers = async () => {
  try {
    // Set date range for yesterday
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Find activities without checkout time
    const activities = await Activity.find({
      checkInTime: { $gte: yesterday, $lt: today },
      checkOutTime: { $exists: false }
    });

    // Process each activity that needs auto-checkout
    for (const activity of activities) {
      // Set checkout time to end of workday (6:00 PM)
      const checkoutTime = new Date(activity.checkInTime);
      checkoutTime.setHours(18, 0, 0, 0);

      // If check-in was after 6 PM, set checkout to check-in + 1 minute
      if (activity.checkInTime > checkoutTime) {
        checkoutTime.setTime(activity.checkInTime.getTime() + 60000);
      }

      // Update activity with checkout time and auto-checkout flag
      activity.checkOutTime = checkoutTime;
      activity.autoCheckout = true;

      // Calculate work hours
      const totalMinutes = Math.floor((checkoutTime - activity.checkInTime) / 60000);
      activity.totalWorkingTime = totalMinutes;

      // Get user's assigned work hours
      const user = await User.findById(activity.user);
      const assignedWorkHours = user?.workHours || 8.5;

      // Calculate actual hours and overtime
      const actualHoursWorked = Math.floor(totalMinutes / 60);
      const overtimeHours = actualHoursWorked > assignedWorkHours ?
        (actualHoursWorked - assignedWorkHours) : 0;

      // Set work hours status flags
      activity.earlyCheckOutStatus = actualHoursWorked < assignedWorkHours;
      activity.halfDayStatus = actualHoursWorked < (assignedWorkHours / 2);
      activity.overtimeStatus = actualHoursWorked > assignedWorkHours;
      activity.actualHours = actualHoursWorked;
      activity.assignedHours = assignedWorkHours;
      activity.overtimeHours = overtimeHours;

      // Save the updated activity
      await activity.save();

      // Update the corresponding attendance record
      const attendance = await Attendance.findOne({
        user: activity.user,
        checkIn: { $gte: yesterday, $lt: today },
        checkOut: { $exists: false }
      });

      if (attendance) {
        attendance.checkOut = checkoutTime;
        await attendance.save();
      }
    }

    return { success: true, count: activities.length };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Mark users as absent who didn't clock in
 *
 * This function finds all active users who didn't create an activity record yesterday,
 * and creates an activity record with absentStatus = true for them.
 *
 * @returns {Object} Result with success status and count of users marked as absent
 */
const markAbsentUsers = async () => {
  try {
    // Set date range for yesterday
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get all active users
    const users = await User.find({ status: 1 });
    let absentCount = 0;

    // Check each user for activity yesterday
    for (const user of users) {
      const activity = await Activity.findOne({
        user: user._id,
        checkInTime: { $gte: yesterday, $lt: today }
      });

      // If no activity record exists, create one with absentStatus = true
      if (!activity) {
        await Activity.create({
          user: user._id,
          absentStatus: true,
          assignedHours: user.workHours || 8.5,
          checkInTime: yesterday,
          checkOutTime: yesterday,
          createdAt: yesterday
        });
        absentCount++;
      }
    }

    return { success: true, count: absentCount };
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Initialize the work hours tracking scheduler
 *
 * This function is called when the server starts to initialize the scheduler.
 * It sets up cron jobs to run the auto-checkout and absent marking processes
 * automatically at specific times.
 */
const initScheduler = () => {
  // Schedule auto-checkout job to run at 12:01 AM every day
  // This will check for users who forgot to check out the previous day
  cron.schedule('1 0 * * *', async () => {
    console.log('🕒 Running auto-checkout for users who forgot to check out...');
    const result = await autoCheckoutUsers();
    console.log(`✅ Auto-checkout completed: ${result.count} users processed`);
  });

  // Schedule absent marking job to run at 12:15 AM every day
  cron.schedule('15 0 * * *', async () => {
    console.log('🕒 Marking absent users for the previous day...');
    const result = await markAbsentUsers();
    console.log(`✅ Absent marking completed: ${result.count} users marked absent`);
  });

  // Also run auto-checkout for same day at 11:59 PM
  // This will check for users who forgot to check out on the current day
  cron.schedule('59 23 * * *', async () => {
    console.log('🕒 Running same-day auto-checkout for users who forgot to check out...');
    
    try {
      // Set date range for today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      // Find activities without checkout time for today
      const activities = await Activity.find({
        checkInTime: { $gte: today, $lt: tomorrow },
        checkOutTime: { $exists: false }
      });
      
      // Process each activity that needs auto-checkout
      for (const activity of activities) {
        // Set checkout time to end of workday (6:00 PM)
        const checkoutTime = new Date();
        checkoutTime.setHours(23, 59, 0, 0);
        
        // Update activity with checkout time and auto-checkout flag
        activity.checkOutTime = checkoutTime;
        activity.autoCheckout = true;
        
        // Calculate work hours
        const totalMinutes = Math.floor((checkoutTime - activity.checkInTime) / 60000);
        activity.totalWorkingTime = totalMinutes;
        
        // Get user's assigned work hours
        const user = await User.findById(activity.user);
        const assignedWorkHours = user?.workHours || 8.5;
        
        // Calculate actual hours and overtime
        const actualHoursWorked = Math.floor(totalMinutes / 60);
        const overtimeHours = actualHoursWorked > assignedWorkHours ?
          (actualHoursWorked - assignedWorkHours) : 0;
        
        // Set work hours status flags
        activity.earlyCheckOutStatus = actualHoursWorked < assignedWorkHours;
        activity.halfDayStatus = actualHoursWorked < (assignedWorkHours / 2);
        activity.overtimeStatus = actualHoursWorked > assignedWorkHours;
        activity.actualHours = actualHoursWorked;
        activity.assignedHours = assignedWorkHours;
        activity.overtimeHours = overtimeHours;
        
        // Save the updated activity
        await activity.save();
        
        // Update the corresponding attendance record
        const attendance = await Attendance.findOne({
          user: activity.user,
          checkIn: { $gte: today, $lt: tomorrow },
          checkOut: { $exists: false }
        });
        
        if (attendance) {
          attendance.checkOut = checkoutTime;
          await attendance.save();
        }
      }
      
      console.log(`✅ Same-day auto-checkout completed: ${activities.length} users processed`);
    } catch (error) {
      console.error('❌ Error in same-day auto-checkout:', error.message);
    }
  });

  console.log('✅ Scheduler initialized - Work hours tracking system ready');
};

module.exports = {
  initScheduler,
  autoCheckoutUsers,
  markAbsentUsers
};
