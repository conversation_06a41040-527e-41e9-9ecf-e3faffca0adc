import React, { useEffect, useState, useRef } from "react";
import {
  Box, Card, IconButton, Pagination, Table, TableBody, TableCell,
  TableHead, TableRow, Typography, Button 
} from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { ProductSelector, UserSelector } from "selectors";
import { ProductActions } from "slices/actions";
import { PlayCircle, StopCircle, CheckCircle, PauseCircle } from "@mui/icons-material";
import styled from "@emotion/styled";
import TaskHeader from "./components/TaskHeader";
import TaskFilterUser from "./components/TaskFilterUser";
import "../../CommonStyle/ButtonStyle.css";

const FilterBox = styled(Box)(() => ({
  width: "100%",
  marginTop: 30,
  marginBottom: 20,
  display: "flex",
  justifyContent: "space-between",
}));

// Store task timer state in sessionStorage to persist between page navigations
const getStoredTaskState = () => {
  try {
    return JSON.parse(sessionStorage.getItem("taskTimerState")) || {
      elapsedTime: 0,
      runningTask: null
    };
  } catch (e) {
    console.error("Error parsing stored task state:", e);
    return { elapsedTime: 0, runningTask: null };
  }
};

const storeTaskState = (state) => {
  try {
    sessionStorage.setItem("taskTimerState", JSON.stringify(state));
  } catch (e) {
    console.error("Error storing task state:", e);
  }
};

function Tasklist() {
  const dispatch = useDispatch();

  // Get products and profile from Redux
  const products = useSelector(ProductSelector.getProducts()) || [];
  const profile = useSelector(UserSelector.profile()) || {};

  // Get stored state from sessionStorage
  const storedState = getStoredTaskState();

  // Local state for filtered tasks, pagination, timer, running task, and active tab
  const [filteredData, setFilteredData] = useState([]);
  const [filter, setFilter] = useState({ page: 1 });
  const [elapsedTime, setElapsedTime] = useState(storedState.elapsedTime || 0);
  const [runningTask, setRunningTask] = useState(storedState.runningTask);
  const [activeTab, setActiveTab] = useState("Today");

  // Inside component:
const timerRef = useRef(null);

useEffect(() => {
  if (runningTask && !runningTask.isPaused) {
    // Always start the timer from 0 when a task is started or resumed
    console.log("Starting timer from 0 seconds");
    
    timerRef.current = setInterval(() => {
      setElapsedTime((prev) => {
        const newTime = prev + 1;
        storeTaskState({ elapsedTime: newTime, runningTask });
        return newTime;
      });
    }, 1000);

    return () => clearInterval(timerRef.current);
  }
}, [runningTask]);


  // Store task state when it changes
  useEffect(() => {
    storeTaskState({ elapsedTime, runningTask });
  }, [runningTask, elapsedTime]);

  // Fetch products when profile becomes available
  useEffect(() => {
    if (profile && profile._id) {
      console.log("Fetching products for user:", profile._id);
      dispatch(ProductActions.getProductsByUser({ id: profile._id }));
    }
  }, [profile, dispatch]);

  // Filter tasks based on active tab and filter conditions
  const filterTasks = (tab, filterConditions) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const filtered = products.map((product) => ({
      ...product,
      taskArr: product.taskArr.filter((task) => {
        const taskCreateDate = new Date(task.createdAt).setHours(0, 0, 0, 0);
        const taskEndDate = task.endDate ? new Date(task.endDate).setHours(0, 0, 0, 0) : null;
        const isCompleted = task.taskStatus === "Completed";
        let tabFilter = true;
        if (tab === "Today") {
          tabFilter = taskCreateDate === today.getTime() && !isCompleted;
        } else if (tab === "Overdue") {
          tabFilter = taskCreateDate < today.getTime() && !isCompleted;
        } else if (tab === "Upcoming") {
          tabFilter = taskEndDate && taskEndDate > today.getTime();
        } else if (tab === "Completed") {
          tabFilter = isCompleted;
        }
        const userFilter = filterConditions.user ? task.assignee.includes(filterConditions.user) : true;
        const statusFilter = filterConditions.status ? task.taskStatus === filterConditions.status : true;
        const projectFilter = filterConditions.project ? product._id === filterConditions.project : true;
        return tabFilter && userFilter && statusFilter && projectFilter;
      }),
    }));
    console.log("Filtered products with tasks:", filtered);
    setFilteredData(filtered);
  };

  // Re-filter tasks when products, active tab, or filter conditions change
  useEffect(() => {
    if (products.length > 0) {
      console.log("Active Tab:", activeTab, "Filter Conditions:", filter);
      filterTasks(activeTab, filter);
    }
  }, [products, activeTab, filter]);

  const handleStartTask = (taskId, projectId) => {
    // If there's already a running task that's not paused
    if (runningTask && !runningTask.isPaused && runningTask.taskId !== taskId) {
      alert("You can only run one task at a time!");
      return;
    }
    
    // Get current date in YYYY-MM-DD format
    const today = new Date().toISOString().split("T")[0];
    
    // If we're resuming a paused task
    if (runningTask && runningTask.isPaused && runningTask.taskId === taskId) {
      const resumeTime = Date.now();
      const updatedTask = { 
        ...runningTask, 
        isPaused: false,
        resumeTime: resumeTime,
        // Set the start time to the current time for this session
        startTime: resumeTime,
        currentDate: today,
        // If this is the first start, record it
        firstStartTime: runningTask.firstStartTime || resumeTime,
        pauseHistory: [...(runningTask.pauseHistory || []), {
          pausedAt: runningTask.pausedAt,
          resumedAt: resumeTime,
          elapsedAtPause: runningTask.pausedElapsedTime,
          date: today
        }]
      };
      
      console.log(`Resuming task with elapsed time: 0 seconds (reset after pause)`);
      
      setRunningTask(updatedTask);
      // Always start from 0 when resuming
      setElapsedTime(0);
      storeTaskState({ elapsedTime: 0, runningTask: updatedTask });
      return;
    }
    
    // Starting a new task
    const startTime = Date.now();
    
    dispatch(ProductActions.startTask({ 
      taskId, 
      projectId,
      date: today
    }));
    
    const newTask = { 
      taskId, 
      projectId, 
      startTime: startTime,
      firstStartTime: startTime, // Record the first start time
      isRunning: true, 
      isPaused: false,
      currentDate: today,
      pauseHistory: [] 
    };
    
    console.log(`Starting new task at: ${new Date(startTime).toISOString()}`);
    
    setRunningTask(newTask);
    setElapsedTime(0);
    storeTaskState({ elapsedTime: 0, runningTask: newTask });
  };

const handleStopTask = (taskId, projectId) => {
  const today = new Date().toISOString().split("T")[0];

  dispatch(ProductActions.stopTask({ 
    taskId, 
    projectId, 
    elapsedTime,
    date: today,
    pauseHistory: runningTask?.pauseHistory || []
  }));

  // Clear the interval manually
  if (timerRef.current) {
    clearInterval(timerRef.current);
    timerRef.current = null;
  }

  setRunningTask(null);
  setElapsedTime(0);
  storeTaskState({ elapsedTime: 0, runningTask: null });
};

  
  const handlePauseTask = (taskId, projectId) => {
    if (!runningTask || runningTask.taskId !== taskId) { return }
    
    // Calculate time spent so far
    const currentElapsedTime = elapsedTime;
    
    // Get current time for accurate timestamp
    const pauseTime = new Date().toISOString();
    const pauseTimeMs = Date.now();
    
    // Get current date in YYYY-MM-DD format for date-wise tracking
    const today = new Date().toISOString().split("T")[0];
    
    // Send elapsed time and timestamps to backend
    dispatch(ProductActions.pauseTask({ 
      taskId, 
      projectId, 
      elapsedTime: currentElapsedTime,
      pauseTime: pauseTime,
      date: today,
      startTime: new Date(runningTask.startTime).toISOString()
    }));
    
    // Store the current elapsed time when paused
    const updatedTask = { 
      ...runningTask, 
      isPaused: true,
      pausedAt: pauseTimeMs,
      pausedElapsedTime: currentElapsedTime,
      pauseTimeIso: pauseTime,
      // Add to pause history if it exists, otherwise create new array
      pauseHistory: [...(runningTask.pauseHistory || []), {
        pausedAt: pauseTimeMs,
        elapsedAtPause: currentElapsedTime,
        date: today
      }]
    };
    
    // Clear the interval to stop the timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    
    console.log(`Task paused after ${currentElapsedTime} seconds`);
    
    // Reset elapsed time to 0 after pausing
    setElapsedTime(0);
    setRunningTask(updatedTask);
    storeTaskState({ elapsedTime: 0, runningTask: updatedTask });
  };

  // Format time from seconds to hh:mm:ss
  const formatTime = (seconds) => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs}h ${mins}m ${secs}s`;
  };

  // Convert decimal hours to hh:mm:ss
  const formatDecimalToTime = (decimalHours) => {
    if (!decimalHours) { return "0h 0m 0s"; }
    return formatTime(Math.floor(decimalHours * 3600));
  };

  return (
    <div style={{ display: "flex", justifyContent: "center", flexDirection: "column", gap: "5px" }}>
      <Typography variant="h5" sx={{ fontWeight: 600 }}>My Tasks</Typography>

      <Card style={{ padding: "10px" }}>
        <TaskHeader />
        {profile?.role && !profile.role.includes("admin") && (
          <TaskFilterUser projects={products} onFilter={setFilter} />
        )}
      </Card>

      <div style={{ display: "flex", gap: "4px" }}>
        {["Today", "Overdue", "Upcoming", "Completed"].map((tab) => (
          <Button
            key={tab}
            onClick={() => {
              setActiveTab(tab);
              filterTasks(tab, filter);
            }}
            sx={{
              borderRadius: 0, // Remove rounded corners
              boxShadow: "none", // Remove default shadow
              textTransform: "none", // Use normal text case
              fontSize:"16px",
              fontWeight:"Bold",
              // Set fixed borderBottom for active tab, otherwise transparent
              borderBottom: activeTab === tab ? "3px solid rgb(111, 0, 255)" : "3px solid transparent",
              color: activeTab === tab ? "#000" : "#757575",
            }}
          >
            {tab}
          </Button>
        ))}
      </div>

      <Card style={{ padding: "10px" }}>
        <Box>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell align="center">Task Name</TableCell>
                <TableCell align="center">Project Name</TableCell>
                <TableCell align="center">Actions</TableCell>
                <TableCell align="center">Timer</TableCell>
                <TableCell align="center">Total Spent</TableCell>
                <TableCell align="center">Total Hours</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((data) =>
                  data.taskArr.map((task) => {
                    if (task.assignee.includes(profile._id) || data.visibility || task.reporter === profile._id) {
                      return (
                        <TableRow key={task._id + task.taskStatus}>
                          <TableCell align="center">{task.taskTitle}</TableCell>
                          <TableCell align="center">{data.productName}</TableCell>
                          <TableCell align="center">
                            {task.taskStatus === "Completed" ? (
                              <IconButton color="success">
                                <CheckCircle />
                              </IconButton>
                            ) : (
                              <>
                                {(!runningTask || runningTask.taskId !== task._id || runningTask.isPaused) && (
                                  <IconButton
                                    onClick={() => handleStartTask(task._id, data._id)}
                                    color="primary"
                                    disabled={runningTask && runningTask.taskId !== task._id && !runningTask.isPaused}
                                  >
                                    <PlayCircle />
                                  </IconButton>
                                )}
                                
                                {runningTask && runningTask.taskId === task._id && !runningTask.isPaused && (
                                  <IconButton
                                    onClick={() => handlePauseTask(task._id, data._id)}
                                    color="warning"
                                  >
                                    <PauseCircle />
                                  </IconButton>
                                )}
                                
                                <IconButton
                                  onClick={() => handleStopTask(task._id, data._id)}
                                  color="secondary"
                                  disabled={!runningTask || runningTask.taskId !== task._id}
                                >
                                  <StopCircle />
                                </IconButton>
                              </>
                            )}
                          </TableCell>
                          <TableCell align="center">
                            {runningTask?.taskId === task._id ? formatTime(elapsedTime) : formatDecimalToTime(task.totalSpent)}
                          </TableCell>
                          <TableCell align="center">{formatDecimalToTime(task.totalSpent)}</TableCell>
                          <TableCell align="center">{formatDecimalToTime(task.totalHours)}</TableCell>
                        </TableRow>
                      );
                    }
                    return null;
                  })
                )
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center">No Data Found</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
          <Pagination sx={{ mt: 1 }} page={filter.page} onChange={(e, val) => setFilter({ ...filter, page: val })} />
        </Box>
      </Card>
    </div>
  );
}

export default Tasklist;