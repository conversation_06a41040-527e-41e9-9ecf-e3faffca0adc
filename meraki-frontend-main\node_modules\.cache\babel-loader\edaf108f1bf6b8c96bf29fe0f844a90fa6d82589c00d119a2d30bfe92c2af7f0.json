{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\AdvancedWorkScheduleForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Typography, MenuItem, Box, Chip, FormControlLabel, Checkbox, Alert } from '@mui/material';\nimport { useFormik } from 'formik';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport Input from 'components/Input';\nimport SelectField from 'components/SelectField';\nimport { UserActions, GeneralActions } from 'slices/actions';\nimport { GeneralSelector, UserSelector } from 'selectors';\nimport { SCHEDULE_TEMPLATES, SCHEDULE_TYPES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\nimport WorkScheduleUtils from 'utils/workScheduleUtils';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdvancedWorkScheduleForm = ({\n  open,\n  onClose,\n  selectedUser,\n  selectedDate,\n  scheduleType = 'daily',\n  editingSchedule = null\n}) => {\n  _s();\n  var _currentSelectedUser$, _currentSelectedUser$2, _currentSelectedUser$3, _currentSelectedUser$4;\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [currentSelectedUser, setCurrentSelectedUser] = useState(selectedUser);\n  const [previewSchedule, setPreviewSchedule] = useState(null);\n  useEffect(() => {\n    if (success) {\n      toast.success(`Work schedule ${editingSchedule ? 'updated' : 'created'} successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true\n      });\n      onClose();\n      // Reset success state to prevent multiple alerts\n      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));\n    }\n  }, [success, onClose, dispatch, editingSchedule]);\n  useEffect(() => {\n    setCurrentSelectedUser(selectedUser);\n  }, [selectedUser]);\n\n  // Helper function to format date for input field\n  const formatDateForInput = dateValue => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0];\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n\n  // Helper to get proper date range based on schedule type\n  const getDateRange = (type, specificDate) => {\n    const date = dayjs(specificDate);\n    switch (type) {\n      case 'time_specific':\n      case 'daily':\n        // For specific day/time, use only that date\n        return {\n          effectiveFrom: date.format('YYYY-MM-DD'),\n          effectiveTo: date.format('YYYY-MM-DD')\n        };\n      case 'weekly':\n        // For weekly, use the week range\n        return {\n          effectiveFrom: date.startOf('week').format('YYYY-MM-DD'),\n          effectiveTo: date.endOf('week').format('YYYY-MM-DD')\n        };\n      default:\n        return {\n          effectiveFrom: date.format('YYYY-MM-DD'),\n          effectiveTo: date.format('YYYY-MM-DD')\n        };\n    }\n  };\n  const initialDateRange = getDateRange((editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.type) || scheduleType, selectedDate);\n  const formik = useFormik({\n    initialValues: {\n      selectedUserId: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.userId) || (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : currentSelectedUser._id) || (users.length > 0 ? users[0]._id : ''),\n      type: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.type) || scheduleType,\n      scheduleTemplate: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.scheduleTemplate) || (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$ = currentSelectedUser.workSchedule) === null || _currentSelectedUser$ === void 0 ? void 0 : _currentSelectedUser$.scheduleTemplate) || DEFAULT_WORK_SCHEDULE.scheduleTemplate,\n      startTime: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.startTime) || (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$2 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$2 === void 0 ? void 0 : _currentSelectedUser$2.startTime) || DEFAULT_WORK_SCHEDULE.startTime,\n      endTime: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.endTime) || (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$3 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$3 === void 0 ? void 0 : _currentSelectedUser$3.endTime) || DEFAULT_WORK_SCHEDULE.endTime,\n      minimumHours: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.minimumHours) || (currentSelectedUser === null || currentSelectedUser === void 0 ? void 0 : (_currentSelectedUser$4 = currentSelectedUser.workSchedule) === null || _currentSelectedUser$4 === void 0 ? void 0 : _currentSelectedUser$4.minimumHours) || DEFAULT_WORK_SCHEDULE.minimumHours,\n      effectiveFrom: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.effectiveFrom) || initialDateRange.effectiveFrom,\n      effectiveTo: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.effectiveTo) || initialDateRange.effectiveTo,\n      specificDate: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.specificDate) || formatDateForInput(selectedDate),\n      daysOfWeek: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.daysOfWeek) || [1, 2, 3, 4, 5],\n      // Monday to Friday\n      description: (editingSchedule === null || editingSchedule === void 0 ? void 0 : editingSchedule.description) || ''\n    },\n    enableReinitialize: true,\n    onSubmit: values => {\n      handleSubmit(values);\n    }\n  });\n  const handleSubmit = values => {\n    const targetUser = users.find(u => u._id === values.selectedUserId);\n    if (!targetUser) {\n      toast.error('Please select a valid user');\n      return;\n    }\n    let updatedWorkSchedules = [];\n    if (editingSchedule) {\n      // Update existing schedule\n      updatedWorkSchedules = (targetUser.workSchedules || []).map(schedule => {\n        if (schedule.id === editingSchedule.id) {\n          return {\n            ...schedule,\n            type: values.type,\n            scheduleTemplate: values.scheduleTemplate,\n            startTime: values.startTime,\n            endTime: values.endTime,\n            minimumHours: parseFloat(values.minimumHours),\n            effectiveFrom: values.effectiveFrom,\n            effectiveTo: values.effectiveTo,\n            specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,\n            daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null,\n            description: values.description,\n            priority: WorkScheduleUtils.getSchedulePriority(values.type)\n          };\n        }\n        return schedule;\n      });\n    } else {\n      // Create new schedule entry\n      const scheduleEntry = WorkScheduleUtils.createScheduleEntry(targetUser._id, {\n        type: values.type,\n        scheduleTemplate: values.scheduleTemplate,\n        startTime: values.startTime,\n        endTime: values.endTime,\n        minimumHours: parseFloat(values.minimumHours),\n        effectiveFrom: values.effectiveFrom,\n        effectiveTo: values.effectiveTo,\n        specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,\n        daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null,\n        description: values.description\n      });\n      updatedWorkSchedules = [...(targetUser.workSchedules || []), scheduleEntry];\n    }\n    const params = {\n      id: targetUser._id,\n      workSchedules: updatedWorkSchedules\n    };\n    dispatch(UserActions.updateUser(params));\n  };\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(field, value);\n\n    // If user selection changes, update the form with that user's current schedule\n    if (field === 'selectedUserId') {\n      const selectedUser = users.find(u => u._id === value);\n      if (selectedUser) {\n        var _selectedUser$workSch, _selectedUser$workSch2, _selectedUser$workSch3, _selectedUser$workSch4;\n        setCurrentSelectedUser(selectedUser);\n        formik.setFieldValue('scheduleTemplate', ((_selectedUser$workSch = selectedUser.workSchedule) === null || _selectedUser$workSch === void 0 ? void 0 : _selectedUser$workSch.scheduleTemplate) || DEFAULT_WORK_SCHEDULE.scheduleTemplate);\n        formik.setFieldValue('startTime', ((_selectedUser$workSch2 = selectedUser.workSchedule) === null || _selectedUser$workSch2 === void 0 ? void 0 : _selectedUser$workSch2.startTime) || DEFAULT_WORK_SCHEDULE.startTime);\n        formik.setFieldValue('endTime', ((_selectedUser$workSch3 = selectedUser.workSchedule) === null || _selectedUser$workSch3 === void 0 ? void 0 : _selectedUser$workSch3.endTime) || DEFAULT_WORK_SCHEDULE.endTime);\n        formik.setFieldValue('minimumHours', ((_selectedUser$workSch4 = selectedUser.workSchedule) === null || _selectedUser$workSch4 === void 0 ? void 0 : _selectedUser$workSch4.minimumHours) || DEFAULT_WORK_SCHEDULE.minimumHours);\n      }\n    }\n\n    // Update date ranges when schedule type changes\n    if (field === 'type') {\n      const newDateRange = getDateRange(value, formik.values.specificDate);\n      formik.setFieldValue('effectiveFrom', newDateRange.effectiveFrom);\n      formik.setFieldValue('effectiveTo', newDateRange.effectiveTo);\n    }\n\n    // Update date ranges when specific date changes\n    if (field === 'specificDate') {\n      const newDateRange = getDateRange(formik.values.type, value);\n      formik.setFieldValue('effectiveFrom', newDateRange.effectiveFrom);\n      formik.setFieldValue('effectiveTo', newDateRange.effectiveTo);\n    }\n\n    // Auto-calculate hours when start or end time changes\n    if (field === 'startTime' || field === 'endTime') {\n      const startTime = field === 'startTime' ? value : formik.values.startTime;\n      const endTime = field === 'endTime' ? value : formik.values.endTime;\n      if (startTime && endTime) {\n        const calculatedHours = WorkScheduleUtils.calculateHours(startTime, endTime);\n        formik.setFieldValue('minimumHours', calculatedHours);\n      }\n    }\n\n    // Auto-suggest schedule template based on time\n    if (field === 'startTime') {\n      const hour = parseInt(value.split(':')[0], 10);\n      if (hour >= 22 || hour < 6) {\n        formik.setFieldValue('scheduleTemplate', 'night_shift');\n      } else {\n        formik.setFieldValue('scheduleTemplate', 'day_shift');\n      }\n    }\n\n    // Update preview\n    updatePreview();\n  };\n  const updatePreview = () => {\n    if (formik.values.startTime && formik.values.endTime) {\n      const mockUser = {\n        ...currentSelectedUser,\n        workSchedules: [{\n          type: formik.values.type,\n          startTime: formik.values.startTime,\n          endTime: formik.values.endTime,\n          scheduleTemplate: formik.values.scheduleTemplate,\n          effectiveFrom: formik.values.effectiveFrom,\n          effectiveTo: formik.values.effectiveTo,\n          priority: WorkScheduleUtils.getSchedulePriority(formik.values.type)\n        }]\n      };\n      const effectiveSchedule = WorkScheduleUtils.getEffectiveSchedule(mockUser, formik.values.specificDate || formik.values.effectiveFrom);\n      setPreviewSchedule(effectiveSchedule);\n    }\n  };\n  const handleDayOfWeekChange = day => {\n    const currentDays = formik.values.daysOfWeek || [];\n    const newDays = currentDays.includes(day) ? currentDays.filter(d => d !== day) : [...currentDays, day];\n    formik.setFieldValue('daysOfWeek', newDays);\n  };\n  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"lg\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: [editingSchedule ? 'Edit' : 'Create', \" Advanced Work Schedule\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        children: editingSchedule ? 'Update existing schedule' : 'Create specific schedules with priority-based resolution'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: formik.handleSubmit,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Select User\",\n                name: \"selectedUserId\",\n                value: formik.values.selectedUserId,\n                onChange: e => handleWorkScheduleChange('selectedUserId', e.target.value),\n                required: true,\n                children: users.map(user => {\n                  var _user$designation;\n                  return /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: user._id,\n                    children: [user.name, \" - \", ((_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation.name) || user.role || 'No Role']\n                  }, user._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 21\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Schedule Type\",\n                name: \"type\",\n                value: formik.values.type,\n                onChange: e => handleWorkScheduleChange('type', e.target.value),\n                required: true,\n                children: SCHEDULE_TYPES.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: type.value,\n                  children: [type.label, \" (Priority: \", type.priority, \")\"]\n                }, type.value, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Schedule Template\",\n                name: \"scheduleTemplate\",\n                value: formik.values.scheduleTemplate,\n                onChange: e => handleWorkScheduleChange('scheduleTemplate', e.target.value),\n                required: true,\n                children: SCHEDULE_TEMPLATES.map(template => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: template.value,\n                  children: template.label\n                }, template.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Minimum Hours\",\n                name: \"minimumHours\",\n                type: \"number\",\n                step: \"0.1\",\n                value: formik.values.minimumHours,\n                onChange: e => handleWorkScheduleChange('minimumHours', e.target.value),\n                required: true,\n                helperText: formik.values.startTime && formik.values.endTime ? `Calculated: ${WorkScheduleUtils.calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Enter start and end time to auto-calculate'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"Start Time\",\n                name: \"startTime\",\n                value: formik.values.startTime,\n                onChange: e => handleWorkScheduleChange('startTime', e.target.value),\n                required: true,\n                children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(SelectField, {\n                label: \"End Time\",\n                name: \"endTime\",\n                value: formik.values.endTime,\n                onChange: e => handleWorkScheduleChange('endTime', e.target.value),\n                required: true,\n                children: TIME_OPTIONS.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: option.value,\n                  children: option.label\n                }, option.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Effective From\",\n                name: \"effectiveFrom\",\n                type: \"date\",\n                value: formik.values.effectiveFrom,\n                onChange: e => handleWorkScheduleChange('effectiveFrom', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Effective To\",\n                name: \"effectiveTo\",\n                type: \"date\",\n                value: formik.values.effectiveTo,\n                onChange: e => handleWorkScheduleChange('effectiveTo', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this), (formik.values.type === 'daily' || formik.values.type === 'time_specific') && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Specific Date\",\n                name: \"specificDate\",\n                type: \"date\",\n                value: formik.values.specificDate,\n                onChange: e => handleWorkScheduleChange('specificDate', e.target.value),\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), formik.values.type === 'weekly' && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: \"Days of Week\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1,\n                  flexWrap: 'wrap'\n                },\n                children: dayNames.map((day, index) => {\n                  var _formik$values$daysOf;\n                  return /*#__PURE__*/_jsxDEV(FormControlLabel, {\n                    control: /*#__PURE__*/_jsxDEV(Checkbox, {\n                      checked: ((_formik$values$daysOf = formik.values.daysOfWeek) === null || _formik$values$daysOf === void 0 ? void 0 : _formik$values$daysOf.includes(index)) || false,\n                      onChange: () => handleDayOfWeekChange(index)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 416,\n                      columnNumber: 27\n                    }, this),\n                    label: day\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                label: \"Description\",\n                name: \"description\",\n                multiline: true,\n                rows: 3,\n                value: formik.values.description,\n                onChange: e => handleWorkScheduleChange('description', e.target.value),\n                placeholder: \"Enter description for this schedule...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this), previewSchedule && /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"info\",\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  children: \"Schedule Preview:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: [previewSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift', \": \", ' ', previewSchedule.startTime, \" - \", previewSchedule.endTime, \" (\", previewSchedule.minimumHours, \"h)\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        color: \"secondary\",\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: formik.handleSubmit,\n        variant: \"contained\",\n        color: \"primary\",\n        children: editingSchedule ? 'Update Schedule' : 'Create Schedule'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 458,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 254,\n    columnNumber: 5\n  }, this);\n};\n_s(AdvancedWorkScheduleForm, \"OfszZU2Q3WCtKNzb9o9mNEtrWyk=\", false, function () {\n  return [useDispatch, useSelector, useSelector, useFormik];\n});\n_c = AdvancedWorkScheduleForm;\nAdvancedWorkScheduleForm.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string,\n  scheduleType: PropTypes.string,\n  editingSchedule: PropTypes.object\n};\nexport default AdvancedWorkScheduleForm;\nvar _c;\n$RefreshReg$(_c, \"AdvancedWorkScheduleForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Grid", "Typography", "MenuItem", "Box", "Chip", "FormControlLabel", "Checkbox", "<PERSON><PERSON>", "useFormik", "useDispatch", "useSelector", "toast", "PropTypes", "Input", "SelectField", "UserActions", "GeneralActions", "GeneralSelector", "UserSelector", "SCHEDULE_TEMPLATES", "SCHEDULE_TYPES", "DEFAULT_WORK_SCHEDULE", "TIME_OPTIONS", "WorkScheduleUtils", "jsxDEV", "_jsxDEV", "AdvancedWorkScheduleForm", "open", "onClose", "selected<PERSON>ser", "selectedDate", "scheduleType", "editingSchedule", "_s", "_currentSelectedUser$", "_currentSelectedUser$2", "_currentSelectedUser$3", "_currentSelectedUser$4", "dispatch", "users", "getUsers", "success", "updateUser", "type", "currentSelectedUser", "setCurrentSelectedUser", "previewSchedule", "setPreviewSchedule", "position", "autoClose", "closeOnClick", "removeSuccess", "formatDateForInput", "dateValue", "Date", "toISOString", "split", "getDateRange", "specificDate", "date", "dayjs", "effectiveFrom", "format", "effectiveTo", "startOf", "endOf", "initialDateRange", "formik", "initialValues", "selectedUserId", "userId", "_id", "length", "scheduleTemplate", "workSchedule", "startTime", "endTime", "minimumHours", "daysOfWeek", "description", "enableReinitialize", "onSubmit", "values", "handleSubmit", "targetUser", "find", "u", "error", "updatedWorkSchedules", "workSchedules", "map", "schedule", "id", "parseFloat", "priority", "getSchedulePriority", "scheduleEntry", "createScheduleEntry", "params", "handleWorkScheduleChange", "field", "value", "setFieldValue", "_selectedUser$workSch", "_selectedUser$workSch2", "_selectedUser$workSch3", "_selectedUser$workSch4", "newDateRange", "calculatedHours", "calculateHours", "hour", "parseInt", "updatePreview", "mockUser", "effectiveSchedule", "getEffectiveSchedule", "handleDayOfWeekChange", "day", "currentDays", "newDays", "includes", "filter", "d", "dayNames", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "sx", "mt", "container", "spacing", "item", "xs", "md", "label", "name", "onChange", "e", "target", "required", "user", "_user$designation", "designation", "role", "template", "step", "helperText", "option", "gutterBottom", "display", "gap", "flexWrap", "index", "_formik$values$daysOf", "control", "checked", "multiline", "rows", "placeholder", "severity", "onClick", "_c", "propTypes", "bool", "isRequired", "func", "object", "string", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/AdvancedWorkScheduleForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>alog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Grid,\n  Typography,\n  MenuItem,\n  Box,\n  Chip,\n  FormControlLabel,\n  Checkbox,\n  Alert\n} from '@mui/material';\nimport { useFormik } from 'formik';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { toast } from 'react-toastify';\nimport PropTypes from 'prop-types';\nimport Input from 'components/Input';\nimport SelectField from 'components/SelectField';\nimport { UserActions, GeneralActions } from 'slices/actions';\nimport { GeneralSelector, UserSelector } from 'selectors';\nimport { SCHEDULE_TEMPLATES, SCHEDULE_TYPES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';\nimport WorkScheduleUtils from 'utils/workScheduleUtils';\n\nconst AdvancedWorkScheduleForm = ({ open, onClose, selectedUser, selectedDate, scheduleType = 'daily', editingSchedule = null }) => {\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));\n  const [currentSelectedUser, setCurrentSelectedUser] = useState(selectedUser);\n  const [previewSchedule, setPreviewSchedule] = useState(null);\n\n  useEffect(() => {\n    if (success) {\n      toast.success(`Work schedule ${editingSchedule ? 'updated' : 'created'} successfully!`, {\n        position: \"top-right\",\n        autoClose: 3000,\n        closeOnClick: true,\n      });\n      onClose();\n      // Reset success state to prevent multiple alerts\n      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));\n    }\n  }, [success, onClose, dispatch, editingSchedule]);\n\n  useEffect(() => {\n    setCurrentSelectedUser(selectedUser);\n  }, [selectedUser]);\n\n  // Helper function to format date for input field\n  const formatDateForInput = (dateValue) => {\n    if (!dateValue) {\n      return new Date().toISOString().split('T')[0];\n    }\n    if (typeof dateValue === 'string') {\n      return dateValue.split('T')[0];\n    }\n    return new Date(dateValue).toISOString().split('T')[0];\n  };\n\n  // Helper to get proper date range based on schedule type\n  const getDateRange = (type, specificDate) => {\n    const date = dayjs(specificDate);\n    switch (type) {\n      case 'time_specific':\n      case 'daily':\n        // For specific day/time, use only that date\n        return {\n          effectiveFrom: date.format('YYYY-MM-DD'),\n          effectiveTo: date.format('YYYY-MM-DD')\n        };\n      case 'weekly':\n        // For weekly, use the week range\n        return {\n          effectiveFrom: date.startOf('week').format('YYYY-MM-DD'),\n          effectiveTo: date.endOf('week').format('YYYY-MM-DD')\n        };\n      default:\n        return {\n          effectiveFrom: date.format('YYYY-MM-DD'),\n          effectiveTo: date.format('YYYY-MM-DD')\n        };\n    }\n  };\n\n  const initialDateRange = getDateRange(editingSchedule?.type || scheduleType, selectedDate);\n\n  const formik = useFormik({\n    initialValues: {\n      selectedUserId: editingSchedule?.userId || currentSelectedUser?._id || (users.length > 0 ? users[0]._id : ''),\n      type: editingSchedule?.type || scheduleType,\n      scheduleTemplate: editingSchedule?.scheduleTemplate || currentSelectedUser?.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate,\n      startTime: editingSchedule?.startTime || currentSelectedUser?.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime,\n      endTime: editingSchedule?.endTime || currentSelectedUser?.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime,\n      minimumHours: editingSchedule?.minimumHours || currentSelectedUser?.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours,\n      effectiveFrom: editingSchedule?.effectiveFrom || initialDateRange.effectiveFrom,\n      effectiveTo: editingSchedule?.effectiveTo || initialDateRange.effectiveTo,\n      specificDate: editingSchedule?.specificDate || formatDateForInput(selectedDate),\n      daysOfWeek: editingSchedule?.daysOfWeek || [1, 2, 3, 4, 5], // Monday to Friday\n      description: editingSchedule?.description || ''\n    },\n    enableReinitialize: true,\n    onSubmit: (values) => {\n      handleSubmit(values);\n    }\n  });\n\n  const handleSubmit = (values) => {\n    const targetUser = users.find(u => u._id === values.selectedUserId);\n    if (!targetUser) {\n      toast.error('Please select a valid user');\n      return;\n    }\n\n    let updatedWorkSchedules = [];\n\n    if (editingSchedule) {\n      // Update existing schedule\n      updatedWorkSchedules = (targetUser.workSchedules || []).map((schedule) => {\n        if (schedule.id === editingSchedule.id) {\n          return {\n            ...schedule,\n            type: values.type,\n            scheduleTemplate: values.scheduleTemplate,\n            startTime: values.startTime,\n            endTime: values.endTime,\n            minimumHours: parseFloat(values.minimumHours),\n            effectiveFrom: values.effectiveFrom,\n            effectiveTo: values.effectiveTo,\n            specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,\n            daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null,\n            description: values.description,\n            priority: WorkScheduleUtils.getSchedulePriority(values.type)\n          };\n        }\n        return schedule;\n      });\n    } else {\n      // Create new schedule entry\n      const scheduleEntry = WorkScheduleUtils.createScheduleEntry(targetUser._id, {\n        type: values.type,\n        scheduleTemplate: values.scheduleTemplate,\n        startTime: values.startTime,\n        endTime: values.endTime,\n        minimumHours: parseFloat(values.minimumHours),\n        effectiveFrom: values.effectiveFrom,\n        effectiveTo: values.effectiveTo,\n        specificDate: values.type === 'daily' || values.type === 'time_specific' ? values.specificDate : null,\n        daysOfWeek: values.type === 'weekly' ? values.daysOfWeek : null,\n        description: values.description\n      });\n\n      updatedWorkSchedules = [...(targetUser.workSchedules || []), scheduleEntry];\n    }\n\n    const params = {\n      id: targetUser._id,\n      workSchedules: updatedWorkSchedules\n    };\n\n    dispatch(UserActions.updateUser(params));\n  };\n\n  const handleWorkScheduleChange = (field, value) => {\n    formik.setFieldValue(field, value);\n\n    // If user selection changes, update the form with that user's current schedule\n    if (field === 'selectedUserId') {\n      const selectedUser = users.find(u => u._id === value);\n      if (selectedUser) {\n        setCurrentSelectedUser(selectedUser);\n        formik.setFieldValue('scheduleTemplate', selectedUser.workSchedule?.scheduleTemplate || DEFAULT_WORK_SCHEDULE.scheduleTemplate);\n        formik.setFieldValue('startTime', selectedUser.workSchedule?.startTime || DEFAULT_WORK_SCHEDULE.startTime);\n        formik.setFieldValue('endTime', selectedUser.workSchedule?.endTime || DEFAULT_WORK_SCHEDULE.endTime);\n        formik.setFieldValue('minimumHours', selectedUser.workSchedule?.minimumHours || DEFAULT_WORK_SCHEDULE.minimumHours);\n      }\n    }\n\n    // Update date ranges when schedule type changes\n    if (field === 'type') {\n      const newDateRange = getDateRange(value, formik.values.specificDate);\n      formik.setFieldValue('effectiveFrom', newDateRange.effectiveFrom);\n      formik.setFieldValue('effectiveTo', newDateRange.effectiveTo);\n    }\n\n    // Update date ranges when specific date changes\n    if (field === 'specificDate') {\n      const newDateRange = getDateRange(formik.values.type, value);\n      formik.setFieldValue('effectiveFrom', newDateRange.effectiveFrom);\n      formik.setFieldValue('effectiveTo', newDateRange.effectiveTo);\n    }\n\n    // Auto-calculate hours when start or end time changes\n    if (field === 'startTime' || field === 'endTime') {\n      const startTime = field === 'startTime' ? value : formik.values.startTime;\n      const endTime = field === 'endTime' ? value : formik.values.endTime;\n\n      if (startTime && endTime) {\n        const calculatedHours = WorkScheduleUtils.calculateHours(startTime, endTime);\n        formik.setFieldValue('minimumHours', calculatedHours);\n      }\n    }\n\n    // Auto-suggest schedule template based on time\n    if (field === 'startTime') {\n      const hour = parseInt(value.split(':')[0], 10);\n      if (hour >= 22 || hour < 6) {\n        formik.setFieldValue('scheduleTemplate', 'night_shift');\n      } else {\n        formik.setFieldValue('scheduleTemplate', 'day_shift');\n      }\n    }\n\n    // Update preview\n    updatePreview();\n  };\n\n  const updatePreview = () => {\n    if (formik.values.startTime && formik.values.endTime) {\n      const mockUser = {\n        ...currentSelectedUser,\n        workSchedules: [{\n          type: formik.values.type,\n          startTime: formik.values.startTime,\n          endTime: formik.values.endTime,\n          scheduleTemplate: formik.values.scheduleTemplate,\n          effectiveFrom: formik.values.effectiveFrom,\n          effectiveTo: formik.values.effectiveTo,\n          priority: WorkScheduleUtils.getSchedulePriority(formik.values.type)\n        }]\n      };\n\n      const effectiveSchedule = WorkScheduleUtils.getEffectiveSchedule(\n        mockUser, \n        formik.values.specificDate || formik.values.effectiveFrom\n      );\n\n      setPreviewSchedule(effectiveSchedule);\n    }\n  };\n\n  const handleDayOfWeekChange = (day) => {\n    const currentDays = formik.values.daysOfWeek || [];\n    const newDays = currentDays.includes(day) ? currentDays.filter(d => d !== day) : [...currentDays, day];\n    \n    formik.setFieldValue('daysOfWeek', newDays);\n  };\n\n  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"lg\" fullWidth>\n      <DialogTitle>\n        <Typography variant=\"h6\">\n          {editingSchedule ? 'Edit' : 'Create'} Advanced Work Schedule\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\">\n          {editingSchedule ? 'Update existing schedule' : 'Create specific schedules with priority-based resolution'}\n        </Typography>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ mt: 2 }}>\n          <form onSubmit={formik.handleSubmit}>\n            <Grid container spacing={2}>\n              {/* User Selection */}\n              <Grid item xs={12} md={6}>\n                <SelectField\n                  label=\"Select User\"\n                  name=\"selectedUserId\"\n                  value={formik.values.selectedUserId}\n                  onChange={(e) => handleWorkScheduleChange('selectedUserId', e.target.value)}\n                  required\n                >\n                  {users.map((user) => (\n                    <MenuItem key={user._id} value={user._id}>\n                      {user.name} - {user.designation?.name || user.role || 'No Role'}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              {/* Schedule Type */}\n              <Grid item xs={12} md={6}>\n                <SelectField\n                  label=\"Schedule Type\"\n                  name=\"type\"\n                  value={formik.values.type}\n                  onChange={(e) => handleWorkScheduleChange('type', e.target.value)}\n                  required\n                >\n                  {SCHEDULE_TYPES.map((type) => (\n                    <MenuItem key={type.value} value={type.value}>\n                      {type.label} (Priority: {type.priority})\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              {/* Schedule Template */}\n              <Grid item xs={12} md={6}>\n                <SelectField\n                  label=\"Schedule Template\"\n                  name=\"scheduleTemplate\"\n                  value={formik.values.scheduleTemplate}\n                  onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}\n                  required\n                >\n                  {SCHEDULE_TEMPLATES.map((template) => (\n                    <MenuItem key={template.value} value={template.value}>\n                      {template.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              {/* Minimum Hours */}\n              <Grid item xs={12} md={6}>\n                <Input\n                  label=\"Minimum Hours\"\n                  name=\"minimumHours\"\n                  type=\"number\"\n                  step=\"0.1\"\n                  value={formik.values.minimumHours}\n                  onChange={(e) => handleWorkScheduleChange('minimumHours', e.target.value)}\n                  required\n                  helperText={\n                    formik.values.startTime && formik.values.endTime ? `Calculated: ${WorkScheduleUtils.calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Enter start and end time to auto-calculate'\n                  }\n                />\n              </Grid>\n\n              {/* Time Range */}\n              <Grid item xs={12} md={6}>\n                <SelectField\n                  label=\"Start Time\"\n                  name=\"startTime\"\n                  value={formik.values.startTime}\n                  onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}\n                  required\n                >\n                  {TIME_OPTIONS.map((option) => (\n                    <MenuItem key={option.value} value={option.value}>\n                      {option.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <SelectField\n                  label=\"End Time\"\n                  name=\"endTime\"\n                  value={formik.values.endTime}\n                  onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}\n                  required\n                >\n                  {TIME_OPTIONS.map((option) => (\n                    <MenuItem key={option.value} value={option.value}>\n                      {option.label}\n                    </MenuItem>\n                  ))}\n                </SelectField>\n              </Grid>\n\n              {/* Date Range */}\n              <Grid item xs={12} md={6}>\n                <Input\n                  label=\"Effective From\"\n                  name=\"effectiveFrom\"\n                  type=\"date\"\n                  value={formik.values.effectiveFrom}\n                  onChange={(e) => handleWorkScheduleChange('effectiveFrom', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <Input\n                  label=\"Effective To\"\n                  name=\"effectiveTo\"\n                  type=\"date\"\n                  value={formik.values.effectiveTo}\n                  onChange={(e) => handleWorkScheduleChange('effectiveTo', e.target.value)}\n                  required\n                />\n              </Grid>\n\n              {/* Specific Date (for daily/time-specific schedules) */}\n              {(formik.values.type === 'daily' || formik.values.type === 'time_specific') && (\n                <Grid item xs={12} md={6}>\n                  <Input\n                    label=\"Specific Date\"\n                    name=\"specificDate\"\n                    type=\"date\"\n                    value={formik.values.specificDate}\n                    onChange={(e) => handleWorkScheduleChange('specificDate', e.target.value)}\n                    required\n                  />\n                </Grid>\n              )}\n\n              {/* Days of Week (for weekly schedules) */}\n              {formik.values.type === 'weekly' && (\n                <Grid item xs={12}>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Days of Week\n                  </Typography>\n                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n                    {dayNames.map((day, index) => (\n                      <FormControlLabel\n                        key={index}\n                        control={\n                          <Checkbox\n                            checked={formik.values.daysOfWeek?.includes(index) || false}\n                            onChange={() => handleDayOfWeekChange(index)}\n                          />\n                        }\n                        label={day}\n                      />\n                    ))}\n                  </Box>\n                </Grid>\n              )}\n\n              {/* Description */}\n              <Grid item xs={12}>\n                <Input\n                  label=\"Description\"\n                  name=\"description\"\n                  multiline\n                  rows={3}\n                  value={formik.values.description}\n                  onChange={(e) => handleWorkScheduleChange('description', e.target.value)}\n                  placeholder=\"Enter description for this schedule...\"\n                />\n              </Grid>\n\n              {/* Preview */}\n              {previewSchedule && (\n                <Grid item xs={12}>\n                  <Alert severity=\"info\">\n                    <Typography variant=\"subtitle2\">Schedule Preview:</Typography>\n                    <Typography variant=\"body2\">\n                      {previewSchedule.scheduleTemplate === 'day_shift' ? 'Day Shift' : 'Night Shift'}: {' '}\n                      {previewSchedule.startTime} - {previewSchedule.endTime} ({previewSchedule.minimumHours}h)\n                    </Typography>\n                  </Alert>\n                </Grid>\n              )}\n            </Grid>\n          </form>\n        </Box>\n      </DialogContent>\n\n      <DialogActions>\n        <Button onClick={onClose} color=\"secondary\">\n          Cancel\n        </Button>\n        <Button\n          onClick={formik.handleSubmit}\n          variant=\"contained\"\n          color=\"primary\"\n        >\n          {editingSchedule ? 'Update Schedule' : 'Create Schedule'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nAdvancedWorkScheduleForm.propTypes = {\n  open: PropTypes.bool.isRequired,\n  onClose: PropTypes.func.isRequired,\n  selectedUser: PropTypes.object,\n  selectedDate: PropTypes.string,\n  scheduleType: PropTypes.string,\n  editingSchedule: PropTypes.object\n};\n\nexport default AdvancedWorkScheduleForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,GAAG,EACHC,IAAI,EACJC,gBAAgB,EAChBC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SAASC,SAAS,QAAQ,QAAQ;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,WAAW,EAAEC,cAAc,QAAQ,gBAAgB;AAC5D,SAASC,eAAe,EAAEC,YAAY,QAAQ,WAAW;AACzD,SAASC,kBAAkB,EAAEC,cAAc,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,wBAAwB;AAChH,OAAOC,iBAAiB,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,wBAAwB,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,YAAY;EAAEC,YAAY;EAAEC,YAAY,GAAG,OAAO;EAAEC,eAAe,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAClI,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,KAAK,GAAG7B,WAAW,CAACQ,YAAY,CAACsB,QAAQ,CAAC,CAAC,CAAC;EAClD,MAAMC,OAAO,GAAG/B,WAAW,CAACO,eAAe,CAACwB,OAAO,CAAC1B,WAAW,CAAC2B,UAAU,CAACC,IAAI,CAAC,CAAC;EACjF,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpD,QAAQ,CAACoC,YAAY,CAAC;EAC5E,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAE5DC,SAAS,CAAC,MAAM;IACd,IAAI+C,OAAO,EAAE;MACX9B,KAAK,CAAC8B,OAAO,CAAC,iBAAiBT,eAAe,GAAG,SAAS,GAAG,SAAS,gBAAgB,EAAE;QACtFgB,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE,IAAI;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC;MACFtB,OAAO,CAAC,CAAC;MACT;MACAU,QAAQ,CAACtB,cAAc,CAACmC,aAAa,CAAC,CAACpC,WAAW,CAAC2B,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC;IACvE;EACF,CAAC,EAAE,CAACF,OAAO,EAAEb,OAAO,EAAEU,QAAQ,EAAEN,eAAe,CAAC,CAAC;EAEjDtC,SAAS,CAAC,MAAM;IACdmD,sBAAsB,CAAChB,YAAY,CAAC;EACtC,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMuB,kBAAkB,GAAIC,SAAS,IAAK;IACxC,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/C;IACA,IAAI,OAAOH,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAOA,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,OAAO,IAAIF,IAAI,CAACD,SAAS,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAACd,IAAI,EAAEe,YAAY,KAAK;IAC3C,MAAMC,IAAI,GAAGC,KAAK,CAACF,YAAY,CAAC;IAChC,QAAQf,IAAI;MACV,KAAK,eAAe;MACpB,KAAK,OAAO;QACV;QACA,OAAO;UACLkB,aAAa,EAAEF,IAAI,CAACG,MAAM,CAAC,YAAY,CAAC;UACxCC,WAAW,EAAEJ,IAAI,CAACG,MAAM,CAAC,YAAY;QACvC,CAAC;MACH,KAAK,QAAQ;QACX;QACA,OAAO;UACLD,aAAa,EAAEF,IAAI,CAACK,OAAO,CAAC,MAAM,CAAC,CAACF,MAAM,CAAC,YAAY,CAAC;UACxDC,WAAW,EAAEJ,IAAI,CAACM,KAAK,CAAC,MAAM,CAAC,CAACH,MAAM,CAAC,YAAY;QACrD,CAAC;MACH;QACE,OAAO;UACLD,aAAa,EAAEF,IAAI,CAACG,MAAM,CAAC,YAAY,CAAC;UACxCC,WAAW,EAAEJ,IAAI,CAACG,MAAM,CAAC,YAAY;QACvC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAGT,YAAY,CAAC,CAAAzB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEW,IAAI,KAAIZ,YAAY,EAAED,YAAY,CAAC;EAE1F,MAAMqC,MAAM,GAAG3D,SAAS,CAAC;IACvB4D,aAAa,EAAE;MACbC,cAAc,EAAE,CAAArC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsC,MAAM,MAAI1B,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE2B,GAAG,MAAKhC,KAAK,CAACiC,MAAM,GAAG,CAAC,GAAGjC,KAAK,CAAC,CAAC,CAAC,CAACgC,GAAG,GAAG,EAAE,CAAC;MAC7G5B,IAAI,EAAE,CAAAX,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEW,IAAI,KAAIZ,YAAY;MAC3C0C,gBAAgB,EAAE,CAAAzC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEyC,gBAAgB,MAAI7B,mBAAmB,aAAnBA,mBAAmB,wBAAAV,qBAAA,GAAnBU,mBAAmB,CAAE8B,YAAY,cAAAxC,qBAAA,uBAAjCA,qBAAA,CAAmCuC,gBAAgB,KAAIpD,qBAAqB,CAACoD,gBAAgB;MACpJE,SAAS,EAAE,CAAA3C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2C,SAAS,MAAI/B,mBAAmB,aAAnBA,mBAAmB,wBAAAT,sBAAA,GAAnBS,mBAAmB,CAAE8B,YAAY,cAAAvC,sBAAA,uBAAjCA,sBAAA,CAAmCwC,SAAS,KAAItD,qBAAqB,CAACsD,SAAS;MACxHC,OAAO,EAAE,CAAA5C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4C,OAAO,MAAIhC,mBAAmB,aAAnBA,mBAAmB,wBAAAR,sBAAA,GAAnBQ,mBAAmB,CAAE8B,YAAY,cAAAtC,sBAAA,uBAAjCA,sBAAA,CAAmCwC,OAAO,KAAIvD,qBAAqB,CAACuD,OAAO;MAChHC,YAAY,EAAE,CAAA7C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6C,YAAY,MAAIjC,mBAAmB,aAAnBA,mBAAmB,wBAAAP,sBAAA,GAAnBO,mBAAmB,CAAE8B,YAAY,cAAArC,sBAAA,uBAAjCA,sBAAA,CAAmCwC,YAAY,KAAIxD,qBAAqB,CAACwD,YAAY;MACpIhB,aAAa,EAAE,CAAA7B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE6B,aAAa,KAAIK,gBAAgB,CAACL,aAAa;MAC/EE,WAAW,EAAE,CAAA/B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+B,WAAW,KAAIG,gBAAgB,CAACH,WAAW;MACzEL,YAAY,EAAE,CAAA1B,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE0B,YAAY,KAAIN,kBAAkB,CAACtB,YAAY,CAAC;MAC/EgD,UAAU,EAAE,CAAA9C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8C,UAAU,KAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAAE;MAC5DC,WAAW,EAAE,CAAA/C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+C,WAAW,KAAI;IAC/C,CAAC;IACDC,kBAAkB,EAAE,IAAI;IACxBC,QAAQ,EAAGC,MAAM,IAAK;MACpBC,YAAY,CAACD,MAAM,CAAC;IACtB;EACF,CAAC,CAAC;EAEF,MAAMC,YAAY,GAAID,MAAM,IAAK;IAC/B,MAAME,UAAU,GAAG7C,KAAK,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,GAAG,KAAKW,MAAM,CAACb,cAAc,CAAC;IACnE,IAAI,CAACe,UAAU,EAAE;MACfzE,KAAK,CAAC4E,KAAK,CAAC,4BAA4B,CAAC;MACzC;IACF;IAEA,IAAIC,oBAAoB,GAAG,EAAE;IAE7B,IAAIxD,eAAe,EAAE;MACnB;MACAwD,oBAAoB,GAAG,CAACJ,UAAU,CAACK,aAAa,IAAI,EAAE,EAAEC,GAAG,CAAEC,QAAQ,IAAK;QACxE,IAAIA,QAAQ,CAACC,EAAE,KAAK5D,eAAe,CAAC4D,EAAE,EAAE;UACtC,OAAO;YACL,GAAGD,QAAQ;YACXhD,IAAI,EAAEuC,MAAM,CAACvC,IAAI;YACjB8B,gBAAgB,EAAES,MAAM,CAACT,gBAAgB;YACzCE,SAAS,EAAEO,MAAM,CAACP,SAAS;YAC3BC,OAAO,EAAEM,MAAM,CAACN,OAAO;YACvBC,YAAY,EAAEgB,UAAU,CAACX,MAAM,CAACL,YAAY,CAAC;YAC7ChB,aAAa,EAAEqB,MAAM,CAACrB,aAAa;YACnCE,WAAW,EAAEmB,MAAM,CAACnB,WAAW;YAC/BL,YAAY,EAAEwB,MAAM,CAACvC,IAAI,KAAK,OAAO,IAAIuC,MAAM,CAACvC,IAAI,KAAK,eAAe,GAAGuC,MAAM,CAACxB,YAAY,GAAG,IAAI;YACrGoB,UAAU,EAAEI,MAAM,CAACvC,IAAI,KAAK,QAAQ,GAAGuC,MAAM,CAACJ,UAAU,GAAG,IAAI;YAC/DC,WAAW,EAAEG,MAAM,CAACH,WAAW;YAC/Be,QAAQ,EAAEvE,iBAAiB,CAACwE,mBAAmB,CAACb,MAAM,CAACvC,IAAI;UAC7D,CAAC;QACH;QACA,OAAOgD,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMK,aAAa,GAAGzE,iBAAiB,CAAC0E,mBAAmB,CAACb,UAAU,CAACb,GAAG,EAAE;QAC1E5B,IAAI,EAAEuC,MAAM,CAACvC,IAAI;QACjB8B,gBAAgB,EAAES,MAAM,CAACT,gBAAgB;QACzCE,SAAS,EAAEO,MAAM,CAACP,SAAS;QAC3BC,OAAO,EAAEM,MAAM,CAACN,OAAO;QACvBC,YAAY,EAAEgB,UAAU,CAACX,MAAM,CAACL,YAAY,CAAC;QAC7ChB,aAAa,EAAEqB,MAAM,CAACrB,aAAa;QACnCE,WAAW,EAAEmB,MAAM,CAACnB,WAAW;QAC/BL,YAAY,EAAEwB,MAAM,CAACvC,IAAI,KAAK,OAAO,IAAIuC,MAAM,CAACvC,IAAI,KAAK,eAAe,GAAGuC,MAAM,CAACxB,YAAY,GAAG,IAAI;QACrGoB,UAAU,EAAEI,MAAM,CAACvC,IAAI,KAAK,QAAQ,GAAGuC,MAAM,CAACJ,UAAU,GAAG,IAAI;QAC/DC,WAAW,EAAEG,MAAM,CAACH;MACtB,CAAC,CAAC;MAEFS,oBAAoB,GAAG,CAAC,IAAIJ,UAAU,CAACK,aAAa,IAAI,EAAE,CAAC,EAAEO,aAAa,CAAC;IAC7E;IAEA,MAAME,MAAM,GAAG;MACbN,EAAE,EAAER,UAAU,CAACb,GAAG;MAClBkB,aAAa,EAAED;IACjB,CAAC;IAEDlD,QAAQ,CAACvB,WAAW,CAAC2B,UAAU,CAACwD,MAAM,CAAC,CAAC;EAC1C,CAAC;EAED,MAAMC,wBAAwB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACjDlC,MAAM,CAACmC,aAAa,CAACF,KAAK,EAAEC,KAAK,CAAC;;IAElC;IACA,IAAID,KAAK,KAAK,gBAAgB,EAAE;MAC9B,MAAMvE,YAAY,GAAGU,KAAK,CAAC8C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACf,GAAG,KAAK8B,KAAK,CAAC;MACrD,IAAIxE,YAAY,EAAE;QAAA,IAAA0E,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAChB7D,sBAAsB,CAAChB,YAAY,CAAC;QACpCsC,MAAM,CAACmC,aAAa,CAAC,kBAAkB,EAAE,EAAAC,qBAAA,GAAA1E,YAAY,CAAC6C,YAAY,cAAA6B,qBAAA,uBAAzBA,qBAAA,CAA2B9B,gBAAgB,KAAIpD,qBAAqB,CAACoD,gBAAgB,CAAC;QAC/HN,MAAM,CAACmC,aAAa,CAAC,WAAW,EAAE,EAAAE,sBAAA,GAAA3E,YAAY,CAAC6C,YAAY,cAAA8B,sBAAA,uBAAzBA,sBAAA,CAA2B7B,SAAS,KAAItD,qBAAqB,CAACsD,SAAS,CAAC;QAC1GR,MAAM,CAACmC,aAAa,CAAC,SAAS,EAAE,EAAAG,sBAAA,GAAA5E,YAAY,CAAC6C,YAAY,cAAA+B,sBAAA,uBAAzBA,sBAAA,CAA2B7B,OAAO,KAAIvD,qBAAqB,CAACuD,OAAO,CAAC;QACpGT,MAAM,CAACmC,aAAa,CAAC,cAAc,EAAE,EAAAI,sBAAA,GAAA7E,YAAY,CAAC6C,YAAY,cAAAgC,sBAAA,uBAAzBA,sBAAA,CAA2B7B,YAAY,KAAIxD,qBAAqB,CAACwD,YAAY,CAAC;MACrH;IACF;;IAEA;IACA,IAAIuB,KAAK,KAAK,MAAM,EAAE;MACpB,MAAMO,YAAY,GAAGlD,YAAY,CAAC4C,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACxB,YAAY,CAAC;MACpES,MAAM,CAACmC,aAAa,CAAC,eAAe,EAAEK,YAAY,CAAC9C,aAAa,CAAC;MACjEM,MAAM,CAACmC,aAAa,CAAC,aAAa,EAAEK,YAAY,CAAC5C,WAAW,CAAC;IAC/D;;IAEA;IACA,IAAIqC,KAAK,KAAK,cAAc,EAAE;MAC5B,MAAMO,YAAY,GAAGlD,YAAY,CAACU,MAAM,CAACe,MAAM,CAACvC,IAAI,EAAE0D,KAAK,CAAC;MAC5DlC,MAAM,CAACmC,aAAa,CAAC,eAAe,EAAEK,YAAY,CAAC9C,aAAa,CAAC;MACjEM,MAAM,CAACmC,aAAa,CAAC,aAAa,EAAEK,YAAY,CAAC5C,WAAW,CAAC;IAC/D;;IAEA;IACA,IAAIqC,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,SAAS,EAAE;MAChD,MAAMzB,SAAS,GAAGyB,KAAK,KAAK,WAAW,GAAGC,KAAK,GAAGlC,MAAM,CAACe,MAAM,CAACP,SAAS;MACzE,MAAMC,OAAO,GAAGwB,KAAK,KAAK,SAAS,GAAGC,KAAK,GAAGlC,MAAM,CAACe,MAAM,CAACN,OAAO;MAEnE,IAAID,SAAS,IAAIC,OAAO,EAAE;QACxB,MAAMgC,eAAe,GAAGrF,iBAAiB,CAACsF,cAAc,CAAClC,SAAS,EAAEC,OAAO,CAAC;QAC5ET,MAAM,CAACmC,aAAa,CAAC,cAAc,EAAEM,eAAe,CAAC;MACvD;IACF;;IAEA;IACA,IAAIR,KAAK,KAAK,WAAW,EAAE;MACzB,MAAMU,IAAI,GAAGC,QAAQ,CAACV,KAAK,CAAC7C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAC9C,IAAIsD,IAAI,IAAI,EAAE,IAAIA,IAAI,GAAG,CAAC,EAAE;QAC1B3C,MAAM,CAACmC,aAAa,CAAC,kBAAkB,EAAE,aAAa,CAAC;MACzD,CAAC,MAAM;QACLnC,MAAM,CAACmC,aAAa,CAAC,kBAAkB,EAAE,WAAW,CAAC;MACvD;IACF;;IAEA;IACAU,aAAa,CAAC,CAAC;EACjB,CAAC;EAED,MAAMA,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI7C,MAAM,CAACe,MAAM,CAACP,SAAS,IAAIR,MAAM,CAACe,MAAM,CAACN,OAAO,EAAE;MACpD,MAAMqC,QAAQ,GAAG;QACf,GAAGrE,mBAAmB;QACtB6C,aAAa,EAAE,CAAC;UACd9C,IAAI,EAAEwB,MAAM,CAACe,MAAM,CAACvC,IAAI;UACxBgC,SAAS,EAAER,MAAM,CAACe,MAAM,CAACP,SAAS;UAClCC,OAAO,EAAET,MAAM,CAACe,MAAM,CAACN,OAAO;UAC9BH,gBAAgB,EAAEN,MAAM,CAACe,MAAM,CAACT,gBAAgB;UAChDZ,aAAa,EAAEM,MAAM,CAACe,MAAM,CAACrB,aAAa;UAC1CE,WAAW,EAAEI,MAAM,CAACe,MAAM,CAACnB,WAAW;UACtC+B,QAAQ,EAAEvE,iBAAiB,CAACwE,mBAAmB,CAAC5B,MAAM,CAACe,MAAM,CAACvC,IAAI;QACpE,CAAC;MACH,CAAC;MAED,MAAMuE,iBAAiB,GAAG3F,iBAAiB,CAAC4F,oBAAoB,CAC9DF,QAAQ,EACR9C,MAAM,CAACe,MAAM,CAACxB,YAAY,IAAIS,MAAM,CAACe,MAAM,CAACrB,aAC9C,CAAC;MAEDd,kBAAkB,CAACmE,iBAAiB,CAAC;IACvC;EACF,CAAC;EAED,MAAME,qBAAqB,GAAIC,GAAG,IAAK;IACrC,MAAMC,WAAW,GAAGnD,MAAM,CAACe,MAAM,CAACJ,UAAU,IAAI,EAAE;IAClD,MAAMyC,OAAO,GAAGD,WAAW,CAACE,QAAQ,CAACH,GAAG,CAAC,GAAGC,WAAW,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKL,GAAG,CAAC,GAAG,CAAC,GAAGC,WAAW,EAAED,GAAG,CAAC;IAEtGlD,MAAM,CAACmC,aAAa,CAAC,YAAY,EAAEiB,OAAO,CAAC;EAC7C,CAAC;EAED,MAAMI,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAElE,oBACElG,OAAA,CAAC9B,MAAM;IAACgC,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAACgG,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DrG,OAAA,CAAC7B,WAAW;MAAAkI,QAAA,gBACVrG,OAAA,CAACxB,UAAU;QAAC8H,OAAO,EAAC,IAAI;QAAAD,QAAA,GACrB9F,eAAe,GAAG,MAAM,GAAG,QAAQ,EAAC,yBACvC;MAAA;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb1G,OAAA,CAACxB,UAAU;QAAC8H,OAAO,EAAC,OAAO;QAACK,KAAK,EAAC,gBAAgB;QAAAN,QAAA,EAC/C9F,eAAe,GAAG,0BAA0B,GAAG;MAA0D;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEd1G,OAAA,CAAC5B,aAAa;MAAAiI,QAAA,eACZrG,OAAA,CAACtB,GAAG;QAACkI,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAR,QAAA,eACjBrG,OAAA;UAAMwD,QAAQ,EAAEd,MAAM,CAACgB,YAAa;UAAA2C,QAAA,eAClCrG,OAAA,CAACzB,IAAI;YAACuI,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAV,QAAA,gBAEzBrG,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACX,WAAW;gBACV8H,KAAK,EAAC,aAAa;gBACnBC,IAAI,EAAC,gBAAgB;gBACrBxC,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACb,cAAe;gBACpCyE,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,gBAAgB,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBAC5E4C,QAAQ;gBAAAnB,QAAA,EAEPvF,KAAK,CAACmD,GAAG,CAAEwD,IAAI;kBAAA,IAAAC,iBAAA;kBAAA,oBACd1H,OAAA,CAACvB,QAAQ;oBAAgBmG,KAAK,EAAE6C,IAAI,CAAC3E,GAAI;oBAAAuD,QAAA,GACtCoB,IAAI,CAACL,IAAI,EAAC,KAAG,EAAC,EAAAM,iBAAA,GAAAD,IAAI,CAACE,WAAW,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAkBN,IAAI,KAAIK,IAAI,CAACG,IAAI,IAAI,SAAS;kBAAA,GADlDH,IAAI,CAAC3E,GAAG;oBAAAyD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEb,CAAC;gBAAA,CACZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGP1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACX,WAAW;gBACV8H,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,MAAM;gBACXxC,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACvC,IAAK;gBAC1BmG,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,MAAM,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBAClE4C,QAAQ;gBAAAnB,QAAA,EAEP1G,cAAc,CAACsE,GAAG,CAAE/C,IAAI,iBACvBlB,OAAA,CAACvB,QAAQ;kBAAkBmG,KAAK,EAAE1D,IAAI,CAAC0D,KAAM;kBAAAyB,QAAA,GAC1CnF,IAAI,CAACiG,KAAK,EAAC,cAAY,EAACjG,IAAI,CAACmD,QAAQ,EAAC,GACzC;gBAAA,GAFenD,IAAI,CAAC0D,KAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGP1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACX,WAAW;gBACV8H,KAAK,EAAC,mBAAmB;gBACzBC,IAAI,EAAC,kBAAkB;gBACvBxC,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACT,gBAAiB;gBACtCqE,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,kBAAkB,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBAC9E4C,QAAQ;gBAAAnB,QAAA,EAEP3G,kBAAkB,CAACuE,GAAG,CAAE4D,QAAQ,iBAC/B7H,OAAA,CAACvB,QAAQ;kBAAsBmG,KAAK,EAAEiD,QAAQ,CAACjD,KAAM;kBAAAyB,QAAA,EAClDwB,QAAQ,CAACV;gBAAK,GADFU,QAAQ,CAACjD,KAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEnB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGP1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACZ,KAAK;gBACJ+H,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,cAAc;gBACnBlG,IAAI,EAAC,QAAQ;gBACb4G,IAAI,EAAC,KAAK;gBACVlD,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACL,YAAa;gBAClCiE,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,cAAc,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBAC1E4C,QAAQ;gBACRO,UAAU,EACRrF,MAAM,CAACe,MAAM,CAACP,SAAS,IAAIR,MAAM,CAACe,MAAM,CAACN,OAAO,GAAG,eAAerD,iBAAiB,CAACsF,cAAc,CAAC1C,MAAM,CAACe,MAAM,CAACP,SAAS,EAAER,MAAM,CAACe,MAAM,CAACN,OAAO,CAAC,QAAQ,GAAG;cAC9J;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGP1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACX,WAAW;gBACV8H,KAAK,EAAC,YAAY;gBAClBC,IAAI,EAAC,WAAW;gBAChBxC,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACP,SAAU;gBAC/BmE,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,WAAW,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBACvE4C,QAAQ;gBAAAnB,QAAA,EAEPxG,YAAY,CAACoE,GAAG,CAAE+D,MAAM,iBACvBhI,OAAA,CAACvB,QAAQ;kBAAoBmG,KAAK,EAAEoD,MAAM,CAACpD,KAAM;kBAAAyB,QAAA,EAC9C2B,MAAM,CAACb;gBAAK,GADAa,MAAM,CAACpD,KAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEP1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACX,WAAW;gBACV8H,KAAK,EAAC,UAAU;gBAChBC,IAAI,EAAC,SAAS;gBACdxC,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACN,OAAQ;gBAC7BkE,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,SAAS,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBACrE4C,QAAQ;gBAAAnB,QAAA,EAEPxG,YAAY,CAACoE,GAAG,CAAE+D,MAAM,iBACvBhI,OAAA,CAACvB,QAAQ;kBAAoBmG,KAAK,EAAEoD,MAAM,CAACpD,KAAM;kBAAAyB,QAAA,EAC9C2B,MAAM,CAACb;gBAAK,GADAa,MAAM,CAACpD,KAAK;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEjB,CACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGP1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACZ,KAAK;gBACJ+H,KAAK,EAAC,gBAAgB;gBACtBC,IAAI,EAAC,eAAe;gBACpBlG,IAAI,EAAC,MAAM;gBACX0D,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACrB,aAAc;gBACnCiF,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,eAAe,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBAC3E4C,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACZ,KAAK;gBACJ+H,KAAK,EAAC,cAAc;gBACpBC,IAAI,EAAC,aAAa;gBAClBlG,IAAI,EAAC,MAAM;gBACX0D,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACnB,WAAY;gBACjC+E,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,aAAa,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBACzE4C,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGN,CAAChE,MAAM,CAACe,MAAM,CAACvC,IAAI,KAAK,OAAO,IAAIwB,MAAM,CAACe,MAAM,CAACvC,IAAI,KAAK,eAAe,kBACxElB,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAAb,QAAA,eACvBrG,OAAA,CAACZ,KAAK;gBACJ+H,KAAK,EAAC,eAAe;gBACrBC,IAAI,EAAC,cAAc;gBACnBlG,IAAI,EAAC,MAAM;gBACX0D,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACxB,YAAa;gBAClCoF,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,cAAc,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBAC1E4C,QAAQ;cAAA;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACP,EAGAhE,MAAM,CAACe,MAAM,CAACvC,IAAI,KAAK,QAAQ,iBAC9BlB,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAZ,QAAA,gBAChBrG,OAAA,CAACxB,UAAU;gBAAC8H,OAAO,EAAC,WAAW;gBAAC2B,YAAY;gBAAA5B,QAAA,EAAC;cAE7C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb1G,OAAA,CAACtB,GAAG;gBAACkI,EAAE,EAAE;kBAAEsB,OAAO,EAAE,MAAM;kBAAEC,GAAG,EAAE,CAAC;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAA/B,QAAA,EACpDH,QAAQ,CAACjC,GAAG,CAAC,CAAC2B,GAAG,EAAEyC,KAAK;kBAAA,IAAAC,qBAAA;kBAAA,oBACvBtI,OAAA,CAACpB,gBAAgB;oBAEf2J,OAAO,eACLvI,OAAA,CAACnB,QAAQ;sBACP2J,OAAO,EAAE,EAAAF,qBAAA,GAAA5F,MAAM,CAACe,MAAM,CAACJ,UAAU,cAAAiF,qBAAA,uBAAxBA,qBAAA,CAA0BvC,QAAQ,CAACsC,KAAK,CAAC,KAAI,KAAM;sBAC5DhB,QAAQ,EAAEA,CAAA,KAAM1B,qBAAqB,CAAC0C,KAAK;oBAAE;sBAAA9B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CACF;oBACDS,KAAK,EAAEvB;kBAAI,GAPNyC,KAAK;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQX,CAAC;gBAAA,CACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CACP,eAGD1G,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAZ,QAAA,eAChBrG,OAAA,CAACZ,KAAK;gBACJ+H,KAAK,EAAC,aAAa;gBACnBC,IAAI,EAAC,aAAa;gBAClBqB,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACR9D,KAAK,EAAElC,MAAM,CAACe,MAAM,CAACH,WAAY;gBACjC+D,QAAQ,EAAGC,CAAC,IAAK5C,wBAAwB,CAAC,aAAa,EAAE4C,CAAC,CAACC,MAAM,CAAC3C,KAAK,CAAE;gBACzE+D,WAAW,EAAC;cAAwC;gBAAApC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EAGNrF,eAAe,iBACdrB,OAAA,CAACzB,IAAI;cAACyI,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAZ,QAAA,eAChBrG,OAAA,CAAClB,KAAK;gBAAC8J,QAAQ,EAAC,MAAM;gBAAAvC,QAAA,gBACpBrG,OAAA,CAACxB,UAAU;kBAAC8H,OAAO,EAAC,WAAW;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9D1G,OAAA,CAACxB,UAAU;kBAAC8H,OAAO,EAAC,OAAO;kBAAAD,QAAA,GACxBhF,eAAe,CAAC2B,gBAAgB,KAAK,WAAW,GAAG,WAAW,GAAG,aAAa,EAAC,IAAE,EAAC,GAAG,EACrF3B,eAAe,CAAC6B,SAAS,EAAC,KAAG,EAAC7B,eAAe,CAAC8B,OAAO,EAAC,IAAE,EAAC9B,eAAe,CAAC+B,YAAY,EAAC,IACzF;gBAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB1G,OAAA,CAAC3B,aAAa;MAAAgI,QAAA,gBACZrG,OAAA,CAAC1B,MAAM;QAACuK,OAAO,EAAE1I,OAAQ;QAACwG,KAAK,EAAC,WAAW;QAAAN,QAAA,EAAC;MAE5C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1G,OAAA,CAAC1B,MAAM;QACLuK,OAAO,EAAEnG,MAAM,CAACgB,YAAa;QAC7B4C,OAAO,EAAC,WAAW;QACnBK,KAAK,EAAC,SAAS;QAAAN,QAAA,EAEd9F,eAAe,GAAG,iBAAiB,GAAG;MAAiB;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAAClG,EAAA,CA5bIP,wBAAwB;EAAA,QACXjB,WAAW,EACdC,WAAW,EACTA,WAAW,EA2DZF,SAAS;AAAA;AAAA+J,EAAA,GA9DpB7I,wBAAwB;AA8b9BA,wBAAwB,CAAC8I,SAAS,GAAG;EACnC7I,IAAI,EAAEf,SAAS,CAAC6J,IAAI,CAACC,UAAU;EAC/B9I,OAAO,EAAEhB,SAAS,CAAC+J,IAAI,CAACD,UAAU;EAClC7I,YAAY,EAAEjB,SAAS,CAACgK,MAAM;EAC9B9I,YAAY,EAAElB,SAAS,CAACiK,MAAM;EAC9B9I,YAAY,EAAEnB,SAAS,CAACiK,MAAM;EAC9B7I,eAAe,EAAEpB,SAAS,CAACgK;AAC7B,CAAC;AAED,eAAelJ,wBAAwB;AAAC,IAAA6I,EAAA;AAAAO,YAAA,CAAAP,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}