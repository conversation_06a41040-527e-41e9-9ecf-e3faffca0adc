'use strict';

const User = require("../models/user.model");

exports.getAllUsersWithPermissions = async (queries) => {
    const limit = queries.limit ?? 20;
    const page = queries.page ?? 1;
    const skip = limit * (page - 1);
    const sort = queries.sort ?? { name: -1 };
    const keyword = queries.keyword ?? "";

    const query = keyword
        ? { name: { $regex: keyword, $options: 'i' } }
        : {};

    const users = await User
        .find(query, { name: 1, email: 1, permissions: 1 }) // project only needed fields
        .skip(skip)
        .limit(limit)
        .sort(sort);

    const total = await User.countDocuments(query);

    return {
        data: users,
        pagination: {
            perPage: limit,
            currentPage: page,
            total,
            pages: Math.ceil(total / limit)
        }
    };
};

exports.getPermissionsByUserId = async (userId) => {
    const user = await User.findById(userId, { permissions: 1 });
    return user?.permissions || [];
};

exports.updatePermissionsByUserId = async (userId, permissions) => {
    const user = await User.findByIdAndUpdate(
        userId,
        { permissions },
        { new: true }
    );
    return user;
};
