import React from "react";
import {
    AccountBalance,
    AccountBalanceWallet,
    AccountTree, Assignment, AssignmentIndOutlined, CalendarToday,
    Dashboard,
    People, Settings,
    MoreTime,
    Task,
    EventNote
} from "@mui/icons-material";
import {actions, features} from "../../constants/permission";
import ROLES from "../../constants/role";

// Admin-only menus
const AdminMenus = [
    { name: "Admin Dashboard", icon: <Dashboard/>, path: "/app/admin-dashboard", act: actions.readAll, feat: 'All' },
    { name: "Employee Management", icon: <People/>, path: "/app/user", act: actions.read, feat: features.user },
    { name: "Department", icon: <AccountBalance/>, path: "/app/department", act: actions.read, feat: features.department },
    { name: "Designation", icon: <AccountTree/>, path: "/app/designation", act: actions.read, feat: features.designation },
    { name: "Attendance", icon: <CalendarToday/>, path: "/app/attendance", act: actions.readAll, feat: features.attendance },
    { name: "Expenses", icon: <AccountBalanceWallet/>, path: "/app/expenses", act: actions.read, feat: features.expense },
    {
        name: "Leave Management",
        icon: <AssignmentIndOutlined/>,
        path: "/app/leave",
        act: actions.readAll,
        feat: features.leave,
        children: [
            { name: "Leave Report", path: "/app/leave/report", act: actions.readAll, feat: features.leavereport },
            { name: "Approval", path: "/app/leave/approval", act: actions.readAll, feat: features.approve },
            { name: "Calendar", path:"/app/leave/calendar", act:actions.readAll, feat: features.calendar},
            { name: "Configuration", path:"/app/leave/configuration", act:actions.readAll, feat: features.configuration}
        ]
    },
    {
        name: "Projects",
        icon: <Task/>,
        path:"/app/project/list",
        act:actions.readAll,
        feat: features.projectlist,
        children: [
            { name: "Project Overview", path: "/app/project/overview", act: actions.readAll, feat: features.projectoverview },
            { name: "Project List", path: "/app/project/list", act: actions.readAll, feat: features.projectlist },
            { name: "Project Timesheet", path:"/app/project/timesheet", act:actions.readAll, feat: features.projecttimesheet}
        ]
    },
    {
        name: "Timeline",
        icon: <MoreTime/>,
        path: "/app/timeline",
        act: actions.readAll,
        feat: features.timeline,
        children: [
            { name: "Overview", path: "/app/timeline/overview", act: actions.readAll, feat: features.overview },
            { name: "Time Request", path: "/app/timeline/request", act: actions.readAll, feat: features.timerequest },
            { name: "Task Request", path:"/app/timeline/taskrequest", act:actions.readAll, feat: features.taskrequest},
            { name: "Work Schedule", path:"/app/timeline/workschedule", act:actions.readAll, feat: features.workschedule}
        ]
    },
    { name: "Client", icon: <AccountBalance/>, path: "/app/client", act: actions.readAll, feat: features.client },
    { name: "Report", icon: <Assignment/>, path: "/app/report", act: actions.read, feat: features.report },
    { name: "Setting", icon: <Settings/>, path: "/app/setting", act: actions.read, feat: features.setting }, // Changed from update to read
];

// User-only menus
const UserMenus = [
    { name: "My Dashboard", icon: <Dashboard/>, path: "/app/user-dashboard", act: actions.readAll, feat: 'All' },
    { name: "My Attendance", icon: <CalendarToday/>, path: "/app/attendance", act: actions.read, feat: features.attendance },
    { name: "My Leave", icon: <AssignmentIndOutlined/>, path: "/app/user/leave", act: actions.read, feat: features.userleave },
    { name: "My Projects", icon: <EventNote/>, path: '/app/user/projects', act: actions.read, feat: features.projectsemployee},
    // Make sure the feature name matches exactly what's in the permissions array
    { name: "My Tasks", icon: <Task/>, path: "/app/tasks", act: actions.read, feat: "My Tasks" },
    { name: "My Timeline", icon: <MoreTime/>, path: "/app/user/timeline", act: actions.read, feat: features.usertimeline},
    { name: "Settings", icon: <Settings/>, path: "/app/setting", act: actions.read, feat: features.setting }, // Added Settings to user menus
];

// Function to get menus based on user role and permissions
const getMenusForUser = (userRoles, userPermissions) => {
    if (!userRoles || !Array.isArray(userRoles)) {
        return [];
    }

    // If user is admin, return admin menus
    if (userRoles.includes(ROLES.admin.id)) {
        console.log('User is admin, returning admin menus');
        return AdminMenus;
    }

    // If user is HR, return admin menus (they have same access as admin)
    if (userRoles.includes(ROLES.humanresource.id)) {
        console.log('User is HR, returning admin menus');
        return AdminMenus;
    }

    // Log user permissions for debugging
    console.log('User permissions for menu filtering:', userPermissions);

    // Otherwise return user menus
    console.log('User is not admin/HR, returning user menus');
    return UserMenus;
};

// Default export combines both menu sets for backward compatibility
const AllMenus = [...AdminMenus, ...UserMenus];

export { getMenusForUser, AdminMenus, UserMenus };
export default AllMenus;