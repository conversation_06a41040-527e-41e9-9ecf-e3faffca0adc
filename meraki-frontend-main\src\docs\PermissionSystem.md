# Meraki HR System - Permission System Documentation

## Overview

The Meraki HR System uses a database-driven permission system that allows administrators to control access to different features of the application. This document explains how the permission system works and how to use it.

## Permission Structure

Permissions in the Meraki HR System are structured around two key concepts:

1. **Features**: Specific areas or modules of the application (e.g., "User", "Attendance", "Leave")
2. **Actions**: Operations that can be performed on features (e.g., "read", "create", "update", "delete")

Each user has a set of permissions stored in the database. These permissions determine what features the user can access and what actions they can perform on those features.

## Permission Actions

The system uses two types of permission actions:

### Basic Actions

These are the standard CRUD operations available for most features:

- **read**: Allows viewing resources
- **create**: Allows creating new resources
- **update**: Allows modifying existing resources
- **delete**: Allows removing resources

### Specialized Read Permissions

Some features require more granular read permissions:

- **read_all**: Full read access to all items in a feature
  - Required for admin dashboards and reports
  - Required for viewing all users' data (attendance, leave, etc.)
  - Example: `Can('read_all', 'Dashboard')` for admin dashboard

- **read_some**: Read access to a subset of items (e.g., department-specific)
  - Used for department managers who need to see their team's data
  - Example: `Can('read_some', 'Attendance')` for viewing department attendance

- **read_self**: Read access only to the user's own data
  - Used for regular users to view their own information
  - Example: `Can('read_self', 'Attendance')` for viewing own attendance

### When to Use Specialized Read Permissions

- Use **read_all** when a feature needs to display data for all users
- Use **read_some** when a feature needs to display data for a subset of users
- Use **read_self** when a feature needs to display only the current user's data

## Permission Inheritance

If a user has `create`, `update`, or `delete` permission for a feature, they automatically get `read` permission for that feature. This is implemented in the `Can()` utility function.

## Permission Checking

The system uses a `Can()` utility function to check if a user has permission to perform an action on a feature. This function is used throughout the application to control access to routes, menus, and UI components.

```javascript
// Example usage
import Can from 'utils/can';

// Check if user can read the User feature
const canReadUser = Can('read', 'User');

// Check if user can create a new Department
const canCreateDepartment = Can('create', 'Department');
```

## Route Protection

Routes are protected using the `PermissionRoute` component, which checks if the user has the required permissions before rendering the component.

```javascript
// Example route definition
{
    path: "/app/user",
    component: User,
    permission: { feat: "User", act: "read" }
}
```

If the user doesn't have the required permission, they will see an "Access Denied" message.

## Menu Generation

Menus are generated dynamically based on the user's permissions. The `generateMenus()` function filters menu items based on the user's permissions.

```javascript
// Example menu definition
{
    name: "Employee Management",
    icon: People,
    path: "/app/user",
    act: "read",
    feat: "User"
}
```

If the user doesn't have the required permission, the menu item will not be displayed.

## Permission Management

Administrators can manage user permissions through the Permission component. This component allows administrators to:

1. View a user's current permissions
2. Grant or revoke permissions for different features
3. Save the updated permissions to the database

## Feature Categories

Features are organized into categories for better management:

1. **Core Features**: Dashboard, User, Department, Designation, Setting
2. **Attendance & Leave**: Attendance, Leave, Calendar, Configuration, Approve, Leave Report
3. **Projects & Tasks**: Projects, Project List, Project Overview, Project Timesheet, Client, Tasks
4. **Timeline & Scheduling**: Timeline, Overview, Time Request, Task Request, Work Schedule
5. **Finance**: Expense, Report

## Admin vs. User Features

Some features have both admin and user versions:

- **Admin features**: Accessible to administrators and users with specific permissions
- **User features**: Accessible to regular users with appropriate permissions

For example, the "Projects" feature has both admin and user versions:
- Admin: `/app/project/list`
- User: `/app/user/projects`

## Default Permissions

By default, all users have access to:
- Their dashboard
- Their profile

Administrators have additional default permissions for:
- Employee management
- User permission management

## Permission Naming Conventions

To ensure consistency, the system uses the following naming conventions:

1. Feature names are case-sensitive and should match exactly what's in the database
2. Admin features use PascalCase (e.g., "User", "Department")
3. User-specific features use camelCase (e.g., "myTasks", "userLeave")

## Troubleshooting

If a user is unable to access a feature they should have access to, check:

1. The user's permissions in the database
2. The permission requirements for the route or menu
3. The `Can()` function logs in the browser console

## Best Practices

1. Always use the `Can()` function to check permissions
2. Use the `PermissionRoute` component to protect routes
3. Use the `generateMenus()` function to generate menus
4. Be consistent with feature and action naming
5. Group related permissions together in the Permission component
