import { API_BASE_URL } from "../utils/apiConfig";

const API_URL = "http://localhost:10000/api";

async function createProduct(params) {
    const token = localStorage.getItem("merakihr-token");
    console.log("Create Product Service ", params);
    // Extract the data property if it exists (for when called from saga)
    const data = params.data || params;
    console.log("Sending data to backend:", data);
    
    try {
        // Check if we need to add this product to a sprint
        if (data.sprintId && data.addToSprint) {
            const result = await fetch(`${API_URL}/product/create`, {
                method: 'POST',
                body: JSON.stringify(data),
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (!result.ok) {
                throw new Error('Failed to create product');
            }
            
            const productData = await result.json();
            const productId = productData.data._id;
            
            // Now add the product to the sprint
            await fetch(`${API_URL}/sprint/add-project`, {
                method: 'POST',
                body: JSON.stringify({
                    productId: productId,
                    sprintId: data.sprintId
                }),
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            return result;
        } else {
            // Regular product creation without sprint
            const result = await fetch(`${API_URL}/product/create`, {
                method: 'POST',
                body: JSON.stringify(data),
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            return result;
        }
    } catch (error) {
        console.error("Error creating product:", error);
        throw error;
    }
}

async function updateProduct(id, body) {
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/product/update/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(body),
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return result;
}

async function deleteProduct(id) {
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/product/delete/${id}`, {
        method: 'DELETE',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return result;
}

async function getProductByUserId(id) {
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/product/${id}`, {
        method: 'GET',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return result;
}

async function getProducts(filter) {
    const token = localStorage.getItem("merakihr-token");
    console.log("Fetching products... Token:", token);
    console.log("Filter:", filter);

    try {
        // Build query string from filter if provided
        let url = `${API_URL}/product`;
        if (filter) {
            const queryParams = new URLSearchParams();
            if (filter.page) {
                queryParams.append('page', filter.page);
            }
            if (filter.limit) {
                queryParams.append('limit', filter.limit);
            }
            if (filter.sort) {
                queryParams.append('sort', filter.sort);
            }

            const queryString = queryParams.toString();
            if (queryString) {
                url += `?${queryString}`;
            }
        }

        console.log("Fetching from URL:", url);

        const response = await fetch(url, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        console.log("Response Status:", response.status);
        console.log("Response Headers:", response.headers);

        const data = await response.json();
        console.log("API Response Data:", data);

        return data;
    } catch (error) {
        console.error("API Fetch Error:", error);
        return { pagination: {}, data: [] }; // Return empty data on error
    }
}

async function createProductsTask(id, data) {
    const token = localStorage.getItem("merakihr-token");
    console.log("Create ProductsTask Service ", id, data);
    
    try {
        // Create the task
        const result = await fetch(`${API_URL}/product/create/task/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(data),
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!result.ok) {
            throw new Error('Failed to create task');
        }
        
        const taskData = await result.json();
        
        // If sprintId is provided, add the task to the sprint
        if (data.sprintId && data.addToSprint) {
            const taskId = taskData.data._id;
            
            await fetch(`${API_URL}/sprint/add-task`, {
                method: 'POST',
                body: JSON.stringify({
                    productId: id,
                    taskId: taskId,
                    sprintId: data.sprintId
                }),
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
        }
        
        return result;
    } catch (error) {
        console.error("Error creating task:", error);
        throw error;
    }
}

async function getProductById(id) {
    const token = localStorage.getItem("merakihr-token");
    console.log("Get Product By Id ", id);
    const result = await fetch(`${API_URL}/product/${id}`, {
        method: 'GET',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return result;
}

async function getProductsByUser(id) {
    const token = localStorage.getItem("merakihr-token");
    console.log("Get Products By User ", id);
    const result = await fetch(`${API_URL}/product/user/${id}`, {
        method: 'GET',
        headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    });
    return result;
}

async function updateTask(productId, taskId, data) {
    const token = localStorage.getItem("merakihr-token");
    console.log("Update Task ", productId, taskId, data);

    try {
        const result = await fetch(`${API_URL}/product/update/task/${productId}/${taskId}`, {
            method: 'PATCH',
            body: JSON.stringify(data),
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!result.ok) {
            const error = await result.json();
            console.error("Error updating task:", error);
            throw new Error(`Failed to update task: ${error.message || result.statusText}`);
        }

        const responseData = await result.json();
        console.log('Response Data:', responseData);
        return responseData; // Returning the response if it's valid
    } catch (error) {
        console.error("Error in updateTask function:", error);
        throw error; // Rethrow the error for higher-level handling
    }
}

// Start Task function
async function startTask(payload) {
    const token = localStorage.getItem("merakihr-token");
    const { taskId, projectId, date } = payload;
    console.log("Start Task Service", projectId, taskId, "Date:", date);
    
    try {
        const result = await fetch(`${API_URL}/product/start-task/${projectId}/${taskId}`, {
            method: 'PATCH',
            body: JSON.stringify({ date }),
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!result.ok) {
            const error = await result.json();
            throw new Error(`Failed to start task: ${error.message || result.statusText}`);
        }
        
        return await result.json();
    } catch (error) {
        console.error("Error in startTask function:", error);
        throw error;
    }
}

// Stop Task function
async function stopTask(payload) {
    const token = localStorage.getItem("merakihr-token");
    const { taskId, projectId, elapsedTime, date, pauseHistory } = payload;
    console.log("Stop Task Service", projectId, taskId, "Elapsed time:", elapsedTime, "Date:", date);
    
    try {
        const result = await fetch(`${API_URL}/product/stop-task/${projectId}/${taskId}`, {
            method: 'PATCH',
            body: JSON.stringify({ 
                elapsedTime,
                date,
                pauseHistory
            }),
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!result.ok) {
            const error = await result.json();
            throw new Error(`Failed to stop task: ${error.message || result.statusText}`);
        }
        
        return await result.json();
    } catch (error) {
        console.error("Error in stopTask function:", error);
        throw error;
    }
}

// Pause Task function
async function pauseTask(payload) {
    const token = localStorage.getItem("merakihr-token");
    const { taskId, projectId, elapsedTime, pauseTime, date, startTime } = payload;
    console.log("Pause Task Service", projectId, taskId, "Elapsed time:", elapsedTime, "Date:", date);
    
    try {
        const result = await fetch(`${API_URL}/product/pause-task/${projectId}/${taskId}`, {
            method: 'PATCH',
            body: JSON.stringify({ 
                elapsedTime,
                pauseTime: pauseTime || new Date().toISOString(),
                date,
                startTime
            }),
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!result.ok) {
            const error = await result.json();
            throw new Error(`Failed to pause task: ${error.message || result.statusText}`);
        }
        
        return await result.json();
    } catch (error) {
        console.error("Error in pauseTask function:", error);
        throw error;
    }
}

export const ProductService = {
    createProduct,
    updateProduct,
    deleteProduct,
    getProductByUserId,
    getProducts,
    createProductsTask,
    getProductById,
    getProductsByUser,
    updateTask,
    startTask,
    stopTask,
    pauseTask
};