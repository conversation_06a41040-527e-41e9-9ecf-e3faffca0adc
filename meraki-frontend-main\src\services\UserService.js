/**
 * User Service
 *
 * This service provides methods for interacting with the user-related API endpoints.
 * It handles user management operations like fetching, creating, updating, and deleting users.
 */

import { del, get, patch, post } from "../utils/api";
const API_URL = "http://localhost:10000/api";

// Use the centralized API URL from apiConfig.js
// This ensures consistent API URLs across the application

/**
 * Get a list of users with optional filtering
 *
 * @param {Object} params - Query parameters for filtering users
 * @returns {Promise<Object>} Response containing users data and pagination
 */
const GetUsers = async (params) => {
    try {
        return await get(`user`, params);
    } catch (error) {
        throw new Error(`Failed to fetch users: ${error.message}`);
    }
};

/**
 * Get a specific user by ID
 *
 * @param {string} id - User ID
 * @returns {Promise<Object>} User data
 */
const GetUserById = async (id) => {
    try {
        return await get(`user/${id}`);
    } catch (error) {
        throw new Error(`Failed to fetch user ${id}: ${error.message}`);
    }
};

/**
 * Create a new user
 *
 * @param {Object} params - User data
 * @returns {Promise<Object>} Created user data
 */
const CreateUser = async (params) => {
    try {
        return await post(`user`, params);
    } catch (error) {
        throw new Error(`Failed to create user: ${error.message}`);
    }
};

/**
 * Update an existing user
 *
 * @param {string} id - User ID
 * @param {Object} params - Updated user data
 * @returns {Promise<Object>} Updated user data
 */
const UpdateUser = async (id, params) => {
    try {
        return await patch(`${API_URL}/user/${id}`, params);
    } catch (error) {
        throw new Error(`Failed to update user ${id}: ${error.message}`);
    }
};

/**
 * Delete a user
 *
 * @param {string} id - User ID
 * @returns {Promise<Object>} Deletion confirmation
 */
const DeleteUser = async (id) => {
    try {
        return await del(`${API_URL}/user/${id}`);
    } catch (error) {
        throw new Error(`Failed to delete user ${id}: ${error.message}`);
    }
};

/**
 * Get the profile of the currently logged-in user
 *
 * @returns {Promise<Object>} Current user's profile data
 */
const Profile = async () => {
    try {
        return await get(`${API_URL}/user/profile`);
    } catch (error) {
        throw new Error(`Failed to fetch user profile: ${error.message}`);
    }
};

/**
 * Update a user's leave information
 *
 * @param {string} id - User ID
 * @param {Object} params - Leave data to update
 * @returns {Promise<Object>} Updated user data
 */
const UpdateUserLeave = async (id, params) => {
    try {
        return await patch(`${API_URL}/user/update/leave/${id}`, params);
    } catch (error) {
        throw new Error(`Failed to update user leave for ${id}: ${error.message}`);
    }
};

/**
 * User service object with all user-related API methods
 */
export const UserService = {
    GetUsers,
    GetUserById,
    CreateUser,
    UpdateUser,
    DeleteUser,
    Profile,
    UpdateUserLeave
};
