import React, { useEffect, useState } from "react";
import {
  Box,
  Card,
  Table,
  TableBody,
  TableCell,
  TableHead,
  Pagination,
  TableRow,
  Hidden,
  IconButton
} from "@mui/material";
import Typography from "@mui/material/Typography";
import { useDispatch, useSelector } from "react-redux";
import { DefaultSort } from "constants/sort";
import { ClientSelector, ProductSelector, UserSelector } from "selectors";
import { ClientActions, ProductActions, UserActions } from "slices/actions";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import ProductHeaderFilterStaff from "../components/ProductHeaderFilterStaff";
import { Delete, Visibility } from "@mui/icons-material";

function ProductListStaff() {
  const [filter, setFilter] = useState({ sort: DefaultSort.newest.value, page: 1 });
  const products = useSelector(ProductSelector.getProducts()) || [];
  const pagination = useSelector(ProductSelector.getPagination()) || { pages: 1 };
  const profile = useSelector(UserSelector.profile());
  const users = useSelector(UserSelector.getUsers()) || [];
  const clients = useSelector(ClientSelector.getClients()) || [];

  const [filterdArr, setFilteredArr] = useState(products);
  const dispatch = useDispatch();
  const history = useHistory();

  useEffect(() => {
    dispatch(UserActions.getUsers());
    dispatch(ClientActions.getClients());
  }, [dispatch]);

  useEffect(() => {
    if (profile) {
      dispatch(ProductActions.getProductsByUser({ id: profile._id, query: filter }));
      console.log("Fetching products with filter:", filter);
    }
  }, [profile, filter, dispatch]);

  useEffect(() => {
    setFilteredArr(products);
    console.log("Updated filteredArr List Staff:", products);
  }, [products]);

  const handleChangePagination = (e, val) => {
    setFilter((prev) => ({ ...prev, page: val }));
  };

  return (
    <Card style={{ overflow: "scroll" }}>
      <Typography variant="h5" sx={{ fontWeight: 600 }}>Projects</Typography>
      <ProductHeaderFilterStaff projects={products} filteredFun={setFilteredArr} />
      <Box>
        <Table>
          <TableHead>
            <TableRow>
              <Hidden smDown>
                <TableCell align="center">Project Name</TableCell>
                <TableCell align="center">Total Hr</TableCell>
                <TableCell align="center">Members</TableCell>
                <TableCell align="center">Client</TableCell>
                <TableCell align="center">Access</TableCell>
                <TableCell align="center">Action</TableCell>
              </Hidden>
            </TableRow>
          </TableHead>
          <TableBody>
            {filterdArr.length > 0 ? (
              filterdArr.map((data, index) => (
                <TableRow key={index} sx={{ "&:last-child td, &:last-child td": { border: 0 } }}>
                  <TableCell align="center">{data.productName}</TableCell>
                  <TableCell align="center">{data?.totalHours}</TableCell>
                  <TableCell align="center">
                    <div style={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                      {data?.members?.map((assigneeId) => {
                        const user = users.find((u) => assigneeId === u._id);
                        return user ? user.name : "";
                      }).filter(Boolean).join(", ")}
                    </div>
                  </TableCell>
                  <TableCell align="center">
                    {clients.find((c) => data?.client === c._id)?.name || "N/A"}
                  </TableCell>
                  <TableCell align="center">{data.visibility ? "Public" : "Private"}</TableCell>
                  <TableCell align="center">
                    <IconButton onClick={() => history.push(`/app/user/tasklist/note/${data._id}`)}>
                      <Visibility />
                    </IconButton>
                    <IconButton>
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} align="center">No Data Found</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <Pagination sx={{ mt: 1 }} page={filter.page} count={pagination.pages} onChange={handleChangePagination} />
      </Box>
    </Card>
  );
}

export default ProductListStaff;
