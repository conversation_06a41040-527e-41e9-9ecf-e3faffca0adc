/**
 * ProductivityChart Component
 *
 * This component visualizes the user's daily activity and productivity
 * with a timeline chart showing different activity states:
 * - Active work time (green)
 * - Idle time (yellow)
 * - Break time (red)
 * - Timeline requests (blue)
 * - Inactive time (light grey)
 */

import PropTypes from "prop-types";
import React, { useState, useEffect, useContext } from "react";
import "../../../App.css";
import { useDispatch, useSelector } from "react-redux";
import { ActivityActions, TimelineActions } from "../../../slices/actions";
import { backContext } from "./Backgroundprovider";
import { TimelineSelector, UserSelector } from "selectors";
import {
  Box, Typography, Grid, Paper, Tooltip
} from '@mui/material';
import TimelineRequest from "./TimelineRequest";


const ProductivityChart = ({ todayActivities }) => {

  const profile = useSelector(UserSelector.profile());
  let minArr = [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
    21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39,
    40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58,
    59,
  ];
  let minArrRev = [...minArr].reverse();

  const hours = Array.from({ length: 24 }, (_, i) => `${i} AM`);
  hours[12] = "12 PM";
  for (let i = 13; i < 24; i++) {
    hours[i] = `${i - 12} PM`;
  }
  const dispatch = useDispatch();
  const [startTime, setStartTime] = useState(null);
  const [endTime, setEndTime] = useState(null);
  const [startTimeline,setStartTimeline] = useState(null)
  const [endTimeline,setEndTimeline] = useState(null)
  const [currentTime, setCurrentTime] = useState(new Date());
  const [chartController, setChartUpdate] = useState(true);
  const [toolTipTitle, setTooltipTitle] = useState("");
  const [toolTipController,setToolTipController] = useState(false)
  const [addRequest,setAddRequest] = useState(false)
  const [fromRequest,setFromRequest] = useState("Idel")

  const timeline = useSelector(TimelineSelector.getTimelineRequests())
  const handleClose = () => { setAddRequest(false); };
  /**
   * Initialize chart data and set up auto-refresh
   *
   * This effect:
   * 1. Sets the start and end times based on check-in/out data
   * 2. Fetches timeline requests for the current date
   * 3. Sets up an interval to refresh activity data every 30 seconds
   */
  useEffect(() => {
    // Set start time from check-in data
    if (todayActivities.length > 0) {
      setStartTime(new Date(todayActivities[0].checkInTime));

      // If user has checked out, update end time and stop chart updates
      if (Object.keys(todayActivities[0]).includes("checkOutTime")) {
        setChartUpdate(false);
        setEndTime(new Date(todayActivities[0].checkOutTime));
      }
    }

    // Fetch timeline requests for the current date
    dispatch(TimelineActions.getTimelineRequestByDate({
      id: profile._id,
      date: new Date()
    }));

    // Set up interval to refresh activity data
    const interval = setInterval(() => {
      if (chartController) {
        // Fetch latest user activity data
        dispatch(ActivityActions.getUserActivity({
          id: profile._id,
        }));

        // Update chart times based on latest data
        if (todayActivities.length > 0) {
          if (Object.keys(todayActivities[0]).includes("checkOutTime")) {
            // User has checked out, stop chart updates
            setChartUpdate(false);
            setEndTime(new Date(todayActivities[0].checkOutTime));
          } else {
            // User is still active, update current time
            setCurrentTime(new Date());
          }
        }
      }
    }, 30000); // Update every 30 seconds

    // Clean up interval on component unmount
    return () => clearInterval(interval);
  }, [todayActivities, profile._id, dispatch, chartController]);

 
  const getSlotColor = (hour, minute) => {
    // Create a date object for the specific time slot
    const slotTime = new Date(new Date().setHours(hour, minute, 0));

    if (todayActivities[0]?.breaksHistory.length > 0) {
      for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {
        if (!("breakEndedTime" in todayActivities[0].breaksHistory[i])) {

          // this is for idel activity checking
          if (todayActivities[0]?.idelHistory.length > 0) {
            for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {
              if (!("idelEndedTime" in todayActivities[0].idelHistory[i])) {
                if (
                  slotTime >=
                    new Date(
                      todayActivities[0].idelHistory[i].idelStartedTime
                    ) &&
                  slotTime <= currentTime
                ) {
                  return "yellow";
                } else {
                  if (
                    slotTime >= startTime &&
                    startTime !== null &&
                    slotTime <= currentTime
                  ) {
                    return "#32CD32";
                  } else {
                    return "lightgrey";
                  }
                }
              }

            
              else {

               

                if (
                  slotTime >=
                    new Date(
                      todayActivities[0].idelHistory[i].idelStartedTime
                    ) &&
                  slotTime <=
                    new Date(todayActivities[0].idelHistory[i].idelEndedTime)
                ) {
                  return "yellow";
                }
              }
            }
          }

          // this is for timeline request checking
          if (todayActivities[0]?.timelineRequestHistory.length > 0) {
            for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {
              if (!("endTimeline" in todayActivities[0].timelineRequestHistory[i])) {
                if (
                  slotTime >=
                    new Date(
                      todayActivities[0].timelineRequestHistory[i].startTimeline
                    ) &&
                  slotTime <= currentTime
                ) {
                  return "blue";
                } else {
                  if (
                    slotTime >= startTime &&
                    startTime !== null &&
                    slotTime <= currentTime
                  ) {
                    return "#32CD32";
                  } else {
                    return "lightgrey";
                  }
                }
              } else {
                if (
                  slotTime >=
                    new Date(
                      todayActivities[0].timelineRequestHistory[i].startTimeline
                    ) &&
                  slotTime <=
                    new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)
                ) {
                  return "blue";
                }
              }
            }
          }

          // this is for break activity checking
          if (
            slotTime >=
              new Date(todayActivities[0].breaksHistory[i].breakStartedTime) &&
            slotTime <= currentTime
          ) {
            return "red";
          } else {
            if (
              slotTime >= startTime &&
              startTime !== null &&
              slotTime <= currentTime
            ) {
              return "#32CD32";
            } else {
              return "lightgrey";
            }
          }
        } else {

          if(timeline.length > 0) {
            for(let i=0; i< timeline.length; i++) {

              if(slotTime >= new Date(timeline[i].fromTime) && slotTime <= new Date(timeline[i].toTime) && timeline[i].updatedByAdmin === false){
                  return "#87CEEB"
              }

            }
          }

          if (
            slotTime >=
              new Date(todayActivities[0].breaksHistory[i].breakStartedTime) &&
            slotTime <=
              new Date(todayActivities[0].breaksHistory[i].breakEndedTime)
          ) {
            return "red";
          }
        }
      }
    }

    if (todayActivities[0]?.idelHistory.length > 0) {
      for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {
        if (!("idelEndedTime" in todayActivities[0].idelHistory[i])) {

          if (todayActivities[0]?.timelineRequestHistory.length > 0) {
            for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {

                if (
                  slotTime >=
                    new Date(
                      todayActivities[0].timelineRequestHistory[i].startTimeline
                    ) &&
                  slotTime <=
                    new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)
                ) {
                  return "blue";
                }
              }
            }


          if (
            slotTime >=
              new Date(todayActivities[0].idelHistory[i].idelStartedTime) &&
            slotTime <= currentTime
          ) {
            return "yellow";
          } else {
            if (
              slotTime >= startTime &&
              startTime !== null &&
              slotTime <= currentTime
            ) {
              return "#32CD32";
            } else {
              return "lightgrey";
            }
          }
        } else {

            if(timeline.length > 0) {
              for(let i=0; i< timeline.length; i++) {

                if(slotTime >= new Date(timeline[i].fromTime) && slotTime <= new Date(timeline[i].toTime) && timeline[i].updatedByAdmin === false){
                    return "#87CEEB"
                }

              }
            }
          if (
            slotTime >=
              new Date(todayActivities[0].idelHistory[i].idelStartedTime) &&
            slotTime <=
              new Date(todayActivities[0].idelHistory[i].idelEndedTime)
          ) {
            return "yellow";
          }
        }

    }

     // this is for timeline request checking
     if (todayActivities[0]?.timelineRequestHistory.length > 0) {
      for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {
        if (!("endTimeline" in todayActivities[0].timelineRequestHistory[i])) {
          if (
            slotTime >=
              new Date(
                todayActivities[0].timelineRequestHistory[i].startTimeline
              ) &&
            slotTime <= currentTime
          ) {
            return "blue";
          } else {
            if (
              slotTime >= startTime &&
              startTime !== null &&
              slotTime <= currentTime
            ) {
              return "#32CD32";
            } else {
              return "lightgrey";
            }
          }
        } else {
          console.log("MUST MUST")
          if (
            slotTime >=
              new Date(
                todayActivities[0].timelineRequestHistory[i].startTimeline
              ) &&
            slotTime <=
              new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)
          ) {
            return "blue";
          }
        }
      }
    }
    }

    if (todayActivities[0]?.timelineRequestHistory.length > 0) {
      for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {
        if (!("endTimeline" in todayActivities[0].timelineRequestHistory[i])) {
          if (
            slotTime >=
              new Date(
                todayActivities[0].timelineRequestHistory[i].startTimeline
              ) &&
            slotTime <= currentTime
          ) {
            return "blue";
          } else {
            if (
              slotTime >= startTime &&
              startTime !== null &&
              slotTime <= currentTime
            ) {
              return "#32CD32";
            } else {
              return "lightgrey";
            }
          }
        } else {

          if (
            slotTime >=
              new Date(
                todayActivities[0].timelineRequestHistory[i].startTimeline
              ) &&
            slotTime <=
              new Date(todayActivities[0].timelineRequestHistory[i].endTimeline)
          ) {
            return "blue";
          }
        }
      }
    }

    if (
      slotTime >= startTime &&
      startTime !== null &&
      slotTime <= currentTime &&
      endTime === null
    ) {
      // console.log("Cherry1")
      return "#32CD32";
    } else if (slotTime >= startTime && slotTime <= endTime) {
      // console.log("Cherry2")
      return "#32CD32";
    } else {
      return "lightgrey";
    }
  };


  const getVerticalSlotColour = (hour, minute) => {
    // Return default color if time boundaries aren't set
    if (!startTime || !currentTime) {
      return { color: "lightgrey", tooltip: "" };
    }

    // Return default color if outside working hours
    if (hour < startTime.getHours() || hour > currentTime.getHours()) {
      return { color: "lightgrey", tooltip: "" };
    }

    // Check productivity history if available
    if (todayActivities && todayActivities[0]?.productivityHistory.length > 0) {
      for (let i = 0; i < todayActivities[0].productivityHistory.length; i++) {
        let activity = todayActivities[0].productivityHistory[i];
        let [startHour, endHour] = activity.slotHours.split("-").map(Number);

        // Only process slots in the current hour
        if (hour === startHour) {
          // Format time values for tooltip
          let startTimeFormatted = `${hour}:${minute.toString().padStart(2, "0")}`;
          let endMinute = minute + activity.productivityFilled;
          let endTimeFormatted = `${hour}:${endMinute.toString().padStart(2, "0")}`;

          // Determine color based on productivity metrics
          if (minute <= activity.productivityFilled) {
            // Dark green for productive work time
            return {
              color: "#32CD32",
              tooltip: `Work: ${activity.productivityFilled}m`,
            };
          } else if (minute <= activity.totalSlotFilled) {
            // Light green for time at work (but not productive)
            return {
              color: "#90EE90",
              tooltip: `Time at work: ${activity.totalSlotFilled}m`,
            };
          } else {
            // Grey for inactive time
            return { color: "lightgrey", tooltip: "" };
          }
        }
      }

      // Default color if no matching activity found
      return { color: "lightgrey", tooltip: "" };
    } else {
      // Default color if no productivity history
      return { color: "lightgrey", tooltip: "" };
    }
  };

  const normalizeRGB = (rgb) => {
    const result = rgb.match(/\d+/g);
    return result ? `rgb(${result[0]},${result[1]},${result[2]})` : rgb;
  };

  const dateFormat = (startTime, endTime) => {
    const startTimeStr = new Date(startTime);
    const endTimeStr = new Date(endTime);

    // Calculate duration in minutes
    let result = (endTimeStr - startTimeStr) / 60000;

    // Format as minutes or hours and minutes
    return result < 60 ? `${Math.floor(result)}m` : `${Math.floor(result / 60)}h ${Math.floor(result % 60)}m`;
  };

  const handleMouseEnter = (event, hour, minute) => {
    // Get the background color of the hovered element
    const divColor = getComputedStyle(event.currentTarget).backgroundColor;

    switch (normalizeRGB(divColor)) {
      case "rgb(255,255,0)": { // Yellow - Idle time
        setToolTipController(true);
        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));
        let idleFound = false;

        // Search for matching idle period
        for (let i = 0; i < todayActivities[0].idelHistory.length; i++) {
          const start = new Date(todayActivities[0].idelHistory[i].idelStartedTime);
          const end = new Date(todayActivities[0].idelHistory[i].idelEndedTime);

          if (start <= activityDate && end >= activityDate) {
            // Format time duration
            const str = dateFormat(start, end);
            setTooltipTitle(`Idle ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);

            idleFound = true;
            break;
          }
        }

        if (!idleFound) {
          setTooltipTitle("Idle");
        }
        break;
      }
      case "rgb(50,205,50)": { // Green - Active work time
        setToolTipController(true);
        const activityDate = new Date(
          new Date().setHours(hour, minute-1, 0, 0)
        );
        let workFound = false;

        // Search for matching work period
        for (let i=0; i<todayActivities[0].productivityHoverHistory.length; i++) {
          const start = new Date(
            todayActivities[0].productivityHoverHistory[i].startWorkTime
          );

          // Use end time if available, otherwise use current time
          let end = todayActivities[0].productivityHoverHistory[i].endWorkTime ? new Date(todayActivities[0].productivityHoverHistory[i].endWorkTime) : new Date();

          if (start <= activityDate && end >= activityDate) {
            // Format time duration
            const str = dateFormat(start, end);
            setTooltipTitle(`Work ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);
            workFound = true;
            break;
          }
        }

        if (!workFound) {
          setTooltipTitle("Work");
        }
        break;
      }
      case "rgb(255,0,0)": { // Red - Break time
        setToolTipController(true);
        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));
        let breakFound = false;

        // Search for matching break period
        for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {
          const start = new Date(todayActivities[0].breaksHistory[i].breakStartedTime);

          // Use end time if available, otherwise use current time
          let end = todayActivities[0].breaksHistory[i].breakEndedTime ? new Date(todayActivities[0].breaksHistory[i].breakEndedTime) : new Date();

          if (start <= activityDate && end >= activityDate) {
            // Format time duration
            const str = dateFormat(start, end);
            setTooltipTitle(`Break ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}`);

            breakFound = true;
            break;
          }
        }

        if (!breakFound) {
          setTooltipTitle("Break");
        }
        break;
      }

      case "rgb(0,0,255)": { // Blue - Timeline request
        setToolTipController(true);
        const activityDate = new Date(new Date().setHours(hour, minute-1, 0, 0));
        let timelineFound = false;
        let status = "Approved";

        // Search for matching timeline request
        for (let i = 0; i < todayActivities[0].timelineRequestHistory.length; i++) {
          const start = new Date(todayActivities[0].timelineRequestHistory[i].startTimeline);

          // Use end time if available, otherwise use current time
          let end = todayActivities[0].timelineRequestHistory[i].endTimeline ? new Date(todayActivities[0].timelineRequestHistory[i].endTimeline) : new Date();

          if (start <= activityDate && end >= activityDate) {
            // Format time duration
            const str = dateFormat(start, end);
            setTooltipTitle(`Request: ${str} ${start.getHours()}:${start.getMinutes()}:${start.getSeconds()} - ${end.getHours()}:${end.getMinutes()}:${end.getSeconds()}\nStatus:${status}`);

            timelineFound = true;
            break;
          }
        }

        if (!timelineFound) {
          setTooltipTitle("Request");
        }
        break;
      }

      default: {
        // Hide tooltip for other colors
        setToolTipController(false);
        break;
      }
    }
  };

  const handleMouseClick = (event, hour, minute) => {
    const divColor = getComputedStyle(event.currentTarget).backgroundColor;

    switch (normalizeRGB(divColor)) {
      case "rgb(255,255,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
          for (let i = 0; i < todayActivities[0]?.idelHistory.length; i++) {
            // console.log("Add request set to true ",todayActivities[0].idelHistory[i]);
            const start = new Date(todayActivities[0]?.idelHistory[i].idelStartedTime);
            const end = new Date(todayActivities[0]?.idelHistory[i].idelEndedTime);

            if (start <= activityDate && end >= activityDate) {
              console.log("HOUR HOUR ",start.getHours())
              setFromRequest("Idel")
              setStartTimeline(start)
              setEndTimeline(end)
              setAddRequest(true);
              break;
            }
          }

        break;
      }

      case "rgb(255,0,0)": {
        const activityDate = new Date(new Date().setHours(hour, minute - 1, 0, 0));
        for (let i = 0; i < todayActivities[0].breaksHistory.length; i++) {
          const start = new Date(todayActivities[0].breaksHistory[i].breakStartedTime);
          let end = null;

          if (todayActivities[0].breaksHistory[i].breakEndedTime) {
            end = new Date(todayActivities[0].breaksHistory[i].breakEndedTime);
          } else {
            end = new Date();
          }

          if (start <= activityDate && end >= activityDate) {
            setFromRequest("Private")
            console.log(" Start Break ",todayActivities[0].breaksHistory[i].breakStartedTime)
            console.log(" End Break ",todayActivities[0].breaksHistory[i].breakEndedTime)
            setStartTimeline(start)
            setEndTimeline(end)
            setAddRequest(true);
            break;
          }
        }
        break;
      }

      default:  setAddRequest(false);
        break;
    }

  }


// Function to format time labels (12-hour format)
const formatTimeLabel = (hour) => {
  if (hour === 0) { return "12AM"; }
  if (hour < 12) { return `${hour}AM`; }
  if (hour === 12) { return "12PM"; }
  return `${hour - 12}PM`;
};

// Function to render progress bars
const renderProgressBars = () => {
  const progressBars = [];
  let currentActivity = null;
  let currentActivityStart = 0;
  let currentActivityWidth = 0;

  hours.forEach((hour, hourIndex) => {
    minArr.forEach((minute) => {
      const activity = getSlotColor(hourIndex, minute);
      if (activity !== currentActivity) {
        if (currentActivity !== null) {
          // Render previous activity block
          progressBars.push(
            <Box
              key={`${hourIndex}-${minute}`}
              sx={{
                width: `${currentActivityWidth}%`,
                backgroundColor: currentActivity,
                height: "100%",
                display: "inline-block",
              }}
              onMouseEnter={(event) => handleMouseEnter(event, hourIndex, minute)}
              onClick={(event) => handleMouseClick(event, hourIndex, minute)}
            >
              {toolTipController && (
                <Tooltip title={toolTipTitle} arrow>
                  <Box sx={{ padding: "10px", display: "inline-block" }} />
                </Tooltip>
              )}
            </Box>
          );
        }
        // Start a new activity block
        currentActivity = activity;
        currentActivityStart = minute;
        currentActivityWidth = 1.04;
      } else {
        // Accumulate width for the same activity
        currentActivityWidth += 1.04;
      }
    });
  });

  // Render the last accumulated div
  if (currentActivity !== null) {
    progressBars.push(
      <Box
        key={`last-${currentActivityStart}`}
        sx={{
          width: `${currentActivityWidth}%`,
          backgroundColor: currentActivity,
          height: "100%",
          display: "inline-block",
        }}
        onMouseEnter={(event) => handleMouseEnter(event, hours.length - 1, minArr.length - 1)}
      >
        {toolTipController && (
          <Tooltip title={toolTipTitle} arrow>
            <Box sx={{ padding: "10px", display: "inline-block" }} />
          </Tooltip>
        )}
      </Box>
    );
  }

  return progressBars;
};

return (
  <>
    {/* Timeline Request Modal - Conditionally Rendered */}
    {addRequest && (
      <TimelineRequest
        startTime={startTimeline}
        endTime={endTimeline}
        addRequest={addRequest}
        setAddRequest={handleClose}
        fromRequest={fromRequest}
      />
    )}

    {/* Title */}
    <Typography variant="h5" align="center" gutterBottom sx={{ padding: "10px", margin: "10px" }}>
      User Activity Progress
    </Typography>

    {/* Timeline Bar Representation */}
   {/* Timeline Bar Representation */}
<Grid container spacing={0.5} justifyContent="center" sx={{ width: "100%", margin: "0 auto" }}>
  {hours.map((hour, index) => (
    <Grid
      item
      key={index}
      sx={{
        flex: "1 1 auto", // Ensures equal distribution
        textAlign: "center",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Paper
        sx={{
          height: 60,
          display: "flex",
          flexDirection: "column",
          justifyContent: "flex-end", // Ensures slots stack properly
          overflow: "hidden", // Prevents unnecessary expansion
          width: "100%", // Ensures full column width
        }}
        elevation={2}
      >
        {minArrRev.map((minute) => {
          const { color, tooltip } = getVerticalSlotColour(index, minute);
          return (
            <Tooltip key={minute} title={tooltip} arrow>
              <Box
                sx={{
                  width: "100%",
                  height: "1px",
                  backgroundColor: color || "#ddd",
                  minHeight: "1px", // Ensures it stays visible
                }}
              />
            </Tooltip>
          );
        })}
      </Paper>
    </Grid>
  ))}
</Grid>


    {/* Progress Bar */}
    <Box sx={{ marginBottom: "10px", display: "flex", justifyContent: "center", width: "100%" }}>
      <Paper sx={{ height: 10, overflow: "hidden", display: "flex", width: "100%" }}>
        {renderProgressBars()}
      </Paper>
    </Box>

    {/* Time Labels - Centered in Column */}
    <Grid container justifyContent="center" sx={{ marginTop: 1, width: "100%" }}>
      {hours.map((hour, i) => (
        <Grid
          item
          key={i}
          sx={{
            flex: "1 1 auto", // Ensures equal width distribution
            textAlign: "center",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Typography variant="caption">{formatTimeLabel(i)}</Typography>
        </Grid>
      ))}
    </Grid>
  </>
);



}

ProductivityChart.propTypes = {
  todayActivities: PropTypes.array,
};


export default ProductivityChart;
