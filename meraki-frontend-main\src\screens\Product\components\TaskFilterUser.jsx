import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Grid, FormControl, Select, MenuItem, Button } from "@mui/material";
import FilterAltIcon from "@mui/icons-material/FilterAlt";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { useDispatch, useSelector } from "react-redux";
import { ProductSelector, UserSelector } from "selectors";
import { UserActions } from "slices/actions";

const TaskFilterUser = ({ onFilter, projects }) => {
  const [filters, setFilters] = useState({
    project: "",
    user: "",
    status: "",
    type: "",
    tag: "",
  });

  const statuses = ["Ongoing", "Completed", "On Hold"];
  const priorities = ["Low", "Medium", "High", "Critical"];
  const taskTypes = ["Bug", "Feature", "Task"];

  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers()) || [];
  const products = useSelector(ProductSelector.getProducts()) || [];

  const getUsernameById = (userId) => {
    const user = users.find((u) => u._id === userId);
    return user ? user.name : "Unknown User";
  };

  useEffect(() => {
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  const handleChange = (event) => {
    const { name, value } = event.target;
    setFilters((prevFilters) => ({ ...prevFilters, [name]: value }));
  };

  const handleFilter = () => {
    onFilter(filters);
  };

  const handleReset = () => {
    setFilters({
      project: "",
      user: "",
      status: "",
      type: "",
      tag: "",
    });
    onFilter({
      project: "",
      user: "",
      status: "",
      type: "",
      tag: "",
    });
  };

  return (
    <Grid
    container
    spacing={2}
    alignItems="center"
    justifyContent="center"  // Centers content horizontally
    sx={{
      p: 2,
      mt: 2,
      mb: 2,
      background: "#fff",
      borderRadius: 2,
      display: "flex",         // Enables flexbox
      flexDirection: "row",    // Ensures horizontal layout
      justifyContent: "space-between",// Centers horizontally
      alignItems: "center",    // Centers vertically (if needed)
    }}
  >
      {/* Project Filter */}
      <Grid item xs={12} sm={1.5}>
        <FormControl fullWidth size="small" sx={{ background: "#EAEAEA", borderRadius: 1 }}>
          <Select
            name="project"
            value={filters.project}
            onChange={handleChange}
            displayEmpty
            renderValue={(selected) => {
              const project = projects.find((p) => p._id === selected);
              return selected ? project?.productName || "Unknown" : "Project";
            }}
            
          >
            <MenuItem value="">Project</MenuItem>
            {(projects ?? []).map((project) => (
  <MenuItem key={project._id} value={project._id}>
    {project.productName}
  </MenuItem>
))}

          </Select>
        </FormControl>
      </Grid>

      {/* User Filter */}
      <Grid item xs={12} sm={1.5}>
        <FormControl fullWidth size="small" sx={{ background: "#EAEAEA", borderRadius: 1 }}>
          <Select
            name="user"
            value={filters.user}
            onChange={handleChange}
            displayEmpty
            renderValue={(selected) => (selected ? getUsernameById(selected) : "User")}
          >
            <MenuItem value="">User</MenuItem>
            {Array.from(new Set(projects.flatMap((p) => p.members))).map((userId) => (
              <MenuItem key={userId} value={userId}>
                {getUsernameById(userId)}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      {/* Status Filter */}
      <Grid item xs={12} sm={1.5}>
        <FormControl fullWidth size="small" sx={{ background: "#EAEAEA", borderRadius: 1 }}>
          <Select name="status" value={filters.status} onChange={handleChange} displayEmpty>
            <MenuItem value="">Status</MenuItem>
            {statuses.map((status, index) => (
              <MenuItem key={index} value={status}>
                {status}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      {/* Task Type Filter */}
      <Grid item xs={12} sm={1.5}>
        <FormControl fullWidth size="small" sx={{ background: "#EAEAEA", borderRadius: 1 }}>
          <Select name="type" value={filters.type} onChange={handleChange} displayEmpty>
            <MenuItem value="">Type</MenuItem>
            {taskTypes.map((type, index) => (
              <MenuItem key={index} value={type}>
                {type}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      {/* Task Tag Filter */}
      <Grid item xs={12} sm={1.5}>
        <FormControl fullWidth size="small" sx={{ background: "#EAEAEA", borderRadius: 1 }}>
          <Select name="tag" value={filters.tag} onChange={handleChange} displayEmpty>
            <MenuItem value="">Tag</MenuItem>
            {priorities.map((priority, index) => (
              <MenuItem key={index} value={priority}>
                {priority}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>

      {/* Filter Button */}
      <Grid item xs={12} sm={1.75}>
        <Button
          variant="contained"
          fullWidth
          startIcon={<FilterAltIcon />}
          sx={{
            backgroundColor: "#6200ea",
            color: "white",
            borderRadius: 2,
            textTransform: "none",
            boxShadow: 2,
            "&:hover": { backgroundColor: "#4b00c0" },
          }}
          onClick={handleFilter}
        >
          Filter
        </Button>
      </Grid>

      {/* Reset Button */}
      <Grid item xs={12} sm={1.5}>
        <Button
          variant="contained"
          fullWidth
          startIcon={<RestartAltIcon />}
          sx={{
            backgroundColor: "#d32f2f",
            color: "white",
            borderRadius: 2,
            textTransform: "none",
            boxShadow: 2,
            "&:hover": { backgroundColor: "#b71c1c" },
          }}
          onClick={handleReset}
        >
          Reset
        </Button>
      </Grid>
    </Grid>
  );
};

TaskFilterUser.propTypes = {
  onFilter: PropTypes.func.isRequired,
  projects: PropTypes.array,
};

TaskFilterUser.defaultProps = {
  projects: [], // Ensures it's never undefined
};


export default TaskFilterUser;
