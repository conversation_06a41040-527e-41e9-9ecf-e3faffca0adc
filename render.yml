services:
  - type: web
    name: meraki-frontend
    env: node
    buildCommand: cd meraki-frontend-main && npm install --force && npm install eslint-loader --save-dev --force && npm run build
    startCommand: cd meraki-frontend-main && PORT=3000 HOST=0.0.0.0 npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      - key: REACT_APP_API_URL
        value: http://localhost:10000/api

  - type: web
    name: meraki-backend
    env: node
    buildCommand: cd meraki-backend-main && npm install
    startCommand: cd meraki-backend-main && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: JWT_SECRET
        value: meraki-secret-key
      - key: MONGODB_URI
        value: mongodb+srv://your-mongodb-uri
