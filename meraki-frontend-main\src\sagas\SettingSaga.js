import {all, call, put, takeLatest} from 'redux-saga/effects'
import {GeneralActions, SettingActions} from "../slices/actions";
import {SettingService} from "../services/SettingService";

function *getSetting({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        const result = yield call(SettingService.GetSetting, payload);

        if (result && result._id) {
            yield put(SettingActions.getSettingSuccess(result));
        } else {
            yield put(SettingActions.getSettingSuccess({
                _id: '',
                name: '',
                address: '',
                city: '',
                country: '',
                email: '',
                phone: '',
                leaveLimit: 0,
                day: 0
            }));
        }

        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));

        if (err.status === 401 || err.status === 403 ||
            err.originalError?.response?.status === 401 ||
            err.originalError?.response?.status === 403) {

            yield put(SettingActions.getSettingSuccess({
                _id: '6188db5ef14de5ca1e1937ee',
                name: '',
                address: '',
                city: '',
                country: '',
                email: '',
                phone: '',
                leaveLimit: 0,
                day: 0
            }));

            yield put(GeneralActions.addError({
                action: type,
                message: 'You do not have permission to access settings'
            }));
        } else {
            yield put(GeneralActions.addError({
                action: type,
                message: err.response?.data?.error || err.message || 'Failed to load settings'
            }));
        }
    }
}

function *updateSetting({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));

        const { id, ...params } = payload;

        if (params.country && typeof params.country === 'object') {
            params.country = params.country.name;
        }

        const result = yield call(SettingService.UpdateSetting, id, params);
        const updated = yield call(SettingService.GetSetting);
        yield put(SettingActions.getSettingSuccess(updated));

        yield put(GeneralActions.addSuccess({
            action: type,
            message: result.data.message
        }));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.message || err.message || "Failed to update settings"
        }));
    }
}

function *addCompanyLeave({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        const result = yield call(SettingService.AddCompanyLeave, payload.id, payload);
        yield put(SettingActions.getSettingSuccess(result.data));
        yield put(GeneralActions.addSuccess({
            action: type,
            message: result.data.message
        }));
        yield put(GeneralActions.stopLoading(type))
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

function *deleteAllCompanyLeave({type,payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        const result = yield call(SettingService.DeleteCompanyLeaves, payload.id);
        yield put(SettingActions.getSettingSuccess(result.data));
        yield put(GeneralActions.addSuccess({
            action: type,
            message: result.data.message
        }));
        yield put(GeneralActions.stopLoading(type))
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

export function *SettingWatcher() {
    yield all([
        yield takeLatest(SettingActions.getSetting.type, getSetting),
        yield takeLatest(SettingActions.updateSetting.type, updateSetting),
        yield takeLatest(SettingActions.addCompanyLeave.type, addCompanyLeave),
        yield takeLatest(SettingActions.deleteAllCompanyLeave.type, deleteAllCompanyLeave)
    ]);
}

