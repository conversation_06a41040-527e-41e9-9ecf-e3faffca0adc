/**
 * Redux Reducers Configuration
 *
 * This module combines all individual slice reducers into a single object
 * that will be used to create the Redux store.
 */

// Application Core
import GeneralSlice from "./slice/GeneralSlice";
import AuthSlice from "./slice/AuthSlice";

// User Management
import UserSlice from "./slice/UserSlice";

// Organization Structure
import DepartmentSlice from "./slice/DepartmentSlice";
import DesignationSlice from "./slice/DesignationSlice";

// HR Management
import AttendanceSlice from "./slice/AttendanceSlice";
import ExpensesSlice from "./slice/ExpensesSlice";
import LeaveSlice from "./slice/LeaveSlice";

// Activity Tracking
import ActivitySlice from "./slice/ActivitySlice";
import TimelineSlice from "./slice/TimelineSlice";

// Project Management
import ProductSlice from "./slice/ProductSlice";
import ClientSlice from "./slice/ClientSlice";
import SprintSlice from "./slice/SprintSlice";

// System Configuration
import SettingSlice from "./slice/SettingSlice";

/**
 * Combined reducers object
 * Each property corresponds to a slice of the Redux store state
 */
const reducers = {
    // Application Core
    general: GeneralSlice.reducer,         // Application-wide state (loading, errors, etc.)
    auth: AuthSlice.reducer,               // Authentication state (login, logout, etc.)

    // User Management
    user: UserSlice.reducer,               // User profiles and management

    // Organization Structure
    department: DepartmentSlice.reducer,   // Department management
    designation: DesignationSlice.reducer, // Job designation management

    // HR Management
    attendance: AttendanceSlice.reducer,   // Employee attendance tracking
    expenses: ExpensesSlice.reducer,       // Expense management
    leave: LeaveSlice.reducer,             // Leave management

    // Activity Tracking
    activity: ActivitySlice.reducer,       // Daily activity tracking
    timeline: TimelineSlice.reducer,       // Timeline visualization

    // Project Management
    product: ProductSlice.reducer,         // Product/project management
    client: ClientSlice.reducer,           // Client management
    sprint: SprintSlice.reducer,           // Sprint management

    // System Configuration
    setting: SettingSlice.reducer,         // Application settings
};

export default reducers;