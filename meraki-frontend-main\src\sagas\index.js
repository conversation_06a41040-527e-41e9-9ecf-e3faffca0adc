/**
 * Root Saga Configuration
 *
 * This module combines all individual feature sagas into a single root saga
 * that will be run by the Redux-Saga middleware.
 */

import { all, fork } from 'redux-saga/effects';

// Authentication and User Management
import { AuthWatcher } from "./AuthSaga";
import { UserWatcher } from "./UserSaga";

// Organization Structure
import { DepartmentWatcher } from "./DepartmentSaga";
import { DesignationWatcher } from "./DesignationSaga";

// HR Management
import { AttendanceWatcher } from "./AttendanceSaga";
import { ExpenseWatcher } from "./ExpenseSaga";
import { LeaveWatcher } from "./LeaveSaga";

// Activity Tracking
import { ActivityWatcher } from './ActivitySaga';
import { TimelineWatcher } from './TimelineSaga';

// Project Management
import { ProductWatcher } from './ProductSaga';
import { ClientWatcher } from './ClientSaga';
import { SprintWatcher } from './SprintSaga';

// System Configuration
import { SettingWatcher } from "./SettingSaga";

/**
 * Root saga that combines all feature sagas
 *
 * @generator
 * @yields {Object} Combined sagas effect
 */
export default function *rootSaga() {
    // Fork all watchers to run them in parallel
    yield all([
        // Authentication and User Management
        AuthWatcher,
        UserWatcher,

        // Organization Structure
        DepartmentWatcher,
        DesignationWatcher,

        // HR Management
        AttendanceWatcher,
        ExpenseWatcher,
        LeaveWatcher,

        // Activity Tracking
        ActivityWatcher,
        TimelineWatcher,

        // Project Management
        ProductWatcher,
        ClientWatcher,
        SprintWatcher,

        // System Configuration
        SettingWatcher
    ].map(fork));
}