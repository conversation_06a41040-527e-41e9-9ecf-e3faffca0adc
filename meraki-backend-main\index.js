/**
 * Meraki HR Management System - Backend Server
 *
 * This is the main entry point for the Meraki HRM backend application.
 * It sets up the Express server, connects to MongoDB, initializes default data,
 * and configures all API routes.
 */

// Core dependencies
const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const morgan = require("morgan");
const listEndpoints = require("express-list-endpoints");
const path = require("path")

// Load environment variables
dotenv.config();

// Initialize Express application
const app = express();

// CORS Configuration
// Allow cross-origin requests from the frontend application
app.use(cors({ origin: "*" }));

// Additional CORS headers for more specific control
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  next();
});

// Request body parsers
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Request logging
app.use(morgan(':method :url :status :res[content-length] - :response-time ms'));

app.use("/static",express.static(path.join(__dirname,'/static/screenshot')))

// Database Connection
const db = require("./models");
db.connectDatabase()
  .then(() => console.log("✅ Database connected successfully!"))
  .catch((err) => console.error("❌ Database connection failed:", err.toString()));

// Initialize Default Data
// These functions create default records if the database is empty
const SettingController = require('./controllers/setting.controller');
SettingController.createDefaultSetting();

const DepartmentController = require('./controllers/department.controller');
DepartmentController.createDefaultDepartments();

const DesignationController = require('./controllers/designation.controller');
DesignationController.createDefaultDesignations();

const UserController = require("./controllers/user.controller");
UserController.createDefaultUsers();

// =====================================================================
// Route Configuration
// =====================================================================

// Public routes (no authentication required)
require("./routes/auth.route")(app);

// Authentication middleware for all /api routes
app.use("/api*", require('./middlewares/auth.middleware'));

// User Management Routes
require("./routes/user.route")(app);

// Organization Structure Routes
require("./routes/department.route")(app);
require("./routes/designation.route")(app);

// HR Management Routes
require("./routes/attendance.route")(app);
require("./routes/leave.route")(app);
require("./routes/expenses.route")(app);

// Activity & Timeline Routes
require("./routes/activity.route")(app);
require('./routes/timeline.route')(app);

// Project Management Routes
require('./routes/product.route')(app);
require('./routes/client.route')(app);
require('./routes/screenshot.route')(app)
require('./routes/sprint.route')(app);

// System Configuration Routes
require("./routes/setting.route")(app);
require('./routes/permissions.route')(app);

// Work Schedule Routes
require('./routes/workSchedule.route')(app);

// API Routes Listing Endpoint (for debugging)
app.get("/api/routes", (req, res) => {
  res.json(listEndpoints(app));
});

// Initialize the scheduler service
const scheduler = require('./services/scheduler.service');
// Health check endpoint (no authentication required)
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "UP",
    timestamp: new Date().toISOString(),
    message: "Meraki Backend API is running"
  });
});

// Root endpoint for basic connectivity testing
app.get("/", (req, res) => {
  res.status(200).json({
    name: "Meraki HR Management System API",
    version: "1.0.0",
    status: "Running",
    timestamp: new Date().toISOString()
  });
});

// Start Server
const port = process.env.PORT || 10000;
app.listen(port, () => {
  console.log(`✅ Backend server is running on port ${port}!`);
  console.log(`✅ Health check available at: http://localhost:${port}/health`);

  // Initialize the scheduler after server starts
  scheduler.initScheduler();
  console.log(`✅ Auto-checkout scheduler initialized!`);
});