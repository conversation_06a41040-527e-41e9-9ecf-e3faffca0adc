'use strict';

/**
 * Authentication Controller
 *
 * This controller handles user authentication operations including:
 * - User login with email and password
 * - JWT token generation
 */

const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const AuthService = require("../services/auth.service");

// JWT secret key from environment variables
const secret = process.env.JWT_SECRET;

/**
 * User login handler
 *
 * Authenticates a user with email and password, and returns a JWT token
 * if authentication is successful.
 *
 * @param {Object} req - Express request object with user credentials in body
 * @param {Object} res - Express response object
 * @returns {Object} Response with token or error message
 */
exports.login = async (req, res) => {
    try {
        const { body } = req;

        // Find user by email
        const result = await AuthService.login(body);

        // Check if user exists
        if (!result) {
            return res.status(401).send({
                message: "User not found!"
            });
        }

        // Verify password
        const passwordValid = await bcrypt.compare(
            body.password,
            result.password
        );

        // Return error if password is invalid
        if (!passwordValid) {
            return res.status(401).send({
                message: 'Wrong password!'
            });
        }

        // Generate JWT token
        const token = jwt.sign(
            { id: result._id },
            secret
        );

        // Return success response with token
        return res.status(200).send({
            message: "Successfully logged in.",
            token
        });
    } catch (error) {
        // Handle unexpected errors
        return res.status(500).send({
            message: "An error occurred during login",
            error: error.message
        });
    }
}