import { ProductService } from "services/ProductService";
import { ProductActions, GeneralActions } from "../slices/actions";
import { all, call, put, takeLatest } from 'redux-saga/effects';

function *createProduct({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Create Product ", payload);

        // Extract filter from payload
        const filter = payload.filter || {};
        console.warn("Using filter for product creation:", filter);

        // Create the product
        const result = yield call(ProductService.createProduct, payload);

        // Get the response status to check if creation was successful
        if (result.status !== 200) {
            const errorData = yield result.json();
            throw new Error(errorData.error || "Failed to create product");
        }

        // Fetch products with the same filter that was used in the request
        const resultHis = yield call(ProductService.getProducts, filter);
        console.warn("Create Product data ", resultHis);

        yield put(ProductActions.getSuccessfullyProducts(resultHis));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in createProduct saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in createProduct"
        }));
    }
}

function *createTaskByAdmin({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Create Task By Admin ", payload);
        const result = yield call(ProductService.createProductsTask, payload.id, payload);
        const resultHis = yield call(ProductService.getProducts);
        // No need to call .json() as getProducts already returns parsed data
        console.warn("Create Task data By Admin ", resultHis);
        yield put(ProductActions.getSuccessfullyProducts(resultHis));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in createTaskByAdmin saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in createTaskByAdmin"
        }));
    }
}

function *updateProduct({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Update Product ", payload.id, payload);
        const result1 = yield call(ProductService.updateProduct, payload.id, payload);
        // For updateProduct, we still need to call .json() as it returns a Response object
        const data1 = yield result1.json();
        console.log("Updated Product Result ", data1);
        const result = yield call(ProductService.getProducts);
        // No need to call .json() as getProducts already returns parsed data
        console.log("Get Products Result ", result);
        yield put(ProductActions.getSuccessfullyProducts(result));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in updateProduct saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in updateProduct"
        }));
    }
}

function *deleteProduct({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Delete Product payload ", payload);
        const result = yield call(ProductService.deleteProduct, payload);
        // For deleteProduct, we still need to call .json() as it returns a Response object
        const data = yield result.json();
        console.warn("After Delete, payload.user: ", payload.user);
        const result1 = yield call(ProductService.getProducts);
        // No need to call .json() as getProducts already returns parsed data
        console.warn("Get Products after delete: ", result1);
        yield put(ProductActions.getSuccessfullyProducts(result1));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in deleteProduct saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in deleteProduct"
        }));
    }
}

function *getProducts({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Get Products payload ", payload);

        // Ensure we're passing the filter correctly
        const filter = payload || {};
        console.warn("Using filter for getProducts:", filter);

        // Call the service with the filter
        const result = yield call(ProductService.getProducts, filter);
        console.warn("Get Products result:", result);

        yield put(ProductActions.getSuccessfullyProducts(result));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in getProducts saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in getProducts"
        }));
    }
}

function *createProductsTaskByUser({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.log("Task By User with payload:", payload);
        
        // Make sure sprintId is properly passed
        if (payload.sprintId) {
            console.log("Sprint ID is present:", payload.sprintId);
            console.log("addToSprint flag:", payload.addToSprint);
        }
        
        // Fix: Use createProductsTask instead of createTask
        const result = yield call(ProductService.createProductsTask, payload.id, payload);
        console.log("Task creation result:", result);
        
        const resultHis = yield call(ProductService.getProducts);
        console.warn("Products after task creation:", resultHis);
        
        yield put(ProductActions.getSuccessfullyProducts(resultHis));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in createProductsTaskByUser saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in createProductsTaskByUser"
        }));
    }
}

function *getProductsByUser({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Get Products By User, id: ", payload.id);
        const result = yield call(ProductService.getProductsByUser, payload.id);
        // For getProductsByUser, we still need to call .json() as it returns a Response object
        const data = yield result.json();
        console.log("Get Products By User Result ", data);
        yield put(ProductActions.getSuccessfullyProducts(data));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in getProductsByUser saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in getProductsByUser"
        }));
    }
}

function *getProductById({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Get Product By Id, payload: ", payload);
        const result = yield call(ProductService.getProductById, payload.id);
        // For getProductById, we still need to call .json() as it returns a Response object
        const data = yield result.json();
        console.log("Get Product By Id Result ", data);
        yield put(ProductActions.getSuccessfullyProductById(data));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in getProductById saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in getProductById"
        }));
    }
}
function *updateTask({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));

        console.warn("Updating Task:", payload);

        // Fix parameter naming to match what the service expects
        const productId = payload.productId || payload.productid;
        const taskId = payload.taskId || payload.taskid;
        const body = payload.updatedTask || payload.body;

        if (!productId || !taskId) {
            throw new Error("Missing required fields in payload");
        }

        console.log(`Updating task ${taskId} in product ${productId} with body:`, body);

        // Call the service with the correct parameters
        const result1 = yield call(ProductService.updateTask, productId, taskId, body || {});
        const data1 = result1;
        console.log("Updated Task Result", data1);

        // Refresh product list
        const result = yield call(ProductService.getProducts);
        yield put(ProductActions.getSuccessfullyProducts(result));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in updateTask saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in updateTask"
        }));
    }
}


// New saga for starting a task
function *startTask({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Starting Task ", payload.taskId, payload.projectId);

        // Pass the entire payload to the startTask service
        const result = yield call(ProductService.startTask, payload);
        console.log("Started Task Result ", result);

        // Refresh the product list after starting the task
        const productsResult = yield call(ProductService.getProducts);
        yield put(ProductActions.getSuccessfullyProducts(productsResult));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in startTask saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in startTask"
        }));
    }
}

// New saga for stopping a task
function *stopTask({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Stopping Task ", payload.taskId, payload.projectId);

        // Pass the entire payload to the stopTask service
        const result = yield call(ProductService.stopTask, payload);
        console.log("Stopped Task Result ", result);

        // Refresh the product list after stopping the task
        const productsResult = yield call(ProductService.getProducts);
        yield put(ProductActions.getSuccessfullyProducts(productsResult));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in stopTask saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in stopTask"
        }));
    }
}

// New saga for pausing a task
function *pauseTask({ type, payload }) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));
        console.warn("Pausing Task ", payload.taskId, payload.projectId);
        console.log("Pause payload:", payload);

        // Pass the entire payload to the pauseTask service
        const result = yield call(ProductService.pauseTask, payload);
        console.log("Paused Task Result ", result);

        // Refresh the product list after pausing the task
        const productsResult = yield call(ProductService.getProducts);
        yield put(ProductActions.getSuccessfullyProducts(productsResult));
        yield put(GeneralActions.stopLoading(type));
    } catch (err) {
        console.error("Error in pauseTask saga:", err);
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error || err.message || "Unknown error in pauseTask"
        }));
    }
}

export function *ProductWatcher() {
    yield all([
        yield takeLatest(ProductActions.createProduct.type, createProduct),
        yield takeLatest(ProductActions.getProducts.type, getProducts),
        yield takeLatest(ProductActions.updateProduct.type, updateProduct),
        yield takeLatest(ProductActions.deleteProduct.type, deleteProduct),
        yield takeLatest(ProductActions.createTaskByAdmin.type, createTaskByAdmin),
        yield takeLatest(ProductActions.createProductsTaskByUser.type, createProductsTaskByUser),
        yield takeLatest(ProductActions.getProductById.type, getProductById),
        yield takeLatest(ProductActions.getProductsByUser.type, getProductsByUser),
        yield takeLatest(ProductActions.updateTask.type, updateTask),
        yield takeLatest(ProductActions.startTask.type, startTask), // Start task watcher
        yield takeLatest(ProductActions.stopTask.type, stopTask),   // Stop task watcher
        yield takeLatest(ProductActions.pauseTask.type, pauseTask)  // Pause task watcher
    ]);
}