/**
 * Middleware to check if a user has permission to manage permissions
 * This is a special case where we need to ensure only users with the right
 * permissions can manage other users' permissions
 */
module.exports = (req, res, next) => {
  const user = req.user;

  if (!user) {
    return res.status(401).json({ message: "Authentication required." });
  }

  // Always allow admin users to manage permissions
  if (user.role && user.role.includes('admin')) {
    console.log('Admin user detected, granting access');
    return next();
  }

  // Check if user has permission to manage permissions
  const hasPermission = user.permissions &&
    user.permissions.some(p =>
      p.feat === 'User' &&
      p.acts &&
      (p.acts.includes('update') || p.acts.includes('*'))
    );

  if (!hasPermission) {
    return res.status(403).json({
      message: "Access denied. You need 'update' permission for 'User' to manage permissions."
    });
  }

  next();
};
