'use strict';

const User = require('../models/user.model');

// GET /api/permissions/:userId
exports.getUserPermissions = async (req, res) => {
  try {
    const { userId } = req.params;

    const user = await User.findById(userId).lean(); // lean() improves performance
    if (!user) {
      return res.status(404).json({ message: 'User not found.' });
    }

    return res.status(200).json(user.permissions || []);
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return res.status(500).json({ message: 'Failed to fetch permissions.' });
  }
};

// PUT /api/permissions/:userId
exports.updateUserPermissions = async (req, res) => {
  try {
    const { userId } = req.params;
    const { permissions } = req.body;

    if (!Array.isArray(permissions)) {
      return res.status(400).json({ message: 'Invalid permissions format.' });
    }

    const user = await User.findByIdAndUpdate(
      userId,
      { permissions },
      { new: true }
    );

    if (!user) {
      return res.status(404).json({ message: 'User not found.' });
    }

    return res.status(200).json({
      message: 'Permissions updated successfully.',
      permissions: user.permissions,
    });
  } catch (error) {
    console.error('Error updating user permissions:', error);
    return res.status(500).json({ message: 'Failed to update permissions.' });
  }
};
