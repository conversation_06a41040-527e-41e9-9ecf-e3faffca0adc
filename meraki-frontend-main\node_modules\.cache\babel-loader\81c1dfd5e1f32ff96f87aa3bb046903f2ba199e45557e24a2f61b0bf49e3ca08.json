{"ast": null, "code": "import dayjs from 'dayjs';\nimport { DEFAULT_WORK_SCHEDULE } from 'constants/workSchedule';\n\n/**\n * Work Schedule Utilities\n * Handles multiple schedule types with priority-based resolution\n */\nexport class WorkScheduleUtils {\n  /**\n   * Get effective work schedule for a user at a specific date/time\n   * Priority: Time-Specific > Daily > Weekly > Default\n   */\n  static getEffectiveSchedule(user, targetDate, targetTime = null) {\n    const schedules = user.workSchedules || [];\n    const defaultSchedule = user.workSchedule || DEFAULT_WORK_SCHEDULE;\n\n    // Convert target date to dayjs for comparison\n    const target = dayjs(targetDate);\n\n    // Find applicable schedules\n    const applicableSchedules = schedules.filter(schedule => {\n      return this.isScheduleApplicable(schedule, target, targetTime);\n    });\n\n    // Sort by priority (highest first)\n    applicableSchedules.sort((a, b) => (b.priority || 1) - (a.priority || 1));\n\n    // Return highest priority schedule or default\n    return applicableSchedules.length > 0 ? applicableSchedules[0] : defaultSchedule;\n  }\n\n  /**\n   * Check if a schedule is applicable for the given date/time\n   */\n  static isScheduleApplicable(schedule, targetDate, targetTime) {\n    const scheduleStart = dayjs(schedule.effectiveFrom);\n    const scheduleEnd = dayjs(schedule.effectiveTo);\n\n    // Check date range\n    if (targetDate.isBefore(scheduleStart) || targetDate.isAfter(scheduleEnd)) {\n      return false;\n    }\n\n    // Check schedule type specific conditions\n    switch (schedule.type) {\n      case 'time_specific':\n        return this.isTimeSpecificApplicable(schedule, targetDate, targetTime);\n      case 'daily':\n        return this.isDailyApplicable(schedule, targetDate);\n      case 'weekly':\n        return this.isWeeklyApplicable(schedule, targetDate);\n      default:\n        return true;\n    }\n  }\n\n  /**\n   * Check if time-specific schedule applies\n   */\n  static isTimeSpecificApplicable(schedule, targetDate, targetTime) {\n    if (!targetTime) return false;\n    const scheduleDate = dayjs(schedule.specificDate);\n    if (!targetDate.isSame(scheduleDate, 'day')) return false;\n    const timeStart = this.parseTime(schedule.startTime);\n    const timeEnd = this.parseTime(schedule.endTime);\n    const target = this.parseTime(targetTime);\n    return target >= timeStart && target <= timeEnd;\n  }\n\n  /**\n   * Check if daily schedule applies\n   */\n  static isDailyApplicable(schedule, targetDate) {\n    if (schedule.specificDate) {\n      return targetDate.isSame(dayjs(schedule.specificDate), 'day');\n    }\n    return true;\n  }\n\n  /**\n   * Check if weekly schedule applies\n   */\n  static isWeeklyApplicable(schedule, targetDate) {\n    if (schedule.daysOfWeek && schedule.daysOfWeek.length > 0) {\n      const dayOfWeek = targetDate.day(); // 0 = Sunday, 1 = Monday, etc.\n      return schedule.daysOfWeek.includes(dayOfWeek);\n    }\n    return true;\n  }\n\n  /**\n   * Parse time string to minutes for comparison\n   */\n  static parseTime(timeString) {\n    const [hours, minutes] = timeString.split(':').map(Number);\n    return hours * 60 + minutes;\n  }\n\n  /**\n   * Validate check-in/check-out against schedule\n   */\n  static validateCheckInOut(user, checkTime, action = 'checkin') {\n    const checkDate = dayjs(checkTime);\n    const timeString = checkDate.format('HH:mm');\n    const effectiveSchedule = this.getEffectiveSchedule(user, checkDate.format('YYYY-MM-DD'), timeString);\n    const scheduleStart = this.parseTime(effectiveSchedule.startTime);\n    const scheduleEnd = this.parseTime(effectiveSchedule.endTime);\n    const checkTimeMinutes = this.parseTime(timeString);\n    const result = {\n      isValid: false,\n      schedule: effectiveSchedule,\n      message: '',\n      allowedRange: `${effectiveSchedule.startTime} - ${effectiveSchedule.endTime}`,\n      scheduleType: effectiveSchedule.type || 'default'\n    };\n    if (action === 'checkin') {\n      // Allow check-in 15 minutes before start time\n      const earlyBuffer = 15;\n      const allowedStart = scheduleStart - earlyBuffer;\n      if (checkTimeMinutes >= allowedStart && checkTimeMinutes <= scheduleEnd) {\n        result.isValid = true;\n        result.message = 'Check-in allowed';\n      } else if (checkTimeMinutes < allowedStart) {\n        result.message = `Too early. Check-in allowed from ${this.minutesToTime(allowedStart)}`;\n      } else {\n        result.message = `Too late. Work hours end at ${effectiveSchedule.endTime}`;\n      }\n    } else if (action === 'checkout') {\n      // Allow check-out 30 minutes after end time\n      const lateBuffer = 30;\n      const allowedEnd = scheduleEnd + lateBuffer;\n      if (checkTimeMinutes >= scheduleStart && checkTimeMinutes <= allowedEnd) {\n        result.isValid = true;\n        result.message = 'Check-out allowed';\n      } else if (checkTimeMinutes < scheduleStart) {\n        result.message = `Too early. Work starts at ${effectiveSchedule.startTime}`;\n      } else {\n        result.message = `Very late check-out. Consider overtime approval.`;\n        result.isValid = true; // Allow but flag for review\n      }\n    }\n    return result;\n  }\n\n  /**\n   * Convert minutes to time string\n   */\n  static minutesToTime(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n  }\n\n  /**\n   * Get all schedules for a user in a date range\n   */\n  static getUserSchedulesInRange(user, startDate, endDate) {\n    const start = dayjs(startDate);\n    const end = dayjs(endDate);\n    const schedules = [];\n    let current = start;\n    while (current.isBefore(end) || current.isSame(end, 'day')) {\n      const dateStr = current.format('YYYY-MM-DD');\n      const effectiveSchedule = this.getEffectiveSchedule(user, dateStr);\n      schedules.push({\n        date: dateStr,\n        schedule: effectiveSchedule,\n        dayOfWeek: current.format('dddd')\n      });\n      current = current.add(1, 'day');\n    }\n    return schedules;\n  }\n\n  /**\n   * Create a new work schedule entry\n   */\n  static createScheduleEntry(userId, scheduleData) {\n    return {\n      id: `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      userId,\n      type: scheduleData.type || 'daily',\n      priority: this.getSchedulePriority(scheduleData.type),\n      scheduleTemplate: scheduleData.scheduleTemplate,\n      startTime: scheduleData.startTime,\n      endTime: scheduleData.endTime,\n      minimumHours: scheduleData.minimumHours,\n      effectiveFrom: scheduleData.effectiveFrom,\n      effectiveTo: scheduleData.effectiveTo,\n      specificDate: scheduleData.specificDate,\n      daysOfWeek: scheduleData.daysOfWeek,\n      description: scheduleData.description,\n      createdAt: new Date().toISOString(),\n      isActive: true\n    };\n  }\n\n  /**\n   * Get priority for schedule type\n   */\n  static getSchedulePriority(type) {\n    const priorities = {\n      'default': 1,\n      'weekly': 2,\n      'daily': 3,\n      'time_specific': 4\n    };\n    return priorities[type] || 1;\n  }\n\n  /**\n   * Calculate hours between two times (handles overnight shifts)\n   */\n  static calculateHours(startTime, endTime) {\n    const [startHour, startMin] = startTime.split(':').map(Number);\n    const [endHour, endMin] = endTime.split(':').map(Number);\n    let startMinutes = startHour * 60 + startMin;\n    let endMinutes = endHour * 60 + endMin;\n\n    // Handle overnight shifts (night shift)\n    if (endMinutes <= startMinutes) {\n      endMinutes += 24 * 60; // Add 24 hours\n    }\n    const diffMinutes = endMinutes - startMinutes;\n    return (diffMinutes / 60).toFixed(2);\n  }\n}\nexport default WorkScheduleUtils;", "map": {"version": 3, "names": ["dayjs", "DEFAULT_WORK_SCHEDULE", "WorkScheduleUtils", "getEffectiveSchedule", "user", "targetDate", "targetTime", "schedules", "workSchedules", "defaultSchedule", "workSchedule", "target", "applicableSchedules", "filter", "schedule", "isScheduleApplicable", "sort", "a", "b", "priority", "length", "scheduleStart", "effectiveFrom", "scheduleEnd", "effectiveTo", "isBefore", "isAfter", "type", "isTimeSpecificApplicable", "isDailyApplicable", "isWeeklyApplicable", "scheduleDate", "specificDate", "isSame", "timeStart", "parseTime", "startTime", "timeEnd", "endTime", "daysOfWeek", "dayOfWeek", "day", "includes", "timeString", "hours", "minutes", "split", "map", "Number", "validateCheckInOut", "checkTime", "action", "checkDate", "format", "effectiveSchedule", "checkTimeMinutes", "result", "<PERSON><PERSON><PERSON><PERSON>", "message", "<PERSON><PERSON><PERSON><PERSON>", "scheduleType", "early<PERSON>uffer", "allowedStart", "minutesToTime", "<PERSON><PERSON><PERSON><PERSON>", "allowedEnd", "Math", "floor", "mins", "toString", "padStart", "getUserSchedulesInRange", "startDate", "endDate", "start", "end", "current", "dateStr", "push", "date", "add", "createScheduleEntry", "userId", "scheduleData", "id", "Date", "now", "random", "substr", "getSchedulePriority", "scheduleTemplate", "minimumHours", "description", "createdAt", "toISOString", "isActive", "priorities", "calculateHours", "startHour", "startMin", "endHour", "endMin", "startMinutes", "endMinutes", "diffMinutes", "toFixed"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/utils/workScheduleUtils.js"], "sourcesContent": ["import dayjs from 'dayjs';\nimport { DEFAULT_WORK_SCHEDULE } from 'constants/workSchedule';\n\n/**\n * Work Schedule Utilities\n * Handles multiple schedule types with priority-based resolution\n */\nexport class WorkScheduleUtils {\n  /**\n   * Get effective work schedule for a user at a specific date/time\n   * Priority: Time-Specific > Daily > Weekly > Default\n   */\n  static getEffectiveSchedule(user, targetDate, targetTime = null) {\n    const schedules = user.workSchedules || [];\n    const defaultSchedule = user.workSchedule || DEFAULT_WORK_SCHEDULE;\n    \n    // Convert target date to dayjs for comparison\n    const target = dayjs(targetDate);\n    \n    // Find applicable schedules\n    const applicableSchedules = schedules.filter(schedule => {\n      return this.isScheduleApplicable(schedule, target, targetTime);\n    });\n    \n    // Sort by priority (highest first)\n    applicableSchedules.sort((a, b) => (b.priority || 1) - (a.priority || 1));\n    \n    // Return highest priority schedule or default\n    return applicableSchedules.length > 0 ? applicableSchedules[0] : defaultSchedule;\n  }\n  \n  /**\n   * Check if a schedule is applicable for the given date/time\n   */\n  static isScheduleApplicable(schedule, targetDate, targetTime) {\n    const scheduleStart = dayjs(schedule.effectiveFrom);\n    const scheduleEnd = dayjs(schedule.effectiveTo);\n    \n    // Check date range\n    if (targetDate.isBefore(scheduleStart) || targetDate.isAfter(scheduleEnd)) {\n      return false;\n    }\n    \n    // Check schedule type specific conditions\n    switch (schedule.type) {\n      case 'time_specific':\n        return this.isTimeSpecificApplicable(schedule, targetDate, targetTime);\n      case 'daily':\n        return this.isDailyApplicable(schedule, targetDate);\n      case 'weekly':\n        return this.isWeeklyApplicable(schedule, targetDate);\n      default:\n        return true;\n    }\n  }\n  \n  /**\n   * Check if time-specific schedule applies\n   */\n  static isTimeSpecificApplicable(schedule, targetDate, targetTime) {\n    if (!targetTime) return false;\n    \n    const scheduleDate = dayjs(schedule.specificDate);\n    if (!targetDate.isSame(scheduleDate, 'day')) return false;\n    \n    const timeStart = this.parseTime(schedule.startTime);\n    const timeEnd = this.parseTime(schedule.endTime);\n    const target = this.parseTime(targetTime);\n    \n    return target >= timeStart && target <= timeEnd;\n  }\n  \n  /**\n   * Check if daily schedule applies\n   */\n  static isDailyApplicable(schedule, targetDate) {\n    if (schedule.specificDate) {\n      return targetDate.isSame(dayjs(schedule.specificDate), 'day');\n    }\n    return true;\n  }\n  \n  /**\n   * Check if weekly schedule applies\n   */\n  static isWeeklyApplicable(schedule, targetDate) {\n    if (schedule.daysOfWeek && schedule.daysOfWeek.length > 0) {\n      const dayOfWeek = targetDate.day(); // 0 = Sunday, 1 = Monday, etc.\n      return schedule.daysOfWeek.includes(dayOfWeek);\n    }\n    return true;\n  }\n  \n  /**\n   * Parse time string to minutes for comparison\n   */\n  static parseTime(timeString) {\n    const [hours, minutes] = timeString.split(':').map(Number);\n    return (hours * 60) + minutes;\n  }\n  \n  /**\n   * Validate check-in/check-out against schedule\n   */\n  static validateCheckInOut(user, checkTime, action = 'checkin') {\n    const checkDate = dayjs(checkTime);\n    const timeString = checkDate.format('HH:mm');\n    \n    const effectiveSchedule = this.getEffectiveSchedule(\n      user, \n      checkDate.format('YYYY-MM-DD'), \n      timeString\n    );\n    \n    const scheduleStart = this.parseTime(effectiveSchedule.startTime);\n    const scheduleEnd = this.parseTime(effectiveSchedule.endTime);\n    const checkTimeMinutes = this.parseTime(timeString);\n    \n    const result = {\n      isValid: false,\n      schedule: effectiveSchedule,\n      message: '',\n      allowedRange: `${effectiveSchedule.startTime} - ${effectiveSchedule.endTime}`,\n      scheduleType: effectiveSchedule.type || 'default'\n    };\n    \n    if (action === 'checkin') {\n      // Allow check-in 15 minutes before start time\n      const earlyBuffer = 15;\n      const allowedStart = scheduleStart - earlyBuffer;\n      \n      if (checkTimeMinutes >= allowedStart && checkTimeMinutes <= scheduleEnd) {\n        result.isValid = true;\n        result.message = 'Check-in allowed';\n      } else if (checkTimeMinutes < allowedStart) {\n        result.message = `Too early. Check-in allowed from ${this.minutesToTime(allowedStart)}`;\n      } else {\n        result.message = `Too late. Work hours end at ${effectiveSchedule.endTime}`;\n      }\n    } else if (action === 'checkout') {\n      // Allow check-out 30 minutes after end time\n      const lateBuffer = 30;\n      const allowedEnd = scheduleEnd + lateBuffer;\n      \n      if (checkTimeMinutes >= scheduleStart && checkTimeMinutes <= allowedEnd) {\n        result.isValid = true;\n        result.message = 'Check-out allowed';\n      } else if (checkTimeMinutes < scheduleStart) {\n        result.message = `Too early. Work starts at ${effectiveSchedule.startTime}`;\n      } else {\n        result.message = `Very late check-out. Consider overtime approval.`;\n        result.isValid = true; // Allow but flag for review\n      }\n    }\n    \n    return result;\n  }\n  \n  /**\n   * Convert minutes to time string\n   */\n  static minutesToTime(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = minutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n  }\n  \n  /**\n   * Get all schedules for a user in a date range\n   */\n  static getUserSchedulesInRange(user, startDate, endDate) {\n    const start = dayjs(startDate);\n    const end = dayjs(endDate);\n    const schedules = [];\n    \n    let current = start;\n    while (current.isBefore(end) || current.isSame(end, 'day')) {\n      const dateStr = current.format('YYYY-MM-DD');\n      const effectiveSchedule = this.getEffectiveSchedule(user, dateStr);\n      \n      schedules.push({\n        date: dateStr,\n        schedule: effectiveSchedule,\n        dayOfWeek: current.format('dddd')\n      });\n      \n      current = current.add(1, 'day');\n    }\n    \n    return schedules;\n  }\n  \n  /**\n   * Create a new work schedule entry\n   */\n  static createScheduleEntry(userId, scheduleData) {\n    return {\n      id: `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      userId,\n      type: scheduleData.type || 'daily',\n      priority: this.getSchedulePriority(scheduleData.type),\n      scheduleTemplate: scheduleData.scheduleTemplate,\n      startTime: scheduleData.startTime,\n      endTime: scheduleData.endTime,\n      minimumHours: scheduleData.minimumHours,\n      effectiveFrom: scheduleData.effectiveFrom,\n      effectiveTo: scheduleData.effectiveTo,\n      specificDate: scheduleData.specificDate,\n      daysOfWeek: scheduleData.daysOfWeek,\n      description: scheduleData.description,\n      createdAt: new Date().toISOString(),\n      isActive: true\n    };\n  }\n  \n  /**\n   * Get priority for schedule type\n   */\n  static getSchedulePriority(type) {\n    const priorities = {\n      'default': 1,\n      'weekly': 2,\n      'daily': 3,\n      'time_specific': 4\n    };\n    return priorities[type] || 1;\n  }\n  \n  /**\n   * Calculate hours between two times (handles overnight shifts)\n   */\n  static calculateHours(startTime, endTime) {\n    const [startHour, startMin] = startTime.split(':').map(Number);\n    const [endHour, endMin] = endTime.split(':').map(Number);\n    \n    let startMinutes = (startHour * 60) + startMin;\n    let endMinutes = (endHour * 60) + endMin;\n    \n    // Handle overnight shifts (night shift)\n    if (endMinutes <= startMinutes) {\n      endMinutes += (24 * 60); // Add 24 hours\n    }\n    \n    const diffMinutes = endMinutes - startMinutes;\n    return (diffMinutes / 60).toFixed(2);\n  }\n}\n\nexport default WorkScheduleUtils;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,qBAAqB,QAAQ,wBAAwB;;AAE9D;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,CAAC;EAC7B;AACF;AACA;AACA;EACE,OAAOC,oBAAoBA,CAACC,IAAI,EAAEC,UAAU,EAAEC,UAAU,GAAG,IAAI,EAAE;IAC/D,MAAMC,SAAS,GAAGH,IAAI,CAACI,aAAa,IAAI,EAAE;IAC1C,MAAMC,eAAe,GAAGL,IAAI,CAACM,YAAY,IAAIT,qBAAqB;;IAElE;IACA,MAAMU,MAAM,GAAGX,KAAK,CAACK,UAAU,CAAC;;IAEhC;IACA,MAAMO,mBAAmB,GAAGL,SAAS,CAACM,MAAM,CAACC,QAAQ,IAAI;MACvD,OAAO,IAAI,CAACC,oBAAoB,CAACD,QAAQ,EAAEH,MAAM,EAAEL,UAAU,CAAC;IAChE,CAAC,CAAC;;IAEF;IACAM,mBAAmB,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,QAAQ,IAAI,CAAC,KAAKF,CAAC,CAACE,QAAQ,IAAI,CAAC,CAAC,CAAC;;IAEzE;IACA,OAAOP,mBAAmB,CAACQ,MAAM,GAAG,CAAC,GAAGR,mBAAmB,CAAC,CAAC,CAAC,GAAGH,eAAe;EAClF;;EAEA;AACF;AACA;EACE,OAAOM,oBAAoBA,CAACD,QAAQ,EAAET,UAAU,EAAEC,UAAU,EAAE;IAC5D,MAAMe,aAAa,GAAGrB,KAAK,CAACc,QAAQ,CAACQ,aAAa,CAAC;IACnD,MAAMC,WAAW,GAAGvB,KAAK,CAACc,QAAQ,CAACU,WAAW,CAAC;;IAE/C;IACA,IAAInB,UAAU,CAACoB,QAAQ,CAACJ,aAAa,CAAC,IAAIhB,UAAU,CAACqB,OAAO,CAACH,WAAW,CAAC,EAAE;MACzE,OAAO,KAAK;IACd;;IAEA;IACA,QAAQT,QAAQ,CAACa,IAAI;MACnB,KAAK,eAAe;QAClB,OAAO,IAAI,CAACC,wBAAwB,CAACd,QAAQ,EAAET,UAAU,EAAEC,UAAU,CAAC;MACxE,KAAK,OAAO;QACV,OAAO,IAAI,CAACuB,iBAAiB,CAACf,QAAQ,EAAET,UAAU,CAAC;MACrD,KAAK,QAAQ;QACX,OAAO,IAAI,CAACyB,kBAAkB,CAAChB,QAAQ,EAAET,UAAU,CAAC;MACtD;QACE,OAAO,IAAI;IACf;EACF;;EAEA;AACF;AACA;EACE,OAAOuB,wBAAwBA,CAACd,QAAQ,EAAET,UAAU,EAAEC,UAAU,EAAE;IAChE,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAE7B,MAAMyB,YAAY,GAAG/B,KAAK,CAACc,QAAQ,CAACkB,YAAY,CAAC;IACjD,IAAI,CAAC3B,UAAU,CAAC4B,MAAM,CAACF,YAAY,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK;IAEzD,MAAMG,SAAS,GAAG,IAAI,CAACC,SAAS,CAACrB,QAAQ,CAACsB,SAAS,CAAC;IACpD,MAAMC,OAAO,GAAG,IAAI,CAACF,SAAS,CAACrB,QAAQ,CAACwB,OAAO,CAAC;IAChD,MAAM3B,MAAM,GAAG,IAAI,CAACwB,SAAS,CAAC7B,UAAU,CAAC;IAEzC,OAAOK,MAAM,IAAIuB,SAAS,IAAIvB,MAAM,IAAI0B,OAAO;EACjD;;EAEA;AACF;AACA;EACE,OAAOR,iBAAiBA,CAACf,QAAQ,EAAET,UAAU,EAAE;IAC7C,IAAIS,QAAQ,CAACkB,YAAY,EAAE;MACzB,OAAO3B,UAAU,CAAC4B,MAAM,CAACjC,KAAK,CAACc,QAAQ,CAACkB,YAAY,CAAC,EAAE,KAAK,CAAC;IAC/D;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,OAAOF,kBAAkBA,CAAChB,QAAQ,EAAET,UAAU,EAAE;IAC9C,IAAIS,QAAQ,CAACyB,UAAU,IAAIzB,QAAQ,CAACyB,UAAU,CAACnB,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMoB,SAAS,GAAGnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpC,OAAO3B,QAAQ,CAACyB,UAAU,CAACG,QAAQ,CAACF,SAAS,CAAC;IAChD;IACA,OAAO,IAAI;EACb;;EAEA;AACF;AACA;EACE,OAAOL,SAASA,CAACQ,UAAU,EAAE;IAC3B,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,UAAU,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAC1D,OAAQJ,KAAK,GAAG,EAAE,GAAIC,OAAO;EAC/B;;EAEA;AACF;AACA;EACE,OAAOI,kBAAkBA,CAAC7C,IAAI,EAAE8C,SAAS,EAAEC,MAAM,GAAG,SAAS,EAAE;IAC7D,MAAMC,SAAS,GAAGpD,KAAK,CAACkD,SAAS,CAAC;IAClC,MAAMP,UAAU,GAAGS,SAAS,CAACC,MAAM,CAAC,OAAO,CAAC;IAE5C,MAAMC,iBAAiB,GAAG,IAAI,CAACnD,oBAAoB,CACjDC,IAAI,EACJgD,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC,EAC9BV,UACF,CAAC;IAED,MAAMtB,aAAa,GAAG,IAAI,CAACc,SAAS,CAACmB,iBAAiB,CAAClB,SAAS,CAAC;IACjE,MAAMb,WAAW,GAAG,IAAI,CAACY,SAAS,CAACmB,iBAAiB,CAAChB,OAAO,CAAC;IAC7D,MAAMiB,gBAAgB,GAAG,IAAI,CAACpB,SAAS,CAACQ,UAAU,CAAC;IAEnD,MAAMa,MAAM,GAAG;MACbC,OAAO,EAAE,KAAK;MACd3C,QAAQ,EAAEwC,iBAAiB;MAC3BI,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,GAAGL,iBAAiB,CAAClB,SAAS,MAAMkB,iBAAiB,CAAChB,OAAO,EAAE;MAC7EsB,YAAY,EAAEN,iBAAiB,CAAC3B,IAAI,IAAI;IAC1C,CAAC;IAED,IAAIwB,MAAM,KAAK,SAAS,EAAE;MACxB;MACA,MAAMU,WAAW,GAAG,EAAE;MACtB,MAAMC,YAAY,GAAGzC,aAAa,GAAGwC,WAAW;MAEhD,IAAIN,gBAAgB,IAAIO,YAAY,IAAIP,gBAAgB,IAAIhC,WAAW,EAAE;QACvEiC,MAAM,CAACC,OAAO,GAAG,IAAI;QACrBD,MAAM,CAACE,OAAO,GAAG,kBAAkB;MACrC,CAAC,MAAM,IAAIH,gBAAgB,GAAGO,YAAY,EAAE;QAC1CN,MAAM,CAACE,OAAO,GAAG,oCAAoC,IAAI,CAACK,aAAa,CAACD,YAAY,CAAC,EAAE;MACzF,CAAC,MAAM;QACLN,MAAM,CAACE,OAAO,GAAG,+BAA+BJ,iBAAiB,CAAChB,OAAO,EAAE;MAC7E;IACF,CAAC,MAAM,IAAIa,MAAM,KAAK,UAAU,EAAE;MAChC;MACA,MAAMa,UAAU,GAAG,EAAE;MACrB,MAAMC,UAAU,GAAG1C,WAAW,GAAGyC,UAAU;MAE3C,IAAIT,gBAAgB,IAAIlC,aAAa,IAAIkC,gBAAgB,IAAIU,UAAU,EAAE;QACvET,MAAM,CAACC,OAAO,GAAG,IAAI;QACrBD,MAAM,CAACE,OAAO,GAAG,mBAAmB;MACtC,CAAC,MAAM,IAAIH,gBAAgB,GAAGlC,aAAa,EAAE;QAC3CmC,MAAM,CAACE,OAAO,GAAG,6BAA6BJ,iBAAiB,CAAClB,SAAS,EAAE;MAC7E,CAAC,MAAM;QACLoB,MAAM,CAACE,OAAO,GAAG,kDAAkD;QACnEF,MAAM,CAACC,OAAO,GAAG,IAAI,CAAC,CAAC;MACzB;IACF;IAEA,OAAOD,MAAM;EACf;;EAEA;AACF;AACA;EACE,OAAOO,aAAaA,CAAClB,OAAO,EAAE;IAC5B,MAAMD,KAAK,GAAGsB,IAAI,CAACC,KAAK,CAACtB,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMuB,IAAI,GAAGvB,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGD,KAAK,CAACyB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACnF;;EAEA;AACF;AACA;EACE,OAAOC,uBAAuBA,CAACnE,IAAI,EAAEoE,SAAS,EAAEC,OAAO,EAAE;IACvD,MAAMC,KAAK,GAAG1E,KAAK,CAACwE,SAAS,CAAC;IAC9B,MAAMG,GAAG,GAAG3E,KAAK,CAACyE,OAAO,CAAC;IAC1B,MAAMlE,SAAS,GAAG,EAAE;IAEpB,IAAIqE,OAAO,GAAGF,KAAK;IACnB,OAAOE,OAAO,CAACnD,QAAQ,CAACkD,GAAG,CAAC,IAAIC,OAAO,CAAC3C,MAAM,CAAC0C,GAAG,EAAE,KAAK,CAAC,EAAE;MAC1D,MAAME,OAAO,GAAGD,OAAO,CAACvB,MAAM,CAAC,YAAY,CAAC;MAC5C,MAAMC,iBAAiB,GAAG,IAAI,CAACnD,oBAAoB,CAACC,IAAI,EAAEyE,OAAO,CAAC;MAElEtE,SAAS,CAACuE,IAAI,CAAC;QACbC,IAAI,EAAEF,OAAO;QACb/D,QAAQ,EAAEwC,iBAAiB;QAC3Bd,SAAS,EAAEoC,OAAO,CAACvB,MAAM,CAAC,MAAM;MAClC,CAAC,CAAC;MAEFuB,OAAO,GAAGA,OAAO,CAACI,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;IACjC;IAEA,OAAOzE,SAAS;EAClB;;EAEA;AACF;AACA;EACE,OAAO0E,mBAAmBA,CAACC,MAAM,EAAEC,YAAY,EAAE;IAC/C,OAAO;MACLC,EAAE,EAAE,YAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIpB,IAAI,CAACqB,MAAM,CAAC,CAAC,CAAClB,QAAQ,CAAC,EAAE,CAAC,CAACmB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;MACvEN,MAAM;MACNvD,IAAI,EAAEwD,YAAY,CAACxD,IAAI,IAAI,OAAO;MAClCR,QAAQ,EAAE,IAAI,CAACsE,mBAAmB,CAACN,YAAY,CAACxD,IAAI,CAAC;MACrD+D,gBAAgB,EAAEP,YAAY,CAACO,gBAAgB;MAC/CtD,SAAS,EAAE+C,YAAY,CAAC/C,SAAS;MACjCE,OAAO,EAAE6C,YAAY,CAAC7C,OAAO;MAC7BqD,YAAY,EAAER,YAAY,CAACQ,YAAY;MACvCrE,aAAa,EAAE6D,YAAY,CAAC7D,aAAa;MACzCE,WAAW,EAAE2D,YAAY,CAAC3D,WAAW;MACrCQ,YAAY,EAAEmD,YAAY,CAACnD,YAAY;MACvCO,UAAU,EAAE4C,YAAY,CAAC5C,UAAU;MACnCqD,WAAW,EAAET,YAAY,CAACS,WAAW;MACrCC,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;MACnCC,QAAQ,EAAE;IACZ,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAON,mBAAmBA,CAAC9D,IAAI,EAAE;IAC/B,MAAMqE,UAAU,GAAG;MACjB,SAAS,EAAE,CAAC;MACZ,QAAQ,EAAE,CAAC;MACX,OAAO,EAAE,CAAC;MACV,eAAe,EAAE;IACnB,CAAC;IACD,OAAOA,UAAU,CAACrE,IAAI,CAAC,IAAI,CAAC;EAC9B;;EAEA;AACF;AACA;EACE,OAAOsE,cAAcA,CAAC7D,SAAS,EAAEE,OAAO,EAAE;IACxC,MAAM,CAAC4D,SAAS,EAAEC,QAAQ,CAAC,GAAG/D,SAAS,CAACU,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAC9D,MAAM,CAACoD,OAAO,EAAEC,MAAM,CAAC,GAAG/D,OAAO,CAACQ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAExD,IAAIsD,YAAY,GAAIJ,SAAS,GAAG,EAAE,GAAIC,QAAQ;IAC9C,IAAII,UAAU,GAAIH,OAAO,GAAG,EAAE,GAAIC,MAAM;;IAExC;IACA,IAAIE,UAAU,IAAID,YAAY,EAAE;MAC9BC,UAAU,IAAK,EAAE,GAAG,EAAG,CAAC,CAAC;IAC3B;IAEA,MAAMC,WAAW,GAAGD,UAAU,GAAGD,YAAY;IAC7C,OAAO,CAACE,WAAW,GAAG,EAAE,EAAEC,OAAO,CAAC,CAAC,CAAC;EACtC;AACF;AAEA,eAAevG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}