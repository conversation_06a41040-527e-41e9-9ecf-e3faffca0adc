import { SprintService } from "../services/SprintService";
import { SprintActions, GeneralActions } from "../slices/actions";
import { all, call, put, takeLatest } from 'redux-saga/effects';

/**
 * Get sprint details by ID
 */
function *getSprintById({ type, payload }) {
  try {
    yield put(GeneralActions.removeError(type));
    yield put(GeneralActions.startLoading(type));
    
    console.log("Getting sprint details for:", payload.id);
    const result = yield call(SprintService.getSprintById, payload.id);
    
    yield put(SprintActions.getSuccessfullySprintById(result));
    yield put(GeneralActions.stopLoading(type));
  } catch (err) {
    console.error("Error in getSprintById saga:", err);
    yield put(GeneralActions.stopLoading(type));
    yield put(GeneralActions.addError({
      action: type,
      message: err.message || "Failed to get sprint details"
    }));
  }
}

/**
 * Get all sprints
 */
function *getSprints({ type, payload }) {
  try {
    yield put(GeneralActions.removeError(type));
    yield put(GeneralActions.startLoading(type));
    
    console.log("Getting sprints with filter:", payload);
    const result = yield call(SprintService.getSprints, payload);
    
    yield put(SprintActions.getSuccessfullySprints(result));
    yield put(GeneralActions.stopLoading(type));
  } catch (err) {
    console.error("Error in getSprints saga:", err);
    yield put(GeneralActions.stopLoading(type));
    yield put(GeneralActions.addError({
      action: type,
      message: err.message || "Failed to get sprints"
    }));
  }
}

/**
 * Create a new sprint
 */
function *createSprint({ type, payload }) {
  try {
    yield put(GeneralActions.removeError(type));
    yield put(GeneralActions.startLoading(type));
    
    console.log("Creating sprint:", payload);
    yield call(SprintService.createSprint, payload);
    
    // Refresh sprints list
    const result = yield call(SprintService.getSprints);
    yield put(SprintActions.getSuccessfullySprints(result));
    
    yield put(GeneralActions.stopLoading(type));
  } catch (err) {
    console.error("Error in createSprint saga:", err);
    yield put(GeneralActions.stopLoading(type));
    yield put(GeneralActions.addError({
      action: type,
      message: err.message || "Failed to create sprint"
    }));
  }
}

/**
 * Update an existing sprint
 */
function *updateSprint({ type, payload }) {
  try {
    yield put(GeneralActions.removeError(type));
    yield put(GeneralActions.startLoading(type));
    
    console.log("Updating sprint:", payload);
    yield call(SprintService.updateSprint, payload.id, payload);
    
    // Refresh sprints list
    const result = yield call(SprintService.getSprints);
    yield put(SprintActions.getSuccessfullySprints(result));
    
    yield put(GeneralActions.stopLoading(type));
  } catch (err) {
    console.error("Error in updateSprint saga:", err);
    yield put(GeneralActions.stopLoading(type));
    yield put(GeneralActions.addError({
      action: type,
      message: err.message || "Failed to update sprint"
    }));
  }
}

/**
 * Sprint saga watcher
 */
export function *SprintWatcher() {
  yield all([
    yield takeLatest(SprintActions.getSprintById.type, getSprintById),
    yield takeLatest(SprintActions.getSprints.type, getSprints),
    yield takeLatest(SprintActions.createSprint.type, createSprint),
    yield takeLatest(SprintActions.updateSprint.type, updateSprint)
  ]);
}