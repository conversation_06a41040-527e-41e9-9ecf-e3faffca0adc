import { createSlice } from "@reduxjs/toolkit";

export const SettingSlice = createSlice({
    name: "setting",
initialState: {
    setting: {
        _id: '',
        name: '',
        address: '',
        city: '',
        country: '',
        email: '',
        phone: '',
        leaveLimit: 0,
        day: 0,
        leaves: []
    },
    loading: false,
    error: null,
    success: null
},
reducers: {
    getSetting: () => {}, // kept as trigger
    getSettingSuccess: (state, action) => {
        state.setting = action.payload;
    },
    updateSetting: () => {},
    addCompanyLeave: () => {},
    deleteAllCompanyLeave: () => {},
}
});

export default SettingSlice;