import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Button } from '@mui/material';

import dayjs from 'dayjs';
import DayPicker from './TimePickers/DayPicker';
import WeeklyPicker from './TimePickers/WeeklyPicker';
import MonthPicker from './TimePickers/MonthPicker';
import DayWorkSchedule from './Components/DayWorkSchedule';
import WeekWorkSchedule from './Components/WeekWorkSchedule';
import MonthWorkSchedule from './Components/MonthWorkSchedule';

function WorkSchedule() {
  
  const [viewOption, setViewOption] = useState('Day');

  // Independent date states for each view to prevent circular dependencies
  const [dayDateRange, setDayDateRange] = useState({
    startDate: dayjs().format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD')
  });

  const [weekDateRange, setWeekDateRange] = useState({
    startDate: dayjs().startOf('week').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('week').format('YYYY-MM-DD')
  });

  const [monthDateRange, setMonthDateRange] = useState({
    startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('month').format('YYYY-MM-DD')
  });

  // Get current date range based on active view
  const getCurrentDateRange = () => {
    switch(viewOption) {
      case 'Day': return dayDateRange;
      case 'Week': return weekDateRange;
      case 'Month': return monthDateRange;
      default: return dayDateRange;
    }
  };

  // Handle date changes from pickers - each picker updates its own state
  const handleDayDateChange = useCallback((newDateRange) => {
    setDayDateRange(newDateRange);
  }, []);

  const handleWeekDateChange = useCallback((newDateRange) => {
    setWeekDateRange(newDateRange);
  }, []);

  const handleMonthDateChange = useCallback((newDateRange) => {
    setMonthDateRange(newDateRange);
  }, []);

  // Reference to track the last fetched data to prevent duplicate API calls
  const lastFetchedRef = useRef({ dateRange: null, view: null });

 

  return (
    <>
      <h1>WorkSchedule</h1>

      {/* View options and date picker */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          position: 'relative',
        }}
      >
        {/* Day/Week/Month tabs */}
        <Box
          sx={{
            display: 'flex',
            borderRadius: '4px',
            overflow: 'hidden',
            border: '1px solid #e0e0e0',
          }}
        >
          {['Day', 'Week', 'Month'].map((option) => (
            <Button
              key={option}
              onClick={() => setViewOption(option)}
              sx={{
                bgcolor: viewOption === option ? 'primary.main' : 'transparent',
                color: viewOption === option ? 'white' : 'text.primary',
                borderRadius: 0,
                '&:hover': {
                  bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              {option}
            </Button>
          ))}
        </Box>

        {/* Date Pickers - Each picker manages its own independent state */}
        {viewOption === 'Day' && (
          <DayPicker
            onChange={handleDayDateChange}
            startDate={dayDateRange.startDate}
            endDate={dayDateRange.endDate}
          />
        )}
        {viewOption === 'Week' && (
          <WeeklyPicker
            onChange={handleWeekDateChange}
            startDate={weekDateRange.startDate}
          />
        )}
        {viewOption === 'Month' && (
          <MonthPicker
            onChange={handleMonthDateChange}
            selectedMonth={monthDateRange.startDate}
          />
        )}
      </Box>

      {/* Conditionally render view with appropriate date range */}
      {viewOption === 'Day' && <DayWorkSchedule dateRange={dayDateRange} />}
      {viewOption === 'Week' && <WeekWorkSchedule dateRange={weekDateRange} />}
      {viewOption === 'Month' && <MonthWorkSchedule dateRange={monthDateRange} />}
    </>
  );
}

export default WorkSchedule;
