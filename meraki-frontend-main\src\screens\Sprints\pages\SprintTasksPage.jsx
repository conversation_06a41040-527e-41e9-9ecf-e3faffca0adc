import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  Button,
  IconButton,
  Grid,
  LinearProgress,
  Card,
  CardContent,
  Divider
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { SprintActions, UserActions } from '../../../slices/actions';
import { UserSelector } from '../../../selectors';

const SprintTasksPage = () => {
  const { sprintId } = useParams();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  
  // Get sprint details and users from Redux store
  const sprintDetails = useSelector(state => state.sprint?.sprintDetails);
  const users = useSelector(UserSelector.getUsers());
  const currentUser = useSelector(UserSelector.profile());
  
  useEffect(() => {
    if (sprintId) {
      setLoading(true);
      // Fetch users first
      dispatch(UserActions.getUsers());
      // Then fetch sprint details
      dispatch(SprintActions.getSprintById({ id: sprintId }));
      setLoading(false);
    }
  }, [dispatch, sprintId]);
  
  // Get user name by ID
  const getUserName = (userId) => {
    if (!userId) { return 'Unknown' }
    const user = users.find(u => u._id === userId);
    return user ? user.name : 'Unknown';
  };


  
  // Get assignee names - improved to handle all possible formats
  const getAssigneeNames = (assignees) => {
    if (!Array.isArray(assignees) || assignees.length === 0) { return 'Unassigned' }
    
    return assignees.map(assignee => {
      // If assignee is an object with name property
      if (assignee && typeof assignee === 'object' && assignee.name) {
        return assignee.name;
      }
      // If assignee is an object with _id property
      else if (assignee && typeof assignee === 'object' && assignee._id) {
        return getUserName(assignee._id);
      }
      // If assignee is just an ID string
      return getUserName(assignee);
    }).join(', ');
  };
  
  // Format hours for display
  const formatHours = (hours) => {
    if (!hours) { return '0h' }
    if (typeof hours === 'number') { return `${hours.toFixed(1)}h` }
    
    // If hours is an object with multiple days
    if (typeof hours === 'object') {
      const totalHours = Object.values(hours).reduce((sum, h) => sum + h, 0);
      return `${totalHours.toFixed(1)}h`;
    }
    
    return '0h';
  };
  

 // Get task status color
const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'completed': return 'success';
    case 'in progress': return 'primary';
    case 'to do': return 'default';
    default: return 'default';
  }
};

  
  // Get sprint status color
  const getSprintStatusColor = (status) => {
    if (status === 'active') { return 'success' }
    if (status === 'completed') { return 'secondary' }
    return 'primary';
  };

  // Calculate sprint metrics
// Calculate sprint metrics
const calculateSprintMetrics = (sprint, tasks) => {
  if (!sprint || !tasks) { return {} }
  
  // Calculate time elapsed percentage
  const startDate = new Date(sprint.startDate);
  const endDate = new Date(sprint.endDate);
  const currentDate = new Date();
  const totalDuration = endDate - startDate;
  const elapsedDuration = currentDate - startDate;
  const timeElapsedPercent = Math.min(100, Math.max(0, Math.round((elapsedDuration / totalDuration) * 100)));
  
  // Calculate task status counts and total hours
  const taskStatusCounts = {
    completed: 0,
    inProgress: 0,
    todo: 0,
    total: tasks.length
  };
  
  let totalHoursSpent = 0;  // Initialize here

  tasks.forEach(task => {
    // Count task statuses
    const status = task.taskStatus?.toLowerCase();
    if (status === 'completed') {
      taskStatusCounts.completed++;
    } else if (status === 'in progress') {
      taskStatusCounts.inProgress++;
    } else {
      taskStatusCounts.todo++;
    }
    
    // Sum hours spent
    if (task.totalHours) {
      totalHoursSpent += parseFloat(task.totalHours);
    } else if (task.hours && typeof task.hours === 'object') {
      totalHoursSpent += Object.values(task.hours).reduce((sum, h) => sum + h, 0);
    }
  });
  
  // Calculate completion percentage
  const completionPercent = tasks.length > 0 ? Math.round((taskStatusCounts.completed / tasks.length) * 100) : 0;
  
  return {
    timeElapsedPercent,
    completionPercent,
    taskStatusCounts,
    totalHoursSpent
  };
};


  // Handle back button
  const handleBack = () => {
    window.history.back();
  };
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (!sprintDetails || !sprintDetails.sprint) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h5">Sprint not found</Typography>
        <Button variant="outlined" onClick={handleBack} sx={{ mt: 2 }}>
          Back to Sprints
        </Button>
      </Box>
    );
  }
  
  const { sprint, tasks = [] } = sprintDetails;
  const metrics = calculateSprintMetrics(sprint, tasks);

  
  
  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handleBack} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4">Sprint: {sprint.name}</Typography>
      </Box>
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3, alignItems: 'center' }}>
        <Box>
          <Typography variant="body1" sx={{ mb: 1 }}>
            <strong>Goal:</strong> {sprint.goal}
          </Typography>
          <Typography variant="body2">
            <strong>Duration:</strong> {new Date(sprint.startDate).toLocaleDateString()} - {new Date(sprint.endDate).toLocaleDateString()}
          </Typography>
        </Box>
        <Chip 
          label={sprint.status || 'Planned'} 
          color={getSprintStatusColor(sprint.status)} 
          size="medium"
        />
      </Box>
      
      {/* Sprint Analysis Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Sprint Progress</Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Time Elapsed: {metrics.timeElapsedPercent}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.timeElapsedPercent} 
                  sx={{ height: 10, borderRadius: 5, mt: 1 }}
                />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Tasks Completed: {metrics.completionPercent}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.completionPercent} 
                  color="success"
                  sx={{ height: 10, borderRadius: 5, mt: 1 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>Task Status</Typography>
             <Grid container spacing={2}>
  <Grid item xs={6}>
    <Typography variant="body2" color="text.secondary">
      Completed: {metrics.taskStatusCounts.completed}
    </Typography>
  </Grid>
  <Grid item xs={6}>
    <Typography variant="body2" color="text.secondary">
      In Progress: {metrics.taskStatusCounts.inProgress}
    </Typography>
  </Grid>
  <Grid item xs={6}>
    <Typography variant="body2" color="text.secondary">
      To Do: {metrics.taskStatusCounts.todo}
    </Typography>
  </Grid>
</Grid>

            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Typography variant="h6" sx={{ mb: 2 }}>Tasks ({tasks.length})</Typography>
      
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Task</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created By</TableCell>
              <TableCell>Assigned To</TableCell>
              <TableCell>Hours</TableCell>
              <TableCell>Priority</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tasks.length > 0 ? (
              tasks.map((task) => (
                <TableRow key={task._id}>
                  <TableCell>{task.taskTitle}</TableCell>
                  <TableCell>
                    <Chip 
                      label={task.taskStatus || 'New'} 
                      color={getStatusColor(task.taskStatus)} 
                      size="small"
                    />
                  </TableCell>
                  <TableCell>{getUserName(task.reporter)}</TableCell>
                  <TableCell>{getAssigneeNames(task.assignee)}</TableCell>
                  <TableCell>{formatHours(task.totalHours || task.hours)}</TableCell>
                  <TableCell>
                    <Chip 
                      label={task.priority || 'Medium'} 
                      color={task.priority === 'High' ? 'error' : 'default'} 
                      size="small"
                    />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No tasks found in this sprint
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default SprintTasksPage;