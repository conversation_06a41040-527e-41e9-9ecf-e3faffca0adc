/**
 * Authentication Middleware
 *
 * This middleware validates JWT tokens for protected routes.
 * It extracts the token from the Authorization header, verifies it,
 * and attaches the user object to the request for downstream handlers.
 */

const jwt = require("jsonwebtoken");
const UserService = require("../services/user.service");
const secret = process.env.JWT_SECRET;

// Validate environment configuration on startup
if (!secret) {
    console.error('❌ ERROR: JWT_SECRET is not set in environment variables!');
    console.error('   Authentication will fail for all protected routes.');
    console.error('   Please set JWT_SECRET in your .env file.');
}

/**
 * Authentication middleware function
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {void}
 */
module.exports = async (req, res, next) => {
    try {
        // Extract authorization header
        const authHeader = req.headers?.authorization;

        // Check if Authorization header exists and has correct format
        if (!authHeader || !authHeader.startsWith("Bearer ")) {
            return res.status(401).json({
                message: "Unauthorized! Please login.",
                details: "Missing or invalid Authorization header"
            });
        }

        // Extract token from header
        const token = authHeader.split(" ")[1];

        if (!token) {
            return res.status(401).json({
                message: "No token provided",
                details: "Bearer token is empty"
            });
        }

        // Verify token and extract user ID
        const decoded = jwt.verify(token, secret);

        // Fetch user from database
        const user = await UserService.getUserById(decoded.id);

        // Ensure user exists
        if (!user) {
            return res.status(401).json({
                message: "Invalid user. Please login again!",
                details: "User not found in database"
            });
        }

        // Attach user object to request for use in route handlers
        req.user = user;

        // Continue to next middleware or route handler
        next();
    } catch (error) {
        // Handle specific JWT errors
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                message: "Invalid token!",
                details: "The token signature is invalid"
            });
        } else if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                message: "Token expired!",
                details: "Please login again to get a new token"
            });
        }

        // Handle other errors
        console.error("Auth Middleware Error:", error);
        return res.status(403).json({
            message: "Authentication failed!",
            details: "An unexpected error occurred during authentication"
        });
    }
};
