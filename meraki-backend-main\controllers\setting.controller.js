'use strict';

const SettingService = require("../services/setting.service");
const { SETTING } = require("../data/default");
const role = require('../constants/role');

exports.createDefaultSetting = async () => {
    const setting = await SettingService.getSetting({});

    if (setting.length === 0) {
        await SettingService.createSetting(SETTING);
    }
}

exports.getSetting = async (req, res) => {
    // Get user from the request (set by auth middleware)
    const { user } = req;

    // Check if user is authenticated
    if (!user) {
        return res.status(401).send('Authentication required');
    }

    // Allow all authenticated users to view settings
    const result = await SettingService.getSetting();
    return res.status(200).send(result[0]);
}

exports.updateSetting = async (req, res) => {
    const { user, params, body } = req;

    // Check if user is authenticated
    if (!user) {
        return res.status(401).send('Authentication required');
    }

    // Log user information for debugging
    console.log('User attempting to update settings:', {
        id: user._id,
        name: user.name,
        role: user.role,
        permissions: user.permissions
    });

    // Check if user has admin role
    const isAdmin = user.role && user.role.includes(role.admin.id);
    console.log('Is admin?', isAdmin);

    // Check if user has update permission for Settings
    const hasUpdatePermission = user.permissions && Array.isArray(user.permissions) && user.permissions.some(p =>
        p && p.feat === 'Setting' &&
        p.acts && Array.isArray(p.acts) &&
        (p.acts.includes('update') || p.acts.includes('*'))
    );
    console.log('Has update permission?', hasUpdatePermission);

    // Check if user has admin role or update permission for Settings
    const hasPermission = isAdmin || hasUpdatePermission;
    console.log('Has permission to update settings?', hasPermission);

    if (!hasPermission) {
        return res.status(403).json({ message: 'Forbidden - You need admin role or update permission for Settings' });
    }

    const result = await SettingService.updateSetting(params.id, body);

    if (!result || result.n === 0) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send({
        message: "Successfully proceed data."
    });
}

exports.addCompanyLeave = async (req, res) => {
    const { user, params, body } = req;

    // Check if user is authenticated
    if (!user) {
        return res.status(401).send('Authentication required');
    }

    // Log user information for debugging
    console.log('User attempting to add company leave:', {
        id: user._id,
        name: user.name,
        role: user.role,
        permissions: user.permissions
    });

    // Check if user has admin role
    const isAdmin = user.role && user.role.includes(role.admin.id);
    console.log('Is admin?', isAdmin);

    // Check if user has update permission for Settings
    const hasUpdatePermission = user.permissions && Array.isArray(user.permissions) && user.permissions.some(p =>
        p && p.feat === 'Setting' &&
        p.acts && Array.isArray(p.acts) &&
        (p.acts.includes('update') || p.acts.includes('*'))
    );
    console.log('Has update permission?', hasUpdatePermission);

    // Check if user has admin role or update permission for Settings
    const hasPermission = isAdmin || hasUpdatePermission;
    console.log('Has permission to add company leave?', hasPermission);

    if (!hasPermission) {
        return res.status(403).json({ message: 'Forbidden - You need admin role or update permission for Settings' });
    }

    console.log("Add Company Leave:", params, body);
    const result = await SettingService.addCompamnyLeave(params.id, body);

    return res.status(200).send(result);
}

exports.deleteCompanyLeaves = async (req, res) => {
    const { user, params } = req;

    // Check if user is authenticated
    if (!user) {
        return res.status(401).send('Authentication required');
    }

    // Log user information for debugging
    console.log('User attempting to delete company leave:', {
        id: user._id,
        name: user.name,
        role: user.role,
        permissions: user.permissions
    });

    // Check if user has admin role
    const isAdmin = user.role && user.role.includes(role.admin.id);
    console.log('Is admin?', isAdmin);

    // Check if user has update permission for Settings
    const hasUpdatePermission = user.permissions && Array.isArray(user.permissions) && user.permissions.some(p =>
        p && p.feat === 'Setting' &&
        p.acts && Array.isArray(p.acts) &&
        (p.acts.includes('update') || p.acts.includes('*'))
    );
    console.log('Has update permission?', hasUpdatePermission);

    // Check if user has admin role or update permission for Settings
    const hasPermission = isAdmin || hasUpdatePermission;
    console.log('Has permission to delete company leave?', hasPermission);

    if (!hasPermission) {
        return res.status(403).json({ message: 'Forbidden - You need admin role or update permission for Settings' });
    }

    console.log("Delete Company Leave:", params);
    const result = await SettingService.deleteCompanyLeaves(params.id);

    return res.status(200).send(result);
}