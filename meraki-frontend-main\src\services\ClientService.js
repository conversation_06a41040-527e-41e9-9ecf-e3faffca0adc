import { API_BASE_URL } from "../utils/apiConfig";

const API_URL = "http://localhost:10000/api";

async function createClient (body) {

    console.log("Create Clientt ",body)
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/client/create`, {
        method: 'POST',
         body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result
    }
}

async function updateClient (id,body) {

    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/client/update/${id}`, {
        method: 'PATCH',
         body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result
    }
}

async function deleteClient (id,body) {

    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/client/delete/${id}`, {
        method: 'DELETE',
         body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result
    }
}

async function getClient(params = {}) {
    const token = localStorage.getItem("merakihr-token");

    // Convert params object into query string
    const queryString = new URLSearchParams(params).toString();

    try {
        console.log("Get CLIENT API called");

        const response = await fetch(`${API_URL}/client?${queryString}`, {
            method: 'GET',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP Error! Status: ${response.status}`);
        }

        return await response.json(); // Convert response to JSON
    } catch (error) {
        console.error("Error fetching client:", error);
        return null; // Handle error properly
    }
}



export const ClientService = {
    createClient,
    updateClient,
    getClient,
    deleteClient
}
