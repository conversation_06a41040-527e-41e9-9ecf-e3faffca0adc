'use strict';

const mongoose = require("mongoose");

/**
 * @desc Task Schema - Defines the structure of tasks within a product.
 */
const taskSchema = new mongoose.Schema({
    taskTitle: {
        type: String,
        default: "",
        trim: true
    },
    assignee: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: "User",
        default: []
      },
    reporter: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User"
    },
    totalHours: {
        type: Number,
        default: 0
    },
    totalSpent: {
        type: Number,
        default: 0
    },
    priority: {
        type: String,
        enum: ["Low", "Medium", "High", "Critical"], // Add "Critical" here
        required: true,
      },      
    description: {
        type: String,
        trim: true
    },
    taskType: {
        type: String,
        enum: ["Normal", "Bug", "Feature"], // Example task types
        default: "Normal"
    },
    billingStatus: {
        type: String,
        enum: ["Billable", "Non-Billable"], // Ensuring valid values
        default: "Non-Billable"
    },
    startDate: {
        type: Date,
        default: null
    },
    endDate: {
        type: Date,
        default: null
    },
    taskStatus: {
        type: String,
        enum: ["To Do", "In Progress", "Completed","Pause"], // Example statuses
        default: "To Do"
    },
    taskTags: {
        type: String,
        default: "Development",
        trim: true
    },
    startTime: {
        type: Date,
        default: null
    },
    endTime: {
        type: Date,
        default: null
    },
    lastPauseTime: {
        type: Date,
        default: null
    },
    totalPausedTime: {
        type: Number,
        default: 0  // Total time in seconds that the task was paused
    },
    pauseTimes: [{
        startTime: Date,
        pauseTime: Date,
        elapsedSeconds: Number
    }],
    hours: {
        type: Map,
        of: Number,
        default: {} // Stores hours spent per day
    },
    sprintId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Sprint",
        default: null
    },
    addToSprint: {
        type: Boolean,
        default: false
    }
}, { timestamps: true });

/**
 * @desc Product Schema - Defines the structure of a product.
 */
const productSchema = new mongoose.Schema({
    productName: {
        type: String,
        required: true,
        trim: true
    },
    taskArr: {
        type: [taskSchema] // Embedding task schema as an array
    },
    description: {
        type: String,
        default: "",
        trim: true
    },
    client: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Client"
    },
    members: {
        type: [mongoose.Schema.Types.ObjectId],
        ref: "User"
    },
    reporter: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        default: null
    },
    priority: {
        type: String,
        enum: ["Low", "Medium", "High", "Critical"], // Add "Critical" here
        default: "Low"
    },    
    startDate: {
        type: Date,
        default: null
    },
    endDate: {
        type: Date,
        default: null
    },
    taskTag: {
        type: [String],
        default: []
    },
    billingStatus: {
        type: String,
        enum: ["Billable", "Non-Billable"], // Ensuring valid values
        default: "Non-Billable"
    },
    spentTime: {
        type: Number,
        default: 0 // Total time spent on the project
    },
    visibility: {
        type: Boolean,
        default: false
    },
    estimatedHours: {
        type: Number,
        default: 0
    },
    totalHours: {
        type: Number,
        default: 0
    },
    status: {
        type: String,
        enum: ["Ongoing", "Completed", "On Hold"], // Valid statuses
        default: "Ongoing"
    },
sprintIds: [{
  type: mongoose.Schema.Types.ObjectId,
  ref: "Sprint"
}]
,
    addToSprint: {
        type: Boolean,
        default: false
    }
}, { timestamps: true });

const Product = mongoose.model("Product", productSchema);

module.exports = Product;