{"ast": null, "code": "export const SCHEDULE_TEMPLATES = [{\n  value: 'day_shift',\n  label: 'Day Shift'\n}, {\n  value: 'night_shift',\n  label: 'Night Shift'\n}, {\n  value: 'flexible',\n  label: 'Flexible Hours'\n}, {\n  value: 'part_time',\n  label: 'Part Time'\n}];\nexport const SCHEDULE_TYPES = [{\n  value: 'default',\n  label: 'Default Schedule',\n  priority: 1\n}, {\n  value: 'weekly',\n  label: 'Weekly Schedule',\n  priority: 2\n}, {\n  value: 'daily',\n  label: 'Daily Schedule',\n  priority: 3\n}, {\n  value: 'time_specific',\n  label: 'Time-Specific Schedule',\n  priority: 4\n}];\nexport const DEFAULT_WORK_SCHEDULE = {\n  scheduleTemplate: 'day_shift',\n  shiftStart: new Date().toISOString().split('T')[0],\n  // Current date\n  shiftEnd: new Date().toISOString().split('T')[0],\n  // Current date\n  startTime: '09:00',\n  endTime: '17:30',\n  minimumHours: 8.30\n};\nexport const TIME_OPTIONS = [];\nfor (let hour = 0; hour < 24; hour++) {\n  for (let minute = 0; minute < 60; minute += 30) {\n    const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n    TIME_OPTIONS.push({\n      value: timeString,\n      label: timeString\n    });\n  }\n}", "map": {"version": 3, "names": ["SCHEDULE_TEMPLATES", "value", "label", "SCHEDULE_TYPES", "priority", "DEFAULT_WORK_SCHEDULE", "scheduleTemplate", "shiftStart", "Date", "toISOString", "split", "shiftEnd", "startTime", "endTime", "minimumHours", "TIME_OPTIONS", "hour", "minute", "timeString", "toString", "padStart", "push"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/constants/workSchedule.js"], "sourcesContent": ["export const SCHEDULE_TEMPLATES = [\n    { value: 'day_shift', label: 'Day Shift' },\n    { value: 'night_shift', label: 'Night Shift' },\n    { value: 'flexible', label: 'Flexible Hours' },\n    { value: 'part_time', label: 'Part Time' }\n];\n\nexport const SCHEDULE_TYPES = [\n    { value: 'default', label: 'Default Schedule', priority: 1 },\n    { value: 'weekly', label: 'Weekly Schedule', priority: 2 },\n    { value: 'daily', label: 'Daily Schedule', priority: 3 },\n    { value: 'time_specific', label: 'Time-Specific Schedule', priority: 4 }\n];\n\nexport const DEFAULT_WORK_SCHEDULE = {\n    scheduleTemplate: 'day_shift',\n    shiftStart: new Date().toISOString().split('T')[0], // Current date\n    shiftEnd: new Date().toISOString().split('T')[0],   // Current date\n    startTime: '09:00',\n    endTime: '17:30',\n    minimumHours: 8.30\n};\n\nexport const TIME_OPTIONS = [];\nfor (let hour = 0; hour < 24; hour++) {\n    for (let minute = 0; minute < 60; minute += 30) {\n        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;\n        TIME_OPTIONS.push({\n            value: timeString,\n            label: timeString\n        });\n    }\n}\n"], "mappings": "AAAA,OAAO,MAAMA,kBAAkB,GAAG,CAC9B;EAAEC,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAY,CAAC,EAC1C;EAAED,KAAK,EAAE,aAAa;EAAEC,KAAK,EAAE;AAAc,CAAC,EAC9C;EAAED,KAAK,EAAE,UAAU;EAAEC,KAAK,EAAE;AAAiB,CAAC,EAC9C;EAAED,KAAK,EAAE,WAAW;EAAEC,KAAK,EAAE;AAAY,CAAC,CAC7C;AAED,OAAO,MAAMC,cAAc,GAAG,CAC1B;EAAEF,KAAK,EAAE,SAAS;EAAEC,KAAK,EAAE,kBAAkB;EAAEE,QAAQ,EAAE;AAAE,CAAC,EAC5D;EAAEH,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE,iBAAiB;EAAEE,QAAQ,EAAE;AAAE,CAAC,EAC1D;EAAEH,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE,gBAAgB;EAAEE,QAAQ,EAAE;AAAE,CAAC,EACxD;EAAEH,KAAK,EAAE,eAAe;EAAEC,KAAK,EAAE,wBAAwB;EAAEE,QAAQ,EAAE;AAAE,CAAC,CAC3E;AAED,OAAO,MAAMC,qBAAqB,GAAG;EACjCC,gBAAgB,EAAE,WAAW;EAC7BC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAAE;EACpDC,QAAQ,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAAI;EACpDE,SAAS,EAAE,OAAO;EAClBC,OAAO,EAAE,OAAO;EAChBC,YAAY,EAAE;AAClB,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,EAAE;AAC9B,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,EAAE,EAAEA,IAAI,EAAE,EAAE;EAClC,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG,EAAE,EAAEA,MAAM,IAAI,EAAE,EAAE;IAC5C,MAAMC,UAAU,GAAG,GAAGF,IAAI,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIH,MAAM,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;IAC9FL,YAAY,CAACM,IAAI,CAAC;MACdpB,KAAK,EAAEiB,UAAU;MACjBhB,KAAK,EAAEgB;IACX,CAAC,CAAC;EACN;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}