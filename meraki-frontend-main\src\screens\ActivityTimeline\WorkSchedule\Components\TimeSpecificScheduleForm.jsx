import React, { useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  MenuItem,
  Alert
} from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import Input from 'components/Input';
import SelectField from 'components/SelectField';
import { UserActions, GeneralActions } from 'slices/actions';
import { GeneralSelector } from 'selectors';
import { SCHEDULE_TEMPLATES, TIME_OPTIONS } from 'constants/workSchedule';
import WorkScheduleUtils from 'utils/workScheduleUtils';

const TimeSpecificScheduleForm = ({ open, onClose, selectedUser, selectedDate, selectedHour }) => {
  const dispatch = useDispatch();
  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));

  useEffect(() => {
    if (success) {
      toast.success(`Time-specific schedule created successfully!`, {
        position: "top-right",
        autoClose: 3000,
        closeOnClick: true,
      });
      onClose();
      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));
    }
  }, [success, onClose, dispatch]);

  // Calculate default end time (1 hour after start)
  const getDefaultEndTime = (startTime) => {
    if (!startTime) { return '10:00' }
    const [hours, minutes] = startTime.split(':').map(Number);
    const endHour = hours + 1;
    if (endHour >= 24) { return '23:59' }
    return `${endHour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  const formik = useFormik({
    initialValues: {
      scheduleTemplate: 'day_shift',
      startTime: selectedHour || '09:00',
      endTime: getDefaultEndTime(selectedHour),
      minimumHours: 1,
      description: `Time-specific schedule for ${selectedHour || 'selected time'}`
    },
    onSubmit: (values) => handleSubmit(values)
  });

  const handleSubmit = (values) => {
    if (!selectedUser?._id) {
      toast.error('User information is missing');
      return;
    }

    // Create time-specific schedule entry
    const scheduleEntry = {
      id: `schedule_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      userId: selectedUser._id,
      type: 'time_specific',
      priority: 4,
      scheduleTemplate: values.scheduleTemplate,
      startTime: values.startTime,
      endTime: values.endTime,
      minimumHours: parseFloat(values.minimumHours),
      effectiveFrom: selectedDate,
      effectiveTo: selectedDate,
      specificDate: selectedDate,
      description: values.description,
      createdAt: new Date().toISOString(),
      isActive: true
    };

    // Add to user's workSchedules array
    const updatedWorkSchedules = [...(selectedUser.workSchedules || []), scheduleEntry];

    const params = {
      id: selectedUser._id,
      workSchedules: updatedWorkSchedules
    };

    dispatch(UserActions.updateUser(params));
  };

  const handleFieldChange = (field, value) => {
    formik.setFieldValue(field, value);
    
    // Auto-calculate hours
    if (field === 'startTime' || field === 'endTime') {
      const startTime = field === 'startTime' ? value : formik.values.startTime;
      const endTime = field === 'endTime' ? value : formik.values.endTime;
      
      if (startTime && endTime) {
        const calculatedHours = WorkScheduleUtils.calculateHours(startTime, endTime);
        formik.setFieldValue('minimumHours', calculatedHours);
      }
    }
    
    // Auto-detect night shift
    if (field === 'startTime') {
      const hour = parseInt(value.split(':')[0], 10);
      if (hour >= 22 || hour < 6) {
        formik.setFieldValue('scheduleTemplate', 'night_shift');
      } else {
        formik.setFieldValue('scheduleTemplate', 'day_shift');
      }
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Typography variant="h6">
          Time-Specific Schedule - {selectedUser?.name}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {selectedDate} at {selectedHour}
        </Typography>
        <Typography variant="caption" color="error.main" sx={{ display: 'block', mt: 1 }}>
          Highest priority - overrides all other schedules for this time slot
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          {/* Schedule Template */}
          <SelectField
            label="Schedule Template"
            name="scheduleTemplate"
            value={formik.values.scheduleTemplate}
            onChange={(e) => handleFieldChange('scheduleTemplate', e.target.value)}
            required
          >
            {SCHEDULE_TEMPLATES.map((template) => (
              <MenuItem key={template.value} value={template.value}>
                {template.label}
              </MenuItem>
            ))}
          </SelectField>

          {/* Time Range */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <SelectField
              label="Start Time"
              name="startTime"
              value={formik.values.startTime}
              onChange={(e) => handleFieldChange('startTime', e.target.value)}
              required
              sx={{ flex: 1 }}
            >
              {TIME_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </SelectField>
            <SelectField
              label="End Time"
              name="endTime"
              value={formik.values.endTime}
              onChange={(e) => handleFieldChange('endTime', e.target.value)}
              required
              sx={{ flex: 1 }}
            >
              {TIME_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </SelectField>
          </Box>

          {/* Minimum Hours */}
          <Input
            label="Duration (Hours)"
            name="minimumHours"
            type="number"
            step="0.1"
            value={formik.values.minimumHours}
            onChange={(e) => handleFieldChange('minimumHours', e.target.value)}
            required
            helperText={
              formik.values.startTime && formik.values.endTime ? `Calculated: ${WorkScheduleUtils.calculateHours(formik.values.startTime, formik.values.endTime)} hours` : 'Auto-calculated from time range'
            }
          />

          {/* Description */}
          <Input
            label="Description"
            name="description"
            multiline
            rows={2}
            value={formik.values.description}
            onChange={(e) => handleFieldChange('description', e.target.value)}
            placeholder="e.g., Client meeting, Important call, Special task..."
          />

          {/* Info Alert */}
          <Alert severity="warning">
            <Typography variant="body2">
              <strong>Time-Specific Schedule (Priority 4):</strong> This schedule will only apply on {selectedDate} from {formik.values.startTime} to {formik.values.endTime}. It will override any other schedules during this exact time period.
            </Typography>
          </Alert>

          {/* Usage Examples */}
          <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              <strong>Common uses:</strong> Client meetings, important calls, special tasks, appointments, training sessions
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Cancel
        </Button>
        <Button 
          onClick={formik.handleSubmit} 
          variant="contained" 
          color="primary"
        >
          Create Time Schedule
        </Button>
      </DialogActions>
    </Dialog>
  );
};

TimeSpecificScheduleForm.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedUser: PropTypes.object,
  selectedDate: PropTypes.string,
  selectedHour: PropTypes.string
};

export default TimeSpecificScheduleForm;
