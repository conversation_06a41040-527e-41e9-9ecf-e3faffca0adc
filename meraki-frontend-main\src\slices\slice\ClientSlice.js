import { createSlice } from "@reduxjs/toolkit";

export const ClientSlice = createSlice({
    name: "client",
    initialState: {
        clients:[],
        pagination:{}
    },
    reducers: {
        getClients: () => {
            
        },
        createClient: () => {
 
        },
        getSuccessfullyClients: (state, action) => {
            console.log("CLient Payload ",action.payload.data)
            if(action.payload.data === 0) {

                state.clients = [];
                state.pagination = {};
            }else {

                state.clients = action.payload.data;
                state.pagination = action.payload.pagination;
            }

        },
        deleteClient: () => {},
        updateClient: () => {}
    }
});

export default ClientSlice;