/**
 * Utility functions for logging and debugging permissions
 */

/**
 * Logs all permissions for a user
 * @param {Object} user - The user object with permissions
 */
export const logUserPermissions = (user) => {
  if (!user) {
    console.log('Permission Log: No user provided');
    return;
  }

  console.group('User Permission Log');
  console.log('User:', user.name);
  console.log('User ID:', user._id);
  console.log('Roles:', user.role || []);

  if (user.permissions && Array.isArray(user.permissions)) {
    console.log('Permissions Count:', user.permissions.length);

    // Group permissions by feature
    const permissionsByFeature = {};
    user.permissions.forEach(p => {
      permissionsByFeature[p.feat] = p.acts;
    });

    console.table(permissionsByFeature);

    // Log detailed permissions
    console.group('Detailed Permissions');
    user.permissions.forEach(p => {
      console.log(`Feature: ${p.feat}`);
      console.log(`Actions: ${p.acts.join(', ')}`);
      console.log('---');
    });
    console.groupEnd();
  } else {
    console.log('No permissions array found on user');
  }

  console.groupEnd();
};

/**
 * Logs all permission checks for a specific route
 * @param {string} path - The route path
 * @param {Object} permission - The permission object with feat and act properties
 * @param {boolean} hasPermission - Whether the user has the permission
 * @param {string} reason - The reason for the permission decision
 */
export const logRoutePermission = (path, permission, hasPermission, reason = '') => {
  console.group(`Route Permission Check: ${path}`);
  console.log(`Feature: ${permission?.feat || 'None'}`);
  console.log(`Action: ${permission?.act || 'None'}`);
  console.log(`Result: ${hasPermission ? 'GRANTED' : 'DENIED'}`);
  if (reason) {
    console.log(`Reason: ${reason}`);
  }
  console.groupEnd();
};

export default {
  logUserPermissions,
  logRoutePermission
};
